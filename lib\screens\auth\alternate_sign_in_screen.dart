import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/gestures.dart';
import '../../models/auth_state.dart';
import '../../providers/auth_provider.dart';
import '../../utils/responsive_helper.dart';
import '../../services/navigation_service.dart';

class AlternateSignInScreen extends ConsumerStatefulWidget {
  const AlternateSignInScreen({super.key});

  @override
  ConsumerState<AlternateSignInScreen> createState() => _AlternateSignInScreenState();
}

class _AlternateSignInScreenState extends ConsumerState<AlternateSignInScreen> {
  final _phoneController = TextEditingController();
  String _phoneNumber = '';
  String? _phoneError;
  bool _isGoogleSignInLoading = false;
  bool _isSendingOtp = false;

  @override
  void initState() {
    super.initState();
    _phoneController.addListener(_onPhoneNumberChanged);
  }

  @override
  void dispose() {
    _phoneController.removeListener(_onPhoneNumberChanged);
    _phoneController.dispose();
    super.dispose();
  }

  void _clearMobileNumber() {
    setState(() {
      _phoneController.clear();
      _phoneNumber = '';
      _phoneError = null;
    });

    // Remove focus from the mobile number field
    FocusScope.of(context).unfocus();
  }

  void _onPhoneNumberChanged() {
    final text = _phoneController.text;
    final cleanNumber = text.replaceAll(' ', '');

    // Format the number with space after 5 digits
    String formattedNumber = _formatPhoneNumber(cleanNumber);

    // Update controller if formatting changed
    if (formattedNumber != text) {
      final cursorPosition = _calculateCursorPosition(text, formattedNumber, _phoneController.selection.baseOffset);
      _phoneController.value = TextEditingValue(
        text: formattedNumber,
        selection: TextSelection.collapsed(offset: cursorPosition),
      );
    }

    // Update phone number and clear any previous errors
    setState(() {
      _phoneNumber = cleanNumber;
      // Clear any previous error when user starts typing
      if (_phoneError != null) {
        _phoneError = null;
      }
    });
  }

  String _formatPhoneNumber(String number) {
    // Remove any existing spaces
    String clean = number.replaceAll(' ', '');

    // Limit to 10 digits
    if (clean.length > 10) {
      clean = clean.substring(0, 10);
    }

    // Add space after 5 digits
    if (clean.length > 5) {
      return '${clean.substring(0, 5)} ${clean.substring(5)}';
    }

    return clean;
  }

  int _calculateCursorPosition(String oldText, String newText, int oldPosition) {
    // Handle cursor position when space is added/removed
    if (newText.length > oldText.length) {
      // Space was added
      if (oldPosition > 5) {
        return oldPosition + 1;
      }
    } else if (newText.length < oldText.length) {
      // Space was removed
      if (oldPosition > 6) {
        return oldPosition - 1;
      }
    }
    return oldPosition.clamp(0, newText.length);
  }

  String? _validatePhoneNumber(String number) {
    if (number.isEmpty) {
      return 'Please enter mobile number';
    }

    if (number.length < 10) {
      return 'Mobile number must be 10 digits';
    }

    if (number.length > 10) {
      return 'Mobile number cannot exceed 10 digits';
    }

    // Check if it's all numbers
    if (!RegExp(r'^[0-9]+$').hasMatch(number)) {
      return 'Only numbers are allowed';
    }

    // Check if it starts with valid Indian mobile digits (6, 7, 8, 9)
    if (!RegExp(r'^[6-9]').hasMatch(number)) {
      return 'Mobile number must start with 6, 7, 8, or 9';
    }

    // Check for consecutive same digits (like 1111111111)
    if (RegExp(r'^(\d)\1{9}$').hasMatch(number)) {
      return 'Invalid mobile number format';
    }

    // Check for common invalid patterns
    if (number == '0000000000' || number == '1234567890' || number == '9876543210') {
      return 'Please enter a valid mobile number';
    }

    return null; // Valid number
  }

  void _onSendOtpPressed() async {
    final validationError = _validatePhoneNumber(_phoneNumber);

    if (validationError != null) {
      // Show error below the field instead of toast
      setState(() {
        _phoneError = validationError;
      });
      return;
    }

    // Clear any previous errors and start loading
    setState(() {
      _phoneError = null;
      _isSendingOtp = true;
    });

    // Format phone number for Firebase (+91 prefix)
    final formattedPhoneNumber = '+91$_phoneNumber';

    // Call Firebase phone verification with proper error handling
    try {
      ref.read(authProvider.notifier).verifyPhoneNumber(
        phoneNumber: formattedPhoneNumber,
        onCodeSent: (verificationId) {
          // Stop loading and navigate
          setState(() {
            _isSendingOtp = false;
          });

          // Use post frame callback to avoid blocking main thread
          WidgetsBinding.instance.addPostFrameCallback((_) {
            NavigationService.instance.navigateToOtp(
              phoneNumber: '+91 ${_phoneController.text}',
              verificationId: verificationId,
            );

            // Clear mobile number when navigating to OTP screen for security
            _clearMobileNumber();
          });
        },
        onAutoVerified: (user) {
          // Stop loading and navigate
          setState(() {
            _isSendingOtp = false;
          });

          // Auto-verification successful (Android only)
          WidgetsBinding.instance.addPostFrameCallback((_) {
            NavigationService.instance.navigateToHome();
            _clearMobileNumber();
          });
        },
      );
    } catch (e) {
      // Handle any immediate errors
      setState(() {
        _isSendingOtp = false;
        _phoneError = 'Failed to send OTP. Please try again.';
      });
    }
  }

  void _onGoogleSignInPressed() async {
    setState(() {
      _isGoogleSignInLoading = true;
    });

    try {
      await ref.read(authProvider.notifier).signInWithGoogle();
    } finally {
      if (mounted) {
        setState(() {
          _isGoogleSignInLoading = false;
        });
      }
    }
  }

  void _onAppleSignInPressed() async {
    try {
      await ref.read(authProvider.notifier).signInWithApple();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple Sign In failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onEmailSignInPressed() {
    NavigationService.instance.navigateToAlternateLogin();
  }

  void _onSignUpPressed() {
    NavigationService.instance.navigateToAlternateRegister();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);
    final horizontalPadding = ResponsiveHelper.getHorizontalPadding(context);
    final maxContentWidth = ResponsiveHelper.getMaxContentWidth(context);

    // Listen to auth state changes
    ref.listen<AuthState>(authProvider, (previous, next) {
      // Handle authentication success
      if (previous?.status != AuthStatus.authenticated &&
          next.status == AuthStatus.authenticated) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          NavigationService.instance.navigateToHome();
        });
      }

      // Handle auth errors (including phone auth billing errors)
      if (next.status == AuthStatus.error && next.errorMessage != null) {
        setState(() {
          _isSendingOtp = false;
          _phoneError = next.errorMessage;
        });
      }
    });

    return PopScope(
      canPop: false, // Prevent default back button behavior
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // Check if we can go back in the navigation stack
        if (NavigationService.instance.canGoBack()) {
          NavigationService.instance.goBack();
        } else {
          // No previous screen, exit the app
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Center(
          child: Container(
            constraints: BoxConstraints(maxWidth: maxContentWidth),
            child: SingleChildScrollView(
              padding: horizontalPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(height: 40),

                // Title and Subtitle
                _buildTitleSection(theme),

                    const SizedBox(height: 32),

                    // Phone Number Description
                    _buildPhoneNumberDescription(theme),

                    const SizedBox(height: 8),

                    // Phone Number Input
                    _buildPhoneNumberInput(theme),

                    // Error message
                    if (_phoneError != null)
                      Padding(
                        padding: const EdgeInsets.only(left: 4, top: 8),
                        child: Text(
                          _phoneError!,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                          ),
                        ),
                      ),

                    SizedBox(height: _phoneError != null ? 16 : 24),

                    // Send OTP Button
                    _buildSendOtpButton(authState),

                    const SizedBox(height: 32),

                    // OR Divider
                    _buildOrDivider(theme),

                    const SizedBox(height: 32),

                    // Social Auth Buttons
                    _buildSocialAuthButtons(authState),

                    const SizedBox(height: 32),

                // Sign Up Link
                _buildSignUpLink(theme),
                ],
              ),
            ),
          ),
        ),
        ), // Close SafeArea
      ),
    ); // Close PopScope
  }



  Widget _buildTitleSection(ThemeData theme) {
    return Column(
      children: [
        const Text(
          'Welcome Back to',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Color(0xFF1a1a1a),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'All About Insurance',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Color(0xFFe92933),
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneNumberInput(ThemeData theme) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFd1d5db)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Phone icon and country code
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(
                  Icons.phone_iphone,
                  color: const Color(0xFF666666),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '+91',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: const Color(0xFF1a1a1a),
                    fontWeight: FontWeight.w500,
                    fontSize: 18, // Bigger font for country code
                  ),
                ),
              ],
            ),
          ),
          // Text input
          Expanded(
            child: TextField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9\s]')),
                LengthLimitingTextInputFormatter(11), // 10 digits + 1 space
              ],
              style: theme.textTheme.bodyLarge?.copyWith(
                color: const Color(0xFF1a1a1a),
                fontSize: 18, // Bigger font for input numbers
                fontWeight: FontWeight.w500,
                letterSpacing: 2.0, // Add spacing between characters
              ),
              decoration: InputDecoration(
                hintText: 'xxxxx xxxxx',
                hintStyle: TextStyle(
                  color: Color(0xFF666666),
                  fontSize: 16, // Slightly smaller for hint text
                  letterSpacing: 2.0, // Match input text spacing
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneNumberDescription(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(left: 4),
      child: Text(
        'Enter Mobile Number to Start',
        style: theme.textTheme.bodySmall?.copyWith(
          color: const Color(0xFF666666),
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildSendOtpButton(AuthState authState) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isSendingOtp ? () {} : _onSendOtpPressed, // Keep button enabled but non-functional when loading
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFe92933), // Always keep red color
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_isSendingOtp) ...[
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
            ],
            const Text(
              'Send OTP',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrDivider(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            color: const Color(0xFFe5e7eb),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'OR',
            style: theme.textTheme.bodySmall?.copyWith(
              color: const Color(0xFF666666),
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            color: const Color(0xFFe5e7eb),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialAuthButtons(AuthState authState) {
    return Column(
      children: [
        // Google Button
        _buildSocialButton(
          text: 'Continue with Google',
          iconWidget: SizedBox(
            width: 20,
            height: 20,
            child: Image.asset(
              'assets/logo/google-logo.webp',
              width: 20,
              height: 20,
              fit: BoxFit.contain,
            ),
          ),
          onPressed: _onGoogleSignInPressed,
          isLoading: _isGoogleSignInLoading,
        ),

        const SizedBox(height: 16),

        // Apple Button
        _buildSocialButton(
          text: 'Continue with Apple',
          iconWidget: const Icon(
            Icons.apple,
            color: Color(0xFF1a1a1a),
            size: 20,
          ),
          onPressed: _onAppleSignInPressed,
          isLoading: false,
        ),

        const SizedBox(height: 16),

        // Email Button
        _buildSocialButton(
          text: 'Continue with Email',
          iconWidget: const Icon(
            Icons.email_outlined,
            color: Color(0xFF1a1a1a),
            size: 20,
          ),
          onPressed: _onEmailSignInPressed,
          isLoading: false,
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required String text,
    required Widget iconWidget,
    required VoidCallback onPressed,
    required bool isLoading,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFf2f2f2),
          foregroundColor: const Color(0xFF1a1a1a),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconWidget,
            const SizedBox(width: 12),
            Stack(
              alignment: Alignment.center,
              children: [
                Text(
                  text,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1a1a1a),
                  ),
                ),
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFe92933)),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSignUpLink(ThemeData theme) {
    return Center(
      child: RichText(
        text: TextSpan(
          style: theme.textTheme.bodyMedium?.copyWith(
            color: const Color(0xFF1a1a1a),
          ),
          children: [
            const TextSpan(text: 'New to All About Insurance? '),
            TextSpan(
              text: 'Sign Up',
              style: const TextStyle(
                color: Color(0xFFe92933),
                fontWeight: FontWeight.w600,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = _onSignUpPressed,
            ),
          ],
        ),
      ),
    );
  }


}
