import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:aai/utils/auth_provider_mapper.dart';

// Generate mocks
@GenerateMocks([firebase_auth.User, firebase_auth.UserInfo])
import 'auth_provider_mapper_test.mocks.dart';

void main() {
  group('AuthProviderMapper', () {
    late AuthProviderMapper mapper;
    late MockUser mockUser;
    late MockUserInfo mockUserInfo;

    setUp(() {
      mapper = AuthProviderMapper.instance;
      mockUser = MockUser();
      mockUserInfo = MockUserInfo();
    });

    group('Singleton Pattern', () {
      test('should return same instance', () {
        final instance1 = AuthProviderMapper.instance;
        final instance2 = AuthProviderMapper.instance;
        expect(identical(instance1, instance2), true);
      });
    });

    group('mapProviderIdToAppProvider', () {
      test('should map google.com to google', () {
        expect(mapper.mapProviderIdToAppProvider('google.com'), 'google');
      });

      test('should map apple.com to apple', () {
        expect(mapper.mapProviderIdToAppProvider('apple.com'), 'apple');
      });

      test('should map phone to phone', () {
        expect(mapper.mapProviderIdToAppProvider('phone'), 'phone');
      });

      test('should map password to email', () {
        expect(mapper.mapProviderIdToAppProvider('password'), 'email');
      });

      test('should map firebase to email', () {
        expect(mapper.mapProviderIdToAppProvider('firebase'), 'email');
      });

      test('should map facebook.com to facebook', () {
        expect(mapper.mapProviderIdToAppProvider('facebook.com'), 'facebook');
      });

      test('should map unknown provider to email', () {
        expect(mapper.mapProviderIdToAppProvider('unknown.provider'), 'email');
      });
    });

    group('mapFirebaseProviderToAppProvider', () {
      test('should return google for Google sign-in user', () {
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.email).thenReturn('<EMAIL>');

        final result = mapper.mapFirebaseProviderToAppProvider(mockUser);
        expect(result, 'google');
      });

      test('should return email for email/password user', () {
        when(mockUserInfo.providerId).thenReturn('password');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.email).thenReturn('<EMAIL>');

        final result = mapper.mapFirebaseProviderToAppProvider(mockUser);
        expect(result, 'email');
      });

      test('should return phone for phone auth user', () {
        when(mockUserInfo.providerId).thenReturn('phone');
        when(mockUser.providerData).thenReturn([mockUserInfo]);

        final result = mapper.mapFirebaseProviderToAppProvider(mockUser);
        expect(result, 'phone');
      });

      test('should return email when no provider data but has email', () {
        when(mockUser.providerData).thenReturn([]);
        when(mockUser.email).thenReturn('<EMAIL>');

        final result = mapper.mapFirebaseProviderToAppProvider(mockUser);
        expect(result, 'email');
      });

      test('should return unknown when no provider data and no email', () {
        when(mockUser.providerData).thenReturn([]);
        when(mockUser.email).thenReturn(null);

        final result = mapper.mapFirebaseProviderToAppProvider(mockUser);
        expect(result, 'unknown');
      });
    });

    group('getAllProvidersForUser', () {
      test('should return all unique providers', () {
        final mockUserInfo1 = MockUserInfo();
        final mockUserInfo2 = MockUserInfo();
        
        when(mockUserInfo1.providerId).thenReturn('google.com');
        when(mockUserInfo2.providerId).thenReturn('password');
        when(mockUser.providerData).thenReturn([mockUserInfo1, mockUserInfo2]);

        final result = mapper.getAllProvidersForUser(mockUser);
        expect(result, containsAll(['google', 'email']));
        expect(result.length, 2);
      });

      test('should remove duplicates', () {
        final mockUserInfo1 = MockUserInfo();
        final mockUserInfo2 = MockUserInfo();
        
        when(mockUserInfo1.providerId).thenReturn('password');
        when(mockUserInfo2.providerId).thenReturn('firebase');
        when(mockUser.providerData).thenReturn([mockUserInfo1, mockUserInfo2]);

        final result = mapper.getAllProvidersForUser(mockUser);
        expect(result, ['email']);
        expect(result.length, 1);
      });
    });

    group('userSignedInWithProvider', () {
      test('should return true when user has the provider', () {
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);

        final result = mapper.userSignedInWithProvider(mockUser, 'google');
        expect(result, true);
      });

      test('should return false when user does not have the provider', () {
        when(mockUserInfo.providerId).thenReturn('password');
        when(mockUser.providerData).thenReturn([mockUserInfo]);

        final result = mapper.userSignedInWithProvider(mockUser, 'google');
        expect(result, false);
      });
    });

    group('getProviderDisplayName', () {
      test('should return correct display names', () {
        expect(mapper.getProviderDisplayName('google'), 'Google');
        expect(mapper.getProviderDisplayName('apple'), 'Apple');
        expect(mapper.getProviderDisplayName('phone'), 'Phone');
        expect(mapper.getProviderDisplayName('email'), 'Email');
        expect(mapper.getProviderDisplayName('unknown'), 'Unknown');
      });
    });

    group('getProviderIconName', () {
      test('should return correct icon names', () {
        expect(mapper.getProviderIconName('google'), 'google_icon');
        expect(mapper.getProviderIconName('apple'), 'apple_icon');
        expect(mapper.getProviderIconName('phone'), 'phone_icon');
        expect(mapper.getProviderIconName('email'), 'email_icon');
        expect(mapper.getProviderIconName('unknown'), 'default_icon');
      });
    });

    group('isProviderSupported', () {
      test('should return true for supported providers', () {
        expect(mapper.isProviderSupported('google'), true);
        expect(mapper.isProviderSupported('apple'), true);
        expect(mapper.isProviderSupported('email'), true);
        expect(mapper.isProviderSupported('phone'), true);
      });

      test('should return false for unsupported providers', () {
        expect(mapper.isProviderSupported('unknown'), false);
        expect(mapper.isProviderSupported('custom'), false);
      });
    });

    group('getProviderPriority', () {
      test('should return correct priorities', () {
        expect(mapper.getProviderPriority('google'), 1);
        expect(mapper.getProviderPriority('apple'), 2);
        expect(mapper.getProviderPriority('email'), 3);
        expect(mapper.getProviderPriority('phone'), 4);
        expect(mapper.getProviderPriority('unknown'), 99);
      });
    });

    group('sortProvidersByPriority', () {
      test('should sort providers correctly', () {
        final providers = ['phone', 'google', 'email', 'apple'];
        final sorted = mapper.sortProvidersByPriority(providers);
        expect(sorted, ['google', 'apple', 'email', 'phone']);
      });
    });

    group('extractProviderSpecificData', () {
      test('should extract Google-specific data', () {
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.photoURL).thenReturn('https://lh3.googleusercontent.com/photo.jpg');
        when(mockUser.displayName).thenReturn('John Doe');

        final result = mapper.extractProviderSpecificData(mockUser);
        
        expect(result['primaryProvider'], 'google');
        expect(result['googleSpecific']['hasGooglePhoto'], true);
        expect(result['googleSpecific']['googleDisplayName'], 'John Doe');
      });

      test('should extract Apple-specific data', () {
        when(mockUserInfo.providerId).thenReturn('apple.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.displayName).thenReturn('Jane Doe');

        final result = mapper.extractProviderSpecificData(mockUser);
        
        expect(result['primaryProvider'], 'apple');
        expect(result['appleSpecific']['isPrivateEmail'], true);
        expect(result['appleSpecific']['appleDisplayName'], 'Jane Doe');
      });

      test('should extract phone-specific data', () {
        when(mockUserInfo.providerId).thenReturn('phone');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.phoneNumber).thenReturn('+**********');

        final result = mapper.extractProviderSpecificData(mockUser);
        
        expect(result['primaryProvider'], 'phone');
        expect(result['phoneSpecific']['phoneNumber'], '+**********');
        expect(result['phoneSpecific']['isPhoneVerified'], true);
      });

      test('should extract email-specific data', () {
        when(mockUserInfo.providerId).thenReturn('password');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.emailVerified).thenReturn(true);

        final result = mapper.extractProviderSpecificData(mockUser);
        
        expect(result['primaryProvider'], 'email');
        expect(result['emailSpecific']['email'], '<EMAIL>');
        expect(result['emailSpecific']['isEmailVerified'], true);
      });
    });

    group('needsAdditionalVerification', () {
      test('should return true for unverified email user', () {
        when(mockUserInfo.providerId).thenReturn('password');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.emailVerified).thenReturn(false);

        final result = mapper.needsAdditionalVerification(mockUser);
        expect(result, true);
      });

      test('should return false for verified email user', () {
        when(mockUserInfo.providerId).thenReturn('password');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.emailVerified).thenReturn(true);

        final result = mapper.needsAdditionalVerification(mockUser);
        expect(result, false);
      });

      test('should return false for Google user', () {
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);

        final result = mapper.needsAdditionalVerification(mockUser);
        expect(result, false);
      });

      test('should return true for phone user without phone number', () {
        when(mockUserInfo.providerId).thenReturn('phone');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.phoneNumber).thenReturn(null);

        final result = mapper.needsAdditionalVerification(mockUser);
        expect(result, true);
      });
    });

    group('getRecommendedNextSteps', () {
      test('should recommend email verification for unverified email user', () {
        when(mockUserInfo.providerId).thenReturn('password');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.emailVerified).thenReturn(false);
        when(mockUser.displayName).thenReturn('John Doe');
        when(mockUser.photoURL).thenReturn('https://example.com/photo.jpg');

        final result = mapper.getRecommendedNextSteps(mockUser);
        expect(result, contains('verify_email'));
      });

      test('should recommend profile completion for user without display name', () {
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.displayName).thenReturn(null);
        when(mockUser.photoURL).thenReturn('https://example.com/photo.jpg');

        final result = mapper.getRecommendedNextSteps(mockUser);
        expect(result, contains('complete_profile'));
      });

      test('should recommend adding profile photo for user without photo', () {
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.displayName).thenReturn('John Doe');
        when(mockUser.photoURL).thenReturn(null);

        final result = mapper.getRecommendedNextSteps(mockUser);
        expect(result, contains('add_profile_photo'));
      });

      test('should return empty list for complete user', () {
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);
        when(mockUser.displayName).thenReturn('John Doe');
        when(mockUser.photoURL).thenReturn('https://example.com/photo.jpg');

        final result = mapper.getRecommendedNextSteps(mockUser);
        expect(result, isEmpty);
      });
    });
  });
}
