// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in aai/test/widgets/profile_sidebar_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:aai/models/user_profile.dart' as _i5;
import 'package:aai/services/profile_update_service.dart' as _i3;
import 'package:aai/utils/result.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResult_0<T> extends _i1.SmartFake implements _i2.Result<T> {
  _FakeResult_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ProfileUpdateService].
///
/// See the documentation for Mockito's code generation for more information.
class MockProfileUpdateService extends _i1.Mock
    implements _i3.ProfileUpdateService {
  MockProfileUpdateService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Result<_i5.ProfileUpdateResult>> updateProfile(
    _i5.ProfileUpdateRequest? request, {
    Duration? timeout = const Duration(seconds: 30),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [request], {#timeout: timeout}),
            returnValue: _i4.Future<_i2.Result<_i5.ProfileUpdateResult>>.value(
              _FakeResult_0<_i5.ProfileUpdateResult>(
                this,
                Invocation.method(
                  #updateProfile,
                  [request],
                  {#timeout: timeout},
                ),
              ),
            ),
          )
          as _i4.Future<_i2.Result<_i5.ProfileUpdateResult>>);

  @override
  _i4.Future<_i2.Result<_i5.UserProfile>> getProfile({
    Duration? timeout = const Duration(seconds: 15),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getProfile, [], {#timeout: timeout}),
            returnValue: _i4.Future<_i2.Result<_i5.UserProfile>>.value(
              _FakeResult_0<_i5.UserProfile>(
                this,
                Invocation.method(#getProfile, [], {#timeout: timeout}),
              ),
            ),
          )
          as _i4.Future<_i2.Result<_i5.UserProfile>>);

  @override
  _i4.Future<_i2.Result<void>> updateLastActive() =>
      (super.noSuchMethod(
            Invocation.method(#updateLastActive, []),
            returnValue: _i4.Future<_i2.Result<void>>.value(
              _FakeResult_0<void>(
                this,
                Invocation.method(#updateLastActive, []),
              ),
            ),
          )
          as _i4.Future<_i2.Result<void>>);

  @override
  bool isValidEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#isValidEmail, [email]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidPhoneNumber(String? phoneNumber) =>
      (super.noSuchMethod(
            Invocation.method(#isValidPhoneNumber, [phoneNumber]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidDateOfBirth(DateTime? dateOfBirth) =>
      (super.noSuchMethod(
            Invocation.method(#isValidDateOfBirth, [dateOfBirth]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidGender(String? gender) =>
      (super.noSuchMethod(
            Invocation.method(#isValidGender, [gender]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidAnnualIncome(double? income) =>
      (super.noSuchMethod(
            Invocation.method(#isValidAnnualIncome, [income]),
            returnValue: false,
          )
          as bool);
}
