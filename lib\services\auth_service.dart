import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import '../models/user_model.dart';

class AuthException implements Exception {
  final String message;
  final String code;
  
  const AuthException(this.message, this.code);
  
  @override
  String toString() => message;
}

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();
  
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  
  // Stream of authentication state changes
  Stream<AppUser?> get authStateChanges {
    return _firebaseAuth.authStateChanges().map((user) {
      return user != null ? AppUser.fromFirebaseUser(user) : null;
    });
  }

  // Stream of Firebase User for email verification detection
  Stream<User?> get firebaseAuthStateChanges {
    return _firebaseAuth.authStateChanges();
  }
  
  // Get current user
  AppUser? get currentUser {
    final user = _firebaseAuth.currentUser;
    return user != null ? AppUser.fromFirebaseUser(user) : null;
  }

  // Get current Firebase user (for email verification checking)
  User? get currentFirebaseUser {
    return _firebaseAuth.currentUser;
  }
  
  // Email/Password Authentication
  Future<AppUser> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        throw const AuthException('Sign in failed', 'sign-in-failed');
      }

      // Don't check email verification here - let Firebase auth state listener handle it
      // This allows the login to succeed and the listener to set the appropriate state
      return AppUser.fromFirebaseUser(credential.user!);
    } on FirebaseAuthException catch (e) {
      print('DEBUG: Firebase auth error - Code: ${e.code}, Message: ${e.message}');
      final errorMessage = _getErrorMessage(e.code);
      throw AuthException(errorMessage, e.code);
    } catch (e) {
      print('DEBUG: Unexpected auth error: $e');
      throw AuthException('An unexpected error occurred', 'unknown');
    }
  }
  
  Future<AppUser> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        throw const AuthException('Account creation failed', 'creation-failed');
      }

      // Update display name if provided
      if (displayName != null && displayName.isNotEmpty) {
        await credential.user!.updateDisplayName(displayName.trim());
        await credential.user!.reload();
      }

      // Send email verification
      await sendEmailVerification();

      return AppUser.fromFirebaseUser(_firebaseAuth.currentUser!);
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('An unexpected error occurred', 'unknown');
    }
  }

  // Create user account and send Firebase email verification
  Future<void> createUserAccountWithEmailVerification({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        throw const AuthException('Account creation failed', 'creation-failed');
      }

      // Update display name if provided
      if (displayName != null && displayName.isNotEmpty) {
        await credential.user!.updateDisplayName(displayName.trim());
        await credential.user!.reload();
      }

      // Send Firebase email verification
      await credential.user!.sendEmailVerification();

      // DON'T sign out - keep user signed in to Firebase so we can check verification status
      // User won't be authenticated in our app until email is verified

    } on FirebaseAuthException catch (e) {
      if (e.code == 'email-already-in-use') {
        // For existing accounts, try to resend verification email
        try {
          // Sign in temporarily to send verification email
          final credential = await _firebaseAuth.signInWithEmailAndPassword(
            email: email.trim(),
            password: password,
          );

          if (credential.user != null && !credential.user!.emailVerified) {
            // Resend verification email for existing unverified account
            await credential.user!.sendEmailVerification();
            // Keep user signed in to Firebase so we can check verification status
            // Don't throw error - let user proceed to verification screen
            return;
          } else if (credential.user != null && credential.user!.emailVerified) {
            // Account already verified - sign out and ask user to sign in
            await _firebaseAuth.signOut();
            throw AuthException('Account already exists and is verified. Please sign in.', 'account-verified');
          }
        } catch (signInError) {
          // If sign in fails, it might be wrong password
          throw AuthException('Account exists with this email. Please sign in or reset your password.', 'email-already-in-use');
        }
      }
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('An unexpected error occurred', 'unknown');
    }
  }

  // Resend Firebase email verification
  Future<void> resendEmailVerification(String email, String password) async {
    try {
      // Check if user is already signed in
      final currentUser = _firebaseAuth.currentUser;

      if (currentUser != null && currentUser.email == email.trim()) {
        // User is already signed in, just resend verification
        if (!currentUser.emailVerified) {
          await currentUser.sendEmailVerification();
        } else {
          throw const AuthException('Account is already verified', 'already-verified');
        }
      } else {
        // Sign in to resend verification
        final credential = await _firebaseAuth.signInWithEmailAndPassword(
          email: email.trim(),
          password: password,
        );

        if (credential.user != null && !credential.user!.emailVerified) {
          await credential.user!.sendEmailVerification();
          // Keep user signed in for verification checking
        } else {
          await _firebaseAuth.signOut();
          throw const AuthException('Account is already verified', 'already-verified');
        }
      }
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('Failed to resend verification email', 'resend-failed');
    }
  }

  // Authenticate user after email verification
  Future<AppUser> authenticateAfterVerification({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        throw const AuthException('Authentication failed', 'auth-failed');
      }

      return AppUser.fromFirebaseUser(credential.user!);
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('Authentication failed', 'auth-failed');
    }
  }
  
  // Google Sign In
  Future<AppUser> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        throw const AuthException('Google sign in was cancelled', 'cancelled');
      }
      
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      
      if (userCredential.user == null) {
        throw const AuthException('Google sign in failed', 'sign-in-failed');
      }
      
      return AppUser.fromFirebaseUser(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('Google sign in failed', 'google-sign-in-failed');
    }
  }

  // Apple Sign In
  Future<AppUser> signInWithApple() async {
    try {
      // Check if Apple Sign In is available
      if (!await SignInWithApple.isAvailable()) {
        throw const AuthException('Apple Sign In is not available on this device', 'apple-not-available');
      }

      // Request Apple ID credential
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create Firebase credential
      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in with Firebase
      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);

      if (userCredential.user == null) {
        throw const AuthException('Apple sign in failed', 'apple-sign-in-failed');
      }

      // Update display name if provided by Apple and not already set
      final user = userCredential.user!;
      if (user.displayName == null || user.displayName!.isEmpty) {
        final fullName = appleCredential.givenName != null && appleCredential.familyName != null
            ? '${appleCredential.givenName} ${appleCredential.familyName}'
            : null;

        if (fullName != null && fullName.isNotEmpty) {
          await user.updateDisplayName(fullName);
          await user.reload();
        }
      }

      return AppUser.fromFirebaseUser(userCredential.user!);
    } on SignInWithAppleAuthorizationException catch (e) {
      if (e.code == AuthorizationErrorCode.canceled) {
        throw const AuthException('Apple sign in was cancelled', 'apple-cancelled');
      } else {
        throw AuthException('Apple sign in failed: ${e.message}', 'apple-auth-error');
      }
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('Apple sign in failed', 'apple-failed');
    }
  }
  
  // Phone Authentication
  Future<void> verifyPhoneNumber({
    required String phoneNumber,
    required Function(String verificationId) onCodeSent,
    required Function(String error) onError,
    Function(AppUser user)? onAutoVerified,
    int? forceResendingToken,
  }) async {
    try {
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            final userCredential = await _firebaseAuth.signInWithCredential(credential);
            if (userCredential.user != null && onAutoVerified != null) {
              onAutoVerified(AppUser.fromFirebaseUser(userCredential.user!));
            }
          } catch (e) {
            onError('Auto verification failed');
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage = _getErrorMessage(e.code);
          if (e.code == 'billing-not-enabled') {
            errorMessage = 'Phone authentication requires Firebase billing to be enabled. Please upgrade to Blaze plan.';
          } else if (e.code == 'too-many-requests' || e.message?.contains('blocked') == true) {
            errorMessage = 'Too many requests. Please use test phone numbers or try again later.';
          }
          onError(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) {
          onCodeSent(verificationId);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Handle timeout - could be used to show manual input UI
        },
        forceResendingToken: forceResendingToken,
        timeout: const Duration(seconds: 60),
      );
    } catch (e) {
      onError('Phone verification failed');
    }
  }
  
  Future<AppUser> signInWithPhoneOtp({
    required String verificationId,
    required String otpCode,
  }) async {
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otpCode,
      );
      
      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      
      if (userCredential.user == null) {
        throw const AuthException('Phone sign in failed', 'sign-in-failed');
      }
      
      return AppUser.fromFirebaseUser(userCredential.user!);
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('OTP verification failed', 'otp-failed');
    }
  }
  
  // Password Reset
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email.trim());
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('Failed to send password reset email', 'reset-failed');
    }
  }
  
  // Email Verification
  Future<void> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('Failed to send verification email', 'verification-failed');
    }
  }
  
  Future<void> reloadUser() async {
    await _firebaseAuth.currentUser?.reload();
  }
  
  // Sign Out
  Future<void> signOut() async {
    try {
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (e) {
      throw AuthException('Sign out failed', 'sign-out-failed');
    }
  }
  
  // Delete Account
  Future<void> deleteAccount() async {
    try {
      await _firebaseAuth.currentUser?.delete();
    } on FirebaseAuthException catch (e) {
      throw AuthException(_getErrorMessage(e.code), e.code);
    } catch (e) {
      throw AuthException('Failed to delete account', 'delete-failed');
    }
  }
  
  // Helper method to get user-friendly error messages
  String _getErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No account found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. If you signed up with Google, please use "Continue with Google" instead.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'operation-not-allowed':
        return 'This sign-in method is not enabled.';
      case 'invalid-verification-code':
        return 'Invalid verification code. Please try again.';
      case 'invalid-verification-id':
        return 'Invalid verification ID. Please request a new code.';
      case 'quota-exceeded':
        return 'SMS quota exceeded. Please try again later.';
      case 'invalid-credential':
        return 'Email or password is incorrect. Please check your credentials or sign up if you don\'t have an account.';
      case 'network-request-failed':
        return 'Network error. Please check your connection and try again.';
      case 'internal-error':
        return 'An internal error occurred. Please try again.';
      default:
        // For unknown error codes, provide a more helpful message
        return 'Login failed. Please check your email and password and try again.';
    }
  }
}
