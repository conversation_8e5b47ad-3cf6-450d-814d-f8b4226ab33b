-- Verification Script for Profile Creation Changes
-- Run this after deploying the migration to verify everything works correctly

-- =============================================================================
-- PART 1: VERIFY SCHEMA CHANGES
-- =============================================================================

SELECT 
    '=== SCHEMA VERIFICATION ===' as section,
    '' as details;

-- Check column defaults and nullability
SELECT 
    'Column Defaults' as check_type,
    column_name,
    COALESCE(column_default, 'NULL') as column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN ('role_id', 'subscription_plan', 'native_language')
ORDER BY column_name;

-- Check subscription plan constraint
SELECT 
    'Subscription Plan Constraint' as check_type,
    constraint_name,
    check_clause
FROM information_schema.check_constraints 
WHERE constraint_name LIKE '%subscription_plan%';

-- =============================================================================
-- PART 2: VERIFY DATA MIGRATION
-- =============================================================================

SELECT 
    '=== DATA MIGRATION VERIFICATION ===' as section,
    '' as details;

-- Check subscription plan distribution
SELECT 
    'Subscription Plans' as check_type,
    subscription_plan,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM profiles 
WHERE deleted_at IS NULL
GROUP BY subscription_plan
ORDER BY user_count DESC;

-- Check role assignment distribution
SELECT 
    'Role Assignment' as check_type,
    CASE 
        WHEN role_id IS NULL THEN 'No Role (NULL)'
        ELSE 'Has Role'
    END as role_status,
    COUNT(*) as user_count
FROM profiles 
WHERE deleted_at IS NULL
GROUP BY role_id IS NULL;

-- Check native language distribution
SELECT 
    'Native Language' as check_type,
    CASE 
        WHEN native_language IS NULL THEN 'Not Set (NULL)'
        ELSE native_language
    END as language_status,
    COUNT(*) as user_count
FROM profiles 
WHERE deleted_at IS NULL
GROUP BY native_language IS NULL, native_language
ORDER BY user_count DESC;

-- =============================================================================
-- PART 3: TEST PROFILE CREATION FUNCTION
-- =============================================================================

SELECT 
    '=== FUNCTION TESTING ===' as section,
    '' as details;

-- Test creating a new profile
DO $$
DECLARE
    test_uid TEXT := 'test-verification-' || extract(epoch from now())::text;
    new_profile_id UUID;
    test_profile RECORD;
BEGIN
    -- Create test profile
    SELECT public.create_user_profile(
        test_uid,
        '<EMAIL>',
        'Test Verification User',
        '+1234567890',
        'https://example.com/test.jpg',
        'google',
        TRUE
    ) INTO new_profile_id;
    
    -- Fetch the created profile
    SELECT * INTO test_profile
    FROM profiles 
    WHERE id = new_profile_id;
    
    -- Verify the profile has correct defaults
    RAISE NOTICE 'Test Profile Created:';
    RAISE NOTICE '- Profile ID: %', new_profile_id;
    RAISE NOTICE '- Role ID: %', COALESCE(test_profile.role_id::text, 'NULL');
    RAISE NOTICE '- Subscription Plan: %', test_profile.subscription_plan;
    RAISE NOTICE '- Native Language: %', COALESCE(test_profile.native_language, 'NULL');
    RAISE NOTICE '- Auth Provider: %', test_profile.auth_provider;
    
    -- Verify expected values
    IF test_profile.role_id IS NOT NULL THEN
        RAISE WARNING 'ISSUE: role_id should be NULL but got %', test_profile.role_id;
    END IF;
    
    IF test_profile.subscription_plan != 'Free' THEN
        RAISE WARNING 'ISSUE: subscription_plan should be "Free" but got %', test_profile.subscription_plan;
    END IF;
    
    IF test_profile.native_language IS NOT NULL THEN
        RAISE WARNING 'ISSUE: native_language should be NULL but got %', test_profile.native_language;
    END IF;
    
    -- Clean up test data
    DELETE FROM profiles WHERE id = new_profile_id;
    RAISE NOTICE 'Test profile cleaned up successfully';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error during profile creation test: %', SQLERRM;
END $$;

-- =============================================================================
-- PART 4: TEST SYNC FUNCTION
-- =============================================================================

-- Test sync function with new user
DO $$
DECLARE
    test_uid TEXT := 'test-sync-' || extract(epoch from now())::text;
    sync_result BOOLEAN;
    test_profile RECORD;
BEGIN
    -- Test sync function
    SELECT public.sync_firebase_user_data(
        test_uid,
        '<EMAIL>',
        'Sync Test User',
        '+9876543210',
        'https://example.com/sync.jpg',
        TRUE,
        'email',
        NOW()::text
    ) INTO sync_result;
    
    -- Fetch the synced profile
    SELECT * INTO test_profile
    FROM profiles 
    WHERE firebase_uid = test_uid;
    
    RAISE NOTICE 'Sync Function Test:';
    RAISE NOTICE '- Sync Result: %', sync_result;
    RAISE NOTICE '- Profile Found: %', FOUND;
    
    IF FOUND THEN
        RAISE NOTICE '- Role ID: %', COALESCE(test_profile.role_id::text, 'NULL');
        RAISE NOTICE '- Subscription Plan: %', test_profile.subscription_plan;
        RAISE NOTICE '- Native Language: %', COALESCE(test_profile.native_language, 'NULL');
        
        -- Clean up test data
        DELETE FROM profiles WHERE firebase_uid = test_uid;
        RAISE NOTICE 'Sync test profile cleaned up successfully';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error during sync function test: %', SQLERRM;
END $$;

-- =============================================================================
-- PART 5: SUMMARY REPORT
-- =============================================================================

SELECT 
    '=== DEPLOYMENT SUMMARY ===' as section,
    '' as details;

-- Overall statistics
WITH stats AS (
    SELECT 
        COUNT(*) as total_users,
        COUNT(role_id) as users_with_roles,
        COUNT(*) - COUNT(role_id) as users_without_roles,
        COUNT(CASE WHEN subscription_plan = 'Free' THEN 1 END) as free_plan_users,
        COUNT(CASE WHEN subscription_plan = 'basic' THEN 1 END) as basic_plan_users,
        COUNT(CASE WHEN native_language IS NULL THEN 1 END) as null_language_users
    FROM profiles 
    WHERE deleted_at IS NULL
)
SELECT 
    'Summary Statistics' as metric,
    json_build_object(
        'total_users', total_users,
        'users_without_roles', users_without_roles,
        'free_plan_users', free_plan_users,
        'basic_plan_users', basic_plan_users,
        'null_language_users', null_language_users,
        'migration_success', CASE 
            WHEN basic_plan_users = 0 AND free_plan_users > 0 THEN 'SUCCESS'
            ELSE 'NEEDS_REVIEW'
        END
    ) as details
FROM stats;

SELECT 
    '=== VERIFICATION COMPLETE ===' as section,
    'Check the output above for any warnings or issues' as details;
