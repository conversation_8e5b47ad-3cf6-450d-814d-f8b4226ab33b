// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in aai/test/error_scenarios/sync_error_scenarios_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:firebase_auth/firebase_auth.dart' as _i5;
import 'package:firebase_auth_platform_interface/firebase_auth_platform_interface.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;
import 'package:supabase/supabase.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFunctionsClient_0 extends _i1.SmartFake
    implements _i2.FunctionsClient {
  _FakeFunctionsClient_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseStorageClient_1 extends _i1.SmartFake
    implements _i2.SupabaseStorageClient {
  _FakeSupabaseStorageClient_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRealtimeClient_2 extends _i1.SmartFake
    implements _i2.RealtimeClient {
  _FakeRealtimeClient_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestClient_3 extends _i1.SmartFake
    implements _i2.PostgrestClient {
  _FakePostgrestClient_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeGoTrueClient_4 extends _i1.SmartFake implements _i2.GoTrueClient {
  _FakeGoTrueClient_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseQueryBuilder_5 extends _i1.SmartFake
    implements _i2.SupabaseQueryBuilder {
  _FakeSupabaseQueryBuilder_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseQuerySchema_6 extends _i1.SmartFake
    implements _i2.SupabaseQuerySchema {
  _FakeSupabaseQuerySchema_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestFilterBuilder_7<T1> extends _i1.SmartFake
    implements _i2.PostgrestFilterBuilder<T1> {
  _FakePostgrestFilterBuilder_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeRealtimeChannel_8 extends _i1.SmartFake
    implements _i2.RealtimeChannel {
  _FakeRealtimeChannel_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeSupabaseStreamFilterBuilder_9 extends _i1.SmartFake
    implements _i2.SupabaseStreamFilterBuilder {
  _FakeSupabaseStreamFilterBuilder_9(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestQueryBuilder_10<T> extends _i1.SmartFake
    implements _i2.PostgrestQueryBuilder<T> {
  _FakePostgrestQueryBuilder_10(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestBuilder_11<T, S, R> extends _i1.SmartFake
    implements _i2.PostgrestBuilder<T, S, R> {
  _FakePostgrestBuilder_11(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUri_12 extends _i1.SmartFake implements Uri {
  _FakeUri_12(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFuture_13<T> extends _i1.SmartFake implements _i3.Future<T> {
  _FakeFuture_13(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakePostgrestTransformBuilder_14<T1> extends _i1.SmartFake
    implements _i2.PostgrestTransformBuilder<T1> {
  _FakePostgrestTransformBuilder_14(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeResponsePostgrestBuilder_15<T1, S, R> extends _i1.SmartFake
    implements _i2.ResponsePostgrestBuilder<T1, S, R> {
  _FakeResponsePostgrestBuilder_15(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserMetadata_16 extends _i1.SmartFake implements _i4.UserMetadata {
  _FakeUserMetadata_16(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMultiFactor_17 extends _i1.SmartFake implements _i5.MultiFactor {
  _FakeMultiFactor_17(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIdTokenResult_18 extends _i1.SmartFake implements _i4.IdTokenResult {
  _FakeIdTokenResult_18(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserCredential_19 extends _i1.SmartFake
    implements _i5.UserCredential {
  _FakeUserCredential_19(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeConfirmationResult_20 extends _i1.SmartFake
    implements _i5.ConfirmationResult {
  _FakeConfirmationResult_20(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUser_21 extends _i1.SmartFake implements _i5.User {
  _FakeUser_21(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SupabaseClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseClient extends _i1.Mock implements _i2.SupabaseClient {
  MockSupabaseClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FunctionsClient get functions =>
      (super.noSuchMethod(
            Invocation.getter(#functions),
            returnValue: _FakeFunctionsClient_0(
              this,
              Invocation.getter(#functions),
            ),
          )
          as _i2.FunctionsClient);

  @override
  _i2.SupabaseStorageClient get storage =>
      (super.noSuchMethod(
            Invocation.getter(#storage),
            returnValue: _FakeSupabaseStorageClient_1(
              this,
              Invocation.getter(#storage),
            ),
          )
          as _i2.SupabaseStorageClient);

  @override
  _i2.RealtimeClient get realtime =>
      (super.noSuchMethod(
            Invocation.getter(#realtime),
            returnValue: _FakeRealtimeClient_2(
              this,
              Invocation.getter(#realtime),
            ),
          )
          as _i2.RealtimeClient);

  @override
  _i2.PostgrestClient get rest =>
      (super.noSuchMethod(
            Invocation.getter(#rest),
            returnValue: _FakePostgrestClient_3(this, Invocation.getter(#rest)),
          )
          as _i2.PostgrestClient);

  @override
  Map<String, String> get headers =>
      (super.noSuchMethod(
            Invocation.getter(#headers),
            returnValue: <String, String>{},
          )
          as Map<String, String>);

  @override
  _i2.GoTrueClient get auth =>
      (super.noSuchMethod(
            Invocation.getter(#auth),
            returnValue: _FakeGoTrueClient_4(this, Invocation.getter(#auth)),
          )
          as _i2.GoTrueClient);

  @override
  set functions(_i2.FunctionsClient? _functions) => super.noSuchMethod(
    Invocation.setter(#functions, _functions),
    returnValueForMissingStub: null,
  );

  @override
  set storage(_i2.SupabaseStorageClient? _storage) => super.noSuchMethod(
    Invocation.setter(#storage, _storage),
    returnValueForMissingStub: null,
  );

  @override
  set realtime(_i2.RealtimeClient? _realtime) => super.noSuchMethod(
    Invocation.setter(#realtime, _realtime),
    returnValueForMissingStub: null,
  );

  @override
  set rest(_i2.PostgrestClient? _rest) => super.noSuchMethod(
    Invocation.setter(#rest, _rest),
    returnValueForMissingStub: null,
  );

  @override
  set headers(Map<String, String>? headers) => super.noSuchMethod(
    Invocation.setter(#headers, headers),
    returnValueForMissingStub: null,
  );

  @override
  _i2.SupabaseQueryBuilder from(String? table) =>
      (super.noSuchMethod(
            Invocation.method(#from, [table]),
            returnValue: _FakeSupabaseQueryBuilder_5(
              this,
              Invocation.method(#from, [table]),
            ),
          )
          as _i2.SupabaseQueryBuilder);

  @override
  _i2.SupabaseQuerySchema schema(String? schema) =>
      (super.noSuchMethod(
            Invocation.method(#schema, [schema]),
            returnValue: _FakeSupabaseQuerySchema_6(
              this,
              Invocation.method(#schema, [schema]),
            ),
          )
          as _i2.SupabaseQuerySchema);

  @override
  _i2.PostgrestFilterBuilder<T> rpc<T>(
    String? fn, {
    Map<String, dynamic>? params,
    dynamic get = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rpc, [fn], {#params: params, #get: get}),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.RealtimeChannel channel(
    String? name, {
    _i2.RealtimeChannelConfig? opts = const _i2.RealtimeChannelConfig(),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#channel, [name], {#opts: opts}),
            returnValue: _FakeRealtimeChannel_8(
              this,
              Invocation.method(#channel, [name], {#opts: opts}),
            ),
          )
          as _i2.RealtimeChannel);

  @override
  List<_i2.RealtimeChannel> getChannels() =>
      (super.noSuchMethod(
            Invocation.method(#getChannels, []),
            returnValue: <_i2.RealtimeChannel>[],
          )
          as List<_i2.RealtimeChannel>);

  @override
  _i3.Future<String> removeChannel(_i2.RealtimeChannel? channel) =>
      (super.noSuchMethod(
            Invocation.method(#removeChannel, [channel]),
            returnValue: _i3.Future<String>.value(
              _i6.dummyValue<String>(
                this,
                Invocation.method(#removeChannel, [channel]),
              ),
            ),
          )
          as _i3.Future<String>);

  @override
  _i3.Future<List<String>> removeAllChannels() =>
      (super.noSuchMethod(
            Invocation.method(#removeAllChannels, []),
            returnValue: _i3.Future<List<String>>.value(<String>[]),
          )
          as _i3.Future<List<String>>);

  @override
  _i3.Future<void> dispose() =>
      (super.noSuchMethod(
            Invocation.method(#dispose, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}

/// A class which mocks [SupabaseQueryBuilder].
///
/// See the documentation for Mockito's code generation for more information.
class MockSupabaseQueryBuilder extends _i1.Mock
    implements _i2.SupabaseQueryBuilder {
  MockSupabaseQueryBuilder() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.SupabaseStreamFilterBuilder stream({required List<String>? primaryKey}) =>
      (super.noSuchMethod(
            Invocation.method(#stream, [], {#primaryKey: primaryKey}),
            returnValue: _FakeSupabaseStreamFilterBuilder_9(
              this,
              Invocation.method(#stream, [], {#primaryKey: primaryKey}),
            ),
          )
          as _i2.SupabaseStreamFilterBuilder);

  @override
  _i2.PostgrestFilterBuilder<List<Map<String, dynamic>>> select([
    String? columns = '*',
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#select, [columns]),
            returnValue:
                _FakePostgrestFilterBuilder_7<List<Map<String, dynamic>>>(
                  this,
                  Invocation.method(#select, [columns]),
                ),
          )
          as _i2.PostgrestFilterBuilder<List<Map<String, dynamic>>>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> insert(
    Object? values, {
    bool? defaultToNull = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #insert,
              [values],
              {#defaultToNull: defaultToNull},
            ),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(
                #insert,
                [values],
                {#defaultToNull: defaultToNull},
              ),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> upsert(
    Object? values, {
    String? onConflict,
    bool? ignoreDuplicates = false,
    bool? defaultToNull = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #upsert,
              [values],
              {
                #onConflict: onConflict,
                #ignoreDuplicates: ignoreDuplicates,
                #defaultToNull: defaultToNull,
              },
            ),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(
                #upsert,
                [values],
                {
                  #onConflict: onConflict,
                  #ignoreDuplicates: ignoreDuplicates,
                  #defaultToNull: defaultToNull,
                },
              ),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> update(Map<dynamic, dynamic>? values) =>
      (super.noSuchMethod(
            Invocation.method(#update, [values]),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(#update, [values]),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> delete() =>
      (super.noSuchMethod(
            Invocation.method(#delete, []),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(#delete, []),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<int> count([
    _i2.CountOption? option = _i2.CountOption.exact,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#count, [option]),
            returnValue: _FakePostgrestFilterBuilder_7<int>(
              this,
              Invocation.method(#count, [option]),
            ),
          )
          as _i2.PostgrestFilterBuilder<int>);

  @override
  _i2.PostgrestQueryBuilder<dynamic> setHeader(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#setHeader, [key, value]),
            returnValue: _FakePostgrestQueryBuilder_10<dynamic>(
              this,
              Invocation.method(#setHeader, [key, value]),
            ),
          )
          as _i2.PostgrestQueryBuilder<dynamic>);

  @override
  _i2.PostgrestBuilder<U, U, dynamic> withConverter<U>(
    _i2.PostgrestConverter<U, dynamic>? converter,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#withConverter, [converter]),
            returnValue: _FakePostgrestBuilder_11<U, U, dynamic>(
              this,
              Invocation.method(#withConverter, [converter]),
            ),
          )
          as _i2.PostgrestBuilder<U, U, dynamic>);

  @override
  Uri appendSearchParams(String? key, String? value, [Uri? url]) =>
      (super.noSuchMethod(
            Invocation.method(#appendSearchParams, [key, value, url]),
            returnValue: _FakeUri_12(
              this,
              Invocation.method(#appendSearchParams, [key, value, url]),
            ),
          )
          as Uri);

  @override
  Uri overrideSearchParams(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#overrideSearchParams, [key, value]),
            returnValue: _FakeUri_12(
              this,
              Invocation.method(#overrideSearchParams, [key, value]),
            ),
          )
          as Uri);

  @override
  _i3.Stream<dynamic> asStream() =>
      (super.noSuchMethod(
            Invocation.method(#asStream, []),
            returnValue: _i3.Stream<dynamic>.empty(),
          )
          as _i3.Stream<dynamic>);

  @override
  _i3.Future<dynamic> catchError(
    Function? onError, {
    bool Function(Object)? test,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#catchError, [onError], {#test: test}),
            returnValue: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<U> then<U>(
    _i3.FutureOr<U> Function(dynamic)? onValue, {
    Function? onError,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#then, [onValue], {#onError: onError}),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<U>(
                    this,
                    Invocation.method(#then, [onValue], {#onError: onError}),
                  ),
                  (U v) => _i3.Future<U>.value(v),
                ) ??
                _FakeFuture_13<U>(
                  this,
                  Invocation.method(#then, [onValue], {#onError: onError}),
                ),
          )
          as _i3.Future<U>);

  @override
  _i3.Future<dynamic> timeout(
    Duration? timeLimit, {
    _i3.FutureOr<dynamic> Function()? onTimeout,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#timeout, [timeLimit], {#onTimeout: onTimeout}),
            returnValue: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);

  @override
  _i3.Future<dynamic> whenComplete(_i3.FutureOr<void> Function()? action) =>
      (super.noSuchMethod(
            Invocation.method(#whenComplete, [action]),
            returnValue: _i3.Future<dynamic>.value(),
          )
          as _i3.Future<dynamic>);
}

/// A class which mocks [PostgrestFilterBuilder].
///
/// See the documentation for Mockito's code generation for more information.
class MockPostgrestFilterBuilder<T> extends _i1.Mock
    implements _i2.PostgrestFilterBuilder<T> {
  MockPostgrestFilterBuilder() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PostgrestFilterBuilder<T> copyWithUrl(Uri? url) =>
      (super.noSuchMethod(
            Invocation.method(#copyWithUrl, [url]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#copyWithUrl, [url]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> not(
    String? column,
    String? operator,
    Object? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#not, [column, operator, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#not, [column, operator, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> or(
    String? filters, {
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #or,
              [filters],
              {#referencedTable: referencedTable},
            ),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(
                #or,
                [filters],
                {#referencedTable: referencedTable},
              ),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> eq(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#eq, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#eq, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> neq(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#neq, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#neq, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> gt(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#gt, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#gt, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> gte(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#gte, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#gte, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> lt(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#lt, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#lt, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> lte(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#lte, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#lte, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> like(String? column, String? pattern) =>
      (super.noSuchMethod(
            Invocation.method(#like, [column, pattern]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#like, [column, pattern]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> likeAllOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#likeAllOf, [column, patterns]),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(#likeAllOf, [column, patterns]),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> likeAnyOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#likeAnyOf, [column, patterns]),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(#likeAnyOf, [column, patterns]),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<T> ilike(String? column, String? pattern) =>
      (super.noSuchMethod(
            Invocation.method(#ilike, [column, pattern]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#ilike, [column, pattern]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> ilikeAllOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#ilikeAllOf, [column, patterns]),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(#ilikeAllOf, [column, patterns]),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<dynamic> ilikeAnyOf(
    String? column,
    List<String>? patterns,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#ilikeAnyOf, [column, patterns]),
            returnValue: _FakePostgrestFilterBuilder_7<dynamic>(
              this,
              Invocation.method(#ilikeAnyOf, [column, patterns]),
            ),
          )
          as _i2.PostgrestFilterBuilder<dynamic>);

  @override
  _i2.PostgrestFilterBuilder<T> isFilter(String? column, bool? value) =>
      (super.noSuchMethod(
            Invocation.method(#isFilter, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#isFilter, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> inFilter(
    String? column,
    List<dynamic>? values,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#inFilter, [column, values]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#inFilter, [column, values]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> contains(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#contains, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#contains, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> containedBy(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#containedBy, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#containedBy, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeLt(String? column, String? range) =>
      (super.noSuchMethod(
            Invocation.method(#rangeLt, [column, range]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rangeLt, [column, range]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeGt(String? column, String? range) =>
      (super.noSuchMethod(
            Invocation.method(#rangeGt, [column, range]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rangeGt, [column, range]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeGte(String? column, String? range) =>
      (super.noSuchMethod(
            Invocation.method(#rangeGte, [column, range]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rangeGte, [column, range]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeLte(String? column, String? range) =>
      (super.noSuchMethod(
            Invocation.method(#rangeLte, [column, range]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rangeLte, [column, range]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> rangeAdjacent(String? column, String? range) =>
      (super.noSuchMethod(
            Invocation.method(#rangeAdjacent, [column, range]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#rangeAdjacent, [column, range]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> overlaps(String? column, Object? value) =>
      (super.noSuchMethod(
            Invocation.method(#overlaps, [column, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#overlaps, [column, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> textSearch(
    String? column,
    String? query, {
    String? config,
    _i2.TextSearchType? type,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #textSearch,
              [column, query],
              {#config: config, #type: type},
            ),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(
                #textSearch,
                [column, query],
                {#config: config, #type: type},
              ),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> filter(
    String? column,
    String? operator,
    Object? value,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#filter, [column, operator, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#filter, [column, operator, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> match(Map<String, Object>? query) =>
      (super.noSuchMethod(
            Invocation.method(#match, [query]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#match, [query]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestFilterBuilder<T> setHeader(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#setHeader, [key, value]),
            returnValue: _FakePostgrestFilterBuilder_7<T>(
              this,
              Invocation.method(#setHeader, [key, value]),
            ),
          )
          as _i2.PostgrestFilterBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<List<Map<String, dynamic>>> select([
    String? columns = '*',
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#select, [columns]),
            returnValue:
                _FakePostgrestTransformBuilder_14<List<Map<String, dynamic>>>(
                  this,
                  Invocation.method(#select, [columns]),
                ),
          )
          as _i2.PostgrestTransformBuilder<List<Map<String, dynamic>>>);

  @override
  _i2.PostgrestTransformBuilder<T> order(
    String? column, {
    bool? ascending = false,
    bool? nullsFirst = false,
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #order,
              [column],
              {
                #ascending: ascending,
                #nullsFirst: nullsFirst,
                #referencedTable: referencedTable,
              },
            ),
            returnValue: _FakePostgrestTransformBuilder_14<T>(
              this,
              Invocation.method(
                #order,
                [column],
                {
                  #ascending: ascending,
                  #nullsFirst: nullsFirst,
                  #referencedTable: referencedTable,
                },
              ),
            ),
          )
          as _i2.PostgrestTransformBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<T> limit(
    int? count, {
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #limit,
              [count],
              {#referencedTable: referencedTable},
            ),
            returnValue: _FakePostgrestTransformBuilder_14<T>(
              this,
              Invocation.method(
                #limit,
                [count],
                {#referencedTable: referencedTable},
              ),
            ),
          )
          as _i2.PostgrestTransformBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<T> range(
    int? from,
    int? to, {
    String? referencedTable,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #range,
              [from, to],
              {#referencedTable: referencedTable},
            ),
            returnValue: _FakePostgrestTransformBuilder_14<T>(
              this,
              Invocation.method(
                #range,
                [from, to],
                {#referencedTable: referencedTable},
              ),
            ),
          )
          as _i2.PostgrestTransformBuilder<T>);

  @override
  _i2.PostgrestTransformBuilder<Map<String, dynamic>> single() =>
      (super.noSuchMethod(
            Invocation.method(#single, []),
            returnValue:
                _FakePostgrestTransformBuilder_14<Map<String, dynamic>>(
                  this,
                  Invocation.method(#single, []),
                ),
          )
          as _i2.PostgrestTransformBuilder<Map<String, dynamic>>);

  @override
  _i2.PostgrestTransformBuilder<Map<String, dynamic>?> maybeSingle() =>
      (super.noSuchMethod(
            Invocation.method(#maybeSingle, []),
            returnValue:
                _FakePostgrestTransformBuilder_14<Map<String, dynamic>?>(
                  this,
                  Invocation.method(#maybeSingle, []),
                ),
          )
          as _i2.PostgrestTransformBuilder<Map<String, dynamic>?>);

  @override
  _i2.PostgrestTransformBuilder<String> csv() =>
      (super.noSuchMethod(
            Invocation.method(#csv, []),
            returnValue: _FakePostgrestTransformBuilder_14<String>(
              this,
              Invocation.method(#csv, []),
            ),
          )
          as _i2.PostgrestTransformBuilder<String>);

  @override
  _i2.ResponsePostgrestBuilder<_i2.PostgrestResponse<T>, T, T> count([
    _i2.CountOption? count = _i2.CountOption.exact,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#count, [count]),
            returnValue:
                _FakeResponsePostgrestBuilder_15<
                  _i2.PostgrestResponse<T>,
                  T,
                  T
                >(this, Invocation.method(#count, [count])),
          )
          as _i2.ResponsePostgrestBuilder<_i2.PostgrestResponse<T>, T, T>);

  @override
  _i2.PostgrestBuilder<void, void, void> head() =>
      (super.noSuchMethod(
            Invocation.method(#head, []),
            returnValue: _FakePostgrestBuilder_11<void, void, void>(
              this,
              Invocation.method(#head, []),
            ),
          )
          as _i2.PostgrestBuilder<void, void, void>);

  @override
  _i2.ResponsePostgrestBuilder<
    Map<String, dynamic>,
    Map<String, dynamic>,
    Map<String, dynamic>
  >
  geojson() =>
      (super.noSuchMethod(
            Invocation.method(#geojson, []),
            returnValue:
                _FakeResponsePostgrestBuilder_15<
                  Map<String, dynamic>,
                  Map<String, dynamic>,
                  Map<String, dynamic>
                >(this, Invocation.method(#geojson, [])),
          )
          as _i2.ResponsePostgrestBuilder<
            Map<String, dynamic>,
            Map<String, dynamic>,
            Map<String, dynamic>
          >);

  @override
  _i2.PostgrestBuilder<String, String, String> explain({
    bool? analyze = false,
    bool? verbose = false,
    bool? settings = false,
    bool? buffers = false,
    bool? wal = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#explain, [], {
              #analyze: analyze,
              #verbose: verbose,
              #settings: settings,
              #buffers: buffers,
              #wal: wal,
            }),
            returnValue: _FakePostgrestBuilder_11<String, String, String>(
              this,
              Invocation.method(#explain, [], {
                #analyze: analyze,
                #verbose: verbose,
                #settings: settings,
                #buffers: buffers,
                #wal: wal,
              }),
            ),
          )
          as _i2.PostgrestBuilder<String, String, String>);

  @override
  _i2.PostgrestBuilder<U, U, T> withConverter<U>(
    _i2.PostgrestConverter<U, T>? converter,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#withConverter, [converter]),
            returnValue: _FakePostgrestBuilder_11<U, U, T>(
              this,
              Invocation.method(#withConverter, [converter]),
            ),
          )
          as _i2.PostgrestBuilder<U, U, T>);

  @override
  Uri appendSearchParams(String? key, String? value, [Uri? url]) =>
      (super.noSuchMethod(
            Invocation.method(#appendSearchParams, [key, value, url]),
            returnValue: _FakeUri_12(
              this,
              Invocation.method(#appendSearchParams, [key, value, url]),
            ),
          )
          as Uri);

  @override
  Uri overrideSearchParams(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#overrideSearchParams, [key, value]),
            returnValue: _FakeUri_12(
              this,
              Invocation.method(#overrideSearchParams, [key, value]),
            ),
          )
          as Uri);

  @override
  _i3.Stream<T> asStream() =>
      (super.noSuchMethod(
            Invocation.method(#asStream, []),
            returnValue: _i3.Stream<T>.empty(),
          )
          as _i3.Stream<T>);

  @override
  _i3.Future<T> catchError(Function? onError, {bool Function(Object)? test}) =>
      (super.noSuchMethod(
            Invocation.method(#catchError, [onError], {#test: test}),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#catchError, [onError], {#test: test}),
                  ),
                  (T v) => _i3.Future<T>.value(v),
                ) ??
                _FakeFuture_13<T>(
                  this,
                  Invocation.method(#catchError, [onError], {#test: test}),
                ),
          )
          as _i3.Future<T>);

  @override
  _i3.Future<U> then<U>(
    _i3.FutureOr<U> Function(T)? onValue, {
    Function? onError,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#then, [onValue], {#onError: onError}),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<U>(
                    this,
                    Invocation.method(#then, [onValue], {#onError: onError}),
                  ),
                  (U v) => _i3.Future<U>.value(v),
                ) ??
                _FakeFuture_13<U>(
                  this,
                  Invocation.method(#then, [onValue], {#onError: onError}),
                ),
          )
          as _i3.Future<U>);

  @override
  _i3.Future<T> timeout(
    Duration? timeLimit, {
    _i3.FutureOr<T> Function()? onTimeout,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#timeout, [timeLimit], {#onTimeout: onTimeout}),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<T>(
                    this,
                    Invocation.method(
                      #timeout,
                      [timeLimit],
                      {#onTimeout: onTimeout},
                    ),
                  ),
                  (T v) => _i3.Future<T>.value(v),
                ) ??
                _FakeFuture_13<T>(
                  this,
                  Invocation.method(
                    #timeout,
                    [timeLimit],
                    {#onTimeout: onTimeout},
                  ),
                ),
          )
          as _i3.Future<T>);

  @override
  _i3.Future<T> whenComplete(_i3.FutureOr<void> Function()? action) =>
      (super.noSuchMethod(
            Invocation.method(#whenComplete, [action]),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#whenComplete, [action]),
                  ),
                  (T v) => _i3.Future<T>.value(v),
                ) ??
                _FakeFuture_13<T>(
                  this,
                  Invocation.method(#whenComplete, [action]),
                ),
          )
          as _i3.Future<T>);
}

/// A class which mocks [PostgrestBuilder].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockPostgrestBuilder<T, S, R> extends _i1.Mock
    implements _i2.PostgrestBuilder<T, S, R> {
  MockPostgrestBuilder() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.PostgrestBuilder<T, S, R> setHeader(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#setHeader, [key, value]),
            returnValue: _FakePostgrestBuilder_11<T, S, R>(
              this,
              Invocation.method(#setHeader, [key, value]),
            ),
          )
          as _i2.PostgrestBuilder<T, S, R>);

  @override
  Uri appendSearchParams(String? key, String? value, [Uri? url]) =>
      (super.noSuchMethod(
            Invocation.method(#appendSearchParams, [key, value, url]),
            returnValue: _FakeUri_12(
              this,
              Invocation.method(#appendSearchParams, [key, value, url]),
            ),
          )
          as Uri);

  @override
  Uri overrideSearchParams(String? key, String? value) =>
      (super.noSuchMethod(
            Invocation.method(#overrideSearchParams, [key, value]),
            returnValue: _FakeUri_12(
              this,
              Invocation.method(#overrideSearchParams, [key, value]),
            ),
          )
          as Uri);

  @override
  _i3.Stream<T> asStream() =>
      (super.noSuchMethod(
            Invocation.method(#asStream, []),
            returnValue: _i3.Stream<T>.empty(),
          )
          as _i3.Stream<T>);

  @override
  _i3.Future<T> catchError(Function? onError, {bool Function(Object)? test}) =>
      (super.noSuchMethod(
            Invocation.method(#catchError, [onError], {#test: test}),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#catchError, [onError], {#test: test}),
                  ),
                  (T v) => _i3.Future<T>.value(v),
                ) ??
                _FakeFuture_13<T>(
                  this,
                  Invocation.method(#catchError, [onError], {#test: test}),
                ),
          )
          as _i3.Future<T>);

  @override
  _i3.Future<U> then<U>(
    _i3.FutureOr<U> Function(T)? onValue, {
    Function? onError,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#then, [onValue], {#onError: onError}),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<U>(
                    this,
                    Invocation.method(#then, [onValue], {#onError: onError}),
                  ),
                  (U v) => _i3.Future<U>.value(v),
                ) ??
                _FakeFuture_13<U>(
                  this,
                  Invocation.method(#then, [onValue], {#onError: onError}),
                ),
          )
          as _i3.Future<U>);

  @override
  _i3.Future<T> timeout(
    Duration? timeLimit, {
    _i3.FutureOr<T> Function()? onTimeout,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#timeout, [timeLimit], {#onTimeout: onTimeout}),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<T>(
                    this,
                    Invocation.method(
                      #timeout,
                      [timeLimit],
                      {#onTimeout: onTimeout},
                    ),
                  ),
                  (T v) => _i3.Future<T>.value(v),
                ) ??
                _FakeFuture_13<T>(
                  this,
                  Invocation.method(
                    #timeout,
                    [timeLimit],
                    {#onTimeout: onTimeout},
                  ),
                ),
          )
          as _i3.Future<T>);

  @override
  _i3.Future<T> whenComplete(_i3.FutureOr<void> Function()? action) =>
      (super.noSuchMethod(
            Invocation.method(#whenComplete, [action]),
            returnValue:
                _i6.ifNotNull(
                  _i6.dummyValueOrNull<T>(
                    this,
                    Invocation.method(#whenComplete, [action]),
                  ),
                  (T v) => _i3.Future<T>.value(v),
                ) ??
                _FakeFuture_13<T>(
                  this,
                  Invocation.method(#whenComplete, [action]),
                ),
          )
          as _i3.Future<T>);
}

/// A class which mocks [User].
///
/// See the documentation for Mockito's code generation for more information.
class MockUser extends _i1.Mock implements _i5.User {
  MockUser() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get emailVerified =>
      (super.noSuchMethod(Invocation.getter(#emailVerified), returnValue: false)
          as bool);

  @override
  bool get isAnonymous =>
      (super.noSuchMethod(Invocation.getter(#isAnonymous), returnValue: false)
          as bool);

  @override
  _i4.UserMetadata get metadata =>
      (super.noSuchMethod(
            Invocation.getter(#metadata),
            returnValue: _FakeUserMetadata_16(
              this,
              Invocation.getter(#metadata),
            ),
          )
          as _i4.UserMetadata);

  @override
  List<_i4.UserInfo> get providerData =>
      (super.noSuchMethod(
            Invocation.getter(#providerData),
            returnValue: <_i4.UserInfo>[],
          )
          as List<_i4.UserInfo>);

  @override
  String get uid =>
      (super.noSuchMethod(
            Invocation.getter(#uid),
            returnValue: _i6.dummyValue<String>(this, Invocation.getter(#uid)),
          )
          as String);

  @override
  _i5.MultiFactor get multiFactor =>
      (super.noSuchMethod(
            Invocation.getter(#multiFactor),
            returnValue: _FakeMultiFactor_17(
              this,
              Invocation.getter(#multiFactor),
            ),
          )
          as _i5.MultiFactor);

  @override
  _i3.Future<void> delete() =>
      (super.noSuchMethod(
            Invocation.method(#delete, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<String?> getIdToken([bool? forceRefresh = false]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdToken, [forceRefresh]),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<_i4.IdTokenResult> getIdTokenResult([
    bool? forceRefresh = false,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdTokenResult, [forceRefresh]),
            returnValue: _i3.Future<_i4.IdTokenResult>.value(
              _FakeIdTokenResult_18(
                this,
                Invocation.method(#getIdTokenResult, [forceRefresh]),
              ),
            ),
          )
          as _i3.Future<_i4.IdTokenResult>);

  @override
  _i3.Future<_i5.UserCredential> linkWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithCredential, [credential]),
            returnValue: _i3.Future<_i5.UserCredential>.value(
              _FakeUserCredential_19(
                this,
                Invocation.method(#linkWithCredential, [credential]),
              ),
            ),
          )
          as _i3.Future<_i5.UserCredential>);

  @override
  _i3.Future<_i5.UserCredential> linkWithProvider(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithProvider, [provider]),
            returnValue: _i3.Future<_i5.UserCredential>.value(
              _FakeUserCredential_19(
                this,
                Invocation.method(#linkWithProvider, [provider]),
              ),
            ),
          )
          as _i3.Future<_i5.UserCredential>);

  @override
  _i3.Future<_i5.UserCredential> reauthenticateWithProvider(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithProvider, [provider]),
            returnValue: _i3.Future<_i5.UserCredential>.value(
              _FakeUserCredential_19(
                this,
                Invocation.method(#reauthenticateWithProvider, [provider]),
              ),
            ),
          )
          as _i3.Future<_i5.UserCredential>);

  @override
  _i3.Future<_i5.UserCredential> reauthenticateWithPopup(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPopup, [provider]),
            returnValue: _i3.Future<_i5.UserCredential>.value(
              _FakeUserCredential_19(
                this,
                Invocation.method(#reauthenticateWithPopup, [provider]),
              ),
            ),
          )
          as _i3.Future<_i5.UserCredential>);

  @override
  _i3.Future<void> reauthenticateWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithRedirect, [provider]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i5.UserCredential> linkWithPopup(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPopup, [provider]),
            returnValue: _i3.Future<_i5.UserCredential>.value(
              _FakeUserCredential_19(
                this,
                Invocation.method(#linkWithPopup, [provider]),
              ),
            ),
          )
          as _i3.Future<_i5.UserCredential>);

  @override
  _i3.Future<void> linkWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithRedirect, [provider]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i5.ConfirmationResult> linkWithPhoneNumber(
    String? phoneNumber, [
    _i5.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i3.Future<_i5.ConfirmationResult>.value(
              _FakeConfirmationResult_20(
                this,
                Invocation.method(#linkWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i3.Future<_i5.ConfirmationResult>);

  @override
  _i3.Future<_i5.UserCredential> reauthenticateWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithCredential, [credential]),
            returnValue: _i3.Future<_i5.UserCredential>.value(
              _FakeUserCredential_19(
                this,
                Invocation.method(#reauthenticateWithCredential, [credential]),
              ),
            ),
          )
          as _i3.Future<_i5.UserCredential>);

  @override
  _i3.Future<void> reload() =>
      (super.noSuchMethod(
            Invocation.method(#reload, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> sendEmailVerification([
    _i4.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, [actionCodeSettings]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<_i5.User> unlink(String? providerId) =>
      (super.noSuchMethod(
            Invocation.method(#unlink, [providerId]),
            returnValue: _i3.Future<_i5.User>.value(
              _FakeUser_21(this, Invocation.method(#unlink, [providerId])),
            ),
          )
          as _i3.Future<_i5.User>);

  @override
  _i3.Future<void> updateEmail(String? newEmail) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [newEmail]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updatePassword(String? newPassword) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [newPassword]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updatePhoneNumber(
    _i4.PhoneAuthCredential? phoneCredential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhoneNumber, [phoneCredential]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateDisplayName(String? displayName) =>
      (super.noSuchMethod(
            Invocation.method(#updateDisplayName, [displayName]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updatePhotoURL(String? photoURL) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhotoURL, [photoURL]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateProfile({String? displayName, String? photoURL}) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> verifyBeforeUpdateEmail(
    String? newEmail, [
    _i4.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#verifyBeforeUpdateEmail, [
              newEmail,
              actionCodeSettings,
            ]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);
}

/// A class which mocks [UserMetadata].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserMetadata extends _i1.Mock implements _i4.UserMetadata {
  MockUserMetadata() {
    _i1.throwOnMissingStub(this);
  }
}
