import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../../lib/widgets/profile_sidebar.dart';
import '../../lib/providers/profile_provider.dart';
import '../../lib/providers/auth_provider.dart';
import '../../lib/models/user_profile.dart';
import '../../lib/models/user_model.dart';
import '../../lib/models/auth_state.dart';
import '../../lib/services/profile_update_service.dart';
import '../../lib/utils/result.dart';

// Generate mocks
@GenerateMocks([ProfileUpdateService])
import 'profile_sidebar_test.mocks.dart';

void main() {
  group('ProfileSidebar Data Loading Tests', () {
    late MockProfileUpdateService mockProfileService;

    setUp(() {
      mockProfileService = MockProfileUpdateService();
    });

    testWidgets('should trigger profile load when profile is null and user is authenticated', (WidgetTester tester) async {
      // Create test user
      final testUser = AppUser(
        id: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
        phoneNumber: '+************',
        isEmailVerified: true,
        authProvider: AuthProvider.email,
        createdAt: DateTime.now(),
      );

      // Create test profile
      final testProfile = UserProfile(
        id: 'profile-id',
        firebaseUid: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Updated Test User',
        phoneNumber: '+************',
        syncVersion: 5,
      );

      // Mock profile service to return success
      when(mockProfileService.getProfile()).thenAnswer(
        (_) async => Result.success(testProfile),
      );

      // Create container with overrides
      final container = ProviderContainer(
        overrides: [
          profileUpdateServiceProvider.overrideWithValue(mockProfileService),
          // Mock auth state with authenticated user
          authProvider.overrideWith((ref) => MockAuthNotifier(testUser)),
        ],
      );

      // Build widget tree with Consumer that mimics sidebar usage
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: Scaffold(
              drawer: Drawer(
                child: Consumer(
                  builder: (context, ref, child) {
                    final currentUser = ref.watch(currentUserProvider);
                    final profileState = ref.watch(profileProvider);
                    final profile = profileState.profile;

                    // This is the fix we implemented
                    if (profile == null && !profileState.isLoading && currentUser != null) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        ref.read(profileProvider.notifier).loadProfile();
                      });
                    }

                    final userName = profile?.displayName ?? currentUser?.displayName ?? 'Name';
                    final userEmail = profile?.email ?? currentUser?.email ?? '<EMAIL>';
                    final userPhone = profile?.phoneNumber ?? currentUser?.phoneNumber ?? '+91 - - - - - - - - - -';

                    return ProfileSidebar(
                      userName: userName,
                      userEmail: userEmail,
                      userPhone: userPhone,
                    );
                  },
                ),
              ),
              body: const Center(child: Text('Test')),
            ),
          ),
        ),
      );

      // Open the drawer to trigger the Consumer
      await tester.tap(find.byType(Scaffold));
      await tester.pump();

      // Verify initial state shows Firebase Auth data
      expect(find.text('Test User'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);

      // Wait for profile load to complete
      await tester.pumpAndSettle();

      // Verify profile service was called
      verify(mockProfileService.getProfile()).called(1);

      // Verify updated profile data is displayed
      expect(find.text('Updated Test User'), findsOneWidget);
    });

    testWidgets('should not trigger profile load when profile is already loaded', (WidgetTester tester) async {
      final testUser = AppUser(
        id: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
        phoneNumber: '+************',
        isEmailVerified: true,
        authProvider: AuthProvider.email,
        createdAt: DateTime.now(),
      );

      final testProfile = UserProfile(
        id: 'profile-id',
        firebaseUid: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Existing Profile',
        phoneNumber: '+************',
        syncVersion: 5,
      );

      // Create container with pre-loaded profile
      final container = ProviderContainer(
        overrides: [
          profileUpdateServiceProvider.overrideWithValue(mockProfileService),
          authProvider.overrideWith((ref) => MockAuthNotifier(testUser)),
          profileProvider.overrideWith((ref) => MockProfileNotifier(
            ProfileState(profile: testProfile, isLoading: false),
          )),
        ],
      );

      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: Scaffold(
              drawer: Drawer(
                child: Consumer(
                  builder: (context, ref, child) {
                    final currentUser = ref.watch(currentUserProvider);
                    final profileState = ref.watch(profileProvider);
                    final profile = profileState.profile;

                    // This should NOT trigger since profile is already loaded
                    if (profile == null && !profileState.isLoading && currentUser != null) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        ref.read(profileProvider.notifier).loadProfile();
                      });
                    }

                    final userName = profile?.displayName ?? currentUser?.displayName ?? 'Name';

                    return ProfileSidebar(userName: userName);
                  },
                ),
              ),
              body: const Center(child: Text('Test')),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify profile service was NOT called since profile was already loaded
      verifyNever(mockProfileService.getProfile());

      // Verify existing profile data is displayed immediately
      expect(find.text('Existing Profile'), findsOneWidget);
    });
  });
}

// Mock classes for testing
class MockAuthNotifier extends StateNotifier<AuthState> {
  MockAuthNotifier(AppUser user) : super(AuthState.authenticated(user));
}

class MockProfileNotifier extends StateNotifier<ProfileState> {
  MockProfileNotifier(ProfileState initialState) : super(initialState);
  
  @override
  Future<void> loadProfile({bool showLoading = true}) async {
    // Mock implementation - do nothing for test
  }
}
