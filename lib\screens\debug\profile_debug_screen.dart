import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../../services/profile_debug_service.dart';
import '../../utils/app_colors.dart';

class ProfileDebugScreen extends StatefulWidget {
  const ProfileDebugScreen({super.key});

  @override
  State<ProfileDebugScreen> createState() => _ProfileDebugScreenState();
}

class _ProfileDebugScreenState extends State<ProfileDebugScreen> {
  final ProfileDebugService _debugService = ProfileDebugService();
  Map<String, dynamic>? _debugResults;
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profile Update Debug'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _debugResults != null ? _copyResults : null,
            tooltip: 'Copy Results',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              color: AppColors.info,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🔍 Profile Update Troubleshooter',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This tool will test your profile update functionality and identify any issues.',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Run Tests Button
            ElevatedButton.icon(
              onPressed: _isRunning ? null : _runDebugTests,
              icon: _isRunning 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.play_arrow),
              label: Text(_isRunning ? 'Running Tests...' : 'Run Debug Tests'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Results
            Expanded(
              child: _debugResults == null
                  ? const Center(
                      child: Text(
                        'Click "Run Debug Tests" to start troubleshooting',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    )
                  : _buildResultsView(),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runDebugTests() async {
    setState(() {
      _isRunning = true;
      _debugResults = null;
    });

    try {
      final results = await _debugService.runDebugTests();
      setState(() {
        _debugResults = results;
      });
      
      // Print to console for debugging
      _debugService.printDebugResults(results);
      
    } catch (e) {
      setState(() {
        _debugResults = {
          'error': 'Failed to run debug tests: $e'
        };
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Widget _buildResultsView() {
    if (_debugResults == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Summary Card
          _buildSummaryCard(),
          
          const SizedBox(height: 16),
          
          // Detailed Results
          ..._debugResults!.entries.map((entry) => _buildTestResultCard(
            entry.key,
            entry.value,
          )),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    final totalTests = _debugResults!.length;
    final passedTests = _debugResults!.values
        .where((result) => _getTestStatus(result) == 'SUCCESS')
        .length;
    final failedTests = _debugResults!.values
        .where((result) => _getTestStatus(result) == 'FAILED' || _getTestStatus(result) == 'ERROR')
        .length;

    return Card(
      color: failedTests > 0 ? Colors.red.shade50 : Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📊 Test Summary',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: failedTests > 0 ? Colors.red.shade700 : Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildStatusChip('✅ Passed', passedTests.toString(), Colors.green),
                const SizedBox(width: 8),
                _buildStatusChip('❌ Failed', failedTests.toString(), Colors.red),
                const SizedBox(width: 8),
                _buildStatusChip('📋 Total', totalTests.toString(), Colors.blue),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String label, String count, Color color) {
    return Chip(
      label: Text('$label: $count'),
      backgroundColor: color.withValues(alpha: 0.1),
      labelStyle: TextStyle(
        color: color is MaterialColor ? color.shade700 : color,
      ),
    );
  }

  Widget _buildTestResultCard(String testName, dynamic result) {
    final status = _getTestStatus(result);
    final statusColor = _getStatusColor(status);
    final statusIcon = _getStatusIcon(status);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        leading: Icon(statusIcon, color: statusColor),
        title: Text(
          testName.replaceAll('_', ' ').toUpperCase(),
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          status,
          style: TextStyle(color: statusColor, fontWeight: FontWeight.w500),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildResultDetails(result),
          ),
        ],
      ),
    );
  }

  Widget _buildResultDetails(dynamic result) {
    if (result is Map<String, dynamic>) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: result.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 120,
                  child: Text(
                    '${entry.key}:',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
                Expanded(
                  child: Text(
                    entry.value.toString(),
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      );
    }
    
    return Text(
      result.toString(),
      style: const TextStyle(fontFamily: 'monospace'),
    );
  }

  String _getTestStatus(dynamic result) {
    if (result is Map<String, dynamic>) {
      return result['status']?.toString() ?? 'UNKNOWN';
    }
    return 'UNKNOWN';
  }

  Color _getStatusColor(String status) {
    switch (status.toUpperCase()) {
      case 'SUCCESS':
        return Colors.green;
      case 'FAILED':
      case 'ERROR':
        return Colors.red;
      case 'SKIPPED':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toUpperCase()) {
      case 'SUCCESS':
        return Icons.check_circle;
      case 'FAILED':
      case 'ERROR':
        return Icons.error;
      case 'SKIPPED':
        return Icons.skip_next;
      default:
        return Icons.help;
    }
  }

  void _copyResults() {
    if (_debugResults != null) {
      final jsonString = const JsonEncoder.withIndent('  ').convert(_debugResults);
      Clipboard.setData(ClipboardData(text: jsonString));
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Debug results copied to clipboard'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
