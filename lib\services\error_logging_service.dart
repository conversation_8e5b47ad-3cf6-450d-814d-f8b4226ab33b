import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import '../models/sync_result.dart';
import 'analytics_service.dart';

/// Service for comprehensive error logging and reporting.
///
/// This service provides centralized error logging functionality for the
/// Firebase Auth to Supabase sync system. It handles both sync-specific
/// errors and general application errors with local storage, remote logging,
/// and analytics integration.
///
/// Key Features:
/// - Local error storage with automatic size management
/// - Firebase Crashlytics integration for remote error tracking
/// - Comprehensive error statistics and analytics
/// - Export functionality for debugging and analysis
/// - Automatic cleanup of old error logs
/// - Device and app context information capture
///
/// Error Types Supported:
/// - Sync errors: Specific to Firebase-Supabase sync operations
/// - Application errors: General application-level errors
/// - Network errors: Connectivity and request failures
/// - Database errors: Supabase operation failures
/// - Validation errors: Data validation and format issues
///
/// Usage:
/// ```dart
/// final errorLogger = ErrorLoggingService();
///
/// // Log sync error
/// await errorLogger.logSyncError(
///   syncResult: failedSyncResult,
///   context: 'user_authentication',
///   additionalData: {'provider': 'google'},
/// );
///
/// // Log application error
/// await errorLogger.logApplicationError(
///   errorMessage: 'Failed to load user data',
///   context: 'profile_screen',
///   errorType: 'data_loading_error',
/// );
/// ```
class ErrorLoggingService {
  /// Singleton instance of the error logging service
  static final ErrorLoggingService _instance = ErrorLoggingService._internal();

  /// Factory constructor that returns the singleton instance
  factory ErrorLoggingService() => _instance;

  /// Private constructor for singleton pattern
  ErrorLoggingService._internal();

  static const String _errorLogsKey = 'error_logs';
  static const String _syncErrorsKey = 'sync_errors';
  static const int _maxLogEntries = 500;
  static const int _maxSyncErrors = 100;

  final AnalyticsService _analytics = AnalyticsService.instance;

  /// Log a sync error with comprehensive details
  Future<void> logSyncError({
    required SyncResult syncResult,
    required String context,
    Map<String, dynamic>? additionalData,
    StackTrace? stackTrace,
  }) async {
    try {
      final errorEntry = SyncErrorEntry(
        id: _generateErrorId(),
        timestamp: DateTime.now(),
        errorType: syncResult.errorType ?? SyncErrorType.unknownError,
        operationType: syncResult.operationType,
        errorMessage: syncResult.errorMessage ?? 'Unknown error',
        firebaseUid: syncResult.firebaseUid,
        context: context,
        additionalData: {
          ...?additionalData,
          'syncResult': syncResult.toJson(),
        },
        stackTrace: stackTrace?.toString(),
        deviceInfo: await _getDeviceInfo(),
        appVersion: await _getAppVersion(),
      );

      // Store locally
      await _storeSyncError(errorEntry);

      // Log to Crashlytics if available
      await _logToCrashlytics(errorEntry);

      // Track in analytics
      await _analytics.trackSyncErrorLogged(
        errorType: syncResult.errorType.toString(),
        operationType: syncResult.operationType.toString(),
        context: context,
        firebaseUid: syncResult.firebaseUid,
      );

    } catch (e) {
      print('Failed to log sync error: $e');
    }
  }

  /// Log a general application error
  Future<void> logApplicationError({
    required String errorMessage,
    required String context,
    String? errorType,
    Map<String, dynamic>? additionalData,
    StackTrace? stackTrace,
  }) async {
    try {
      final errorEntry = ApplicationErrorEntry(
        id: _generateErrorId(),
        timestamp: DateTime.now(),
        errorType: errorType ?? 'application_error',
        errorMessage: errorMessage,
        context: context,
        additionalData: additionalData ?? {},
        stackTrace: stackTrace?.toString(),
        deviceInfo: await _getDeviceInfo(),
        appVersion: await _getAppVersion(),
      );

      // Store locally
      await _storeApplicationError(errorEntry);

      // Log to Crashlytics if available
      await _logApplicationErrorToCrashlytics(errorEntry);

      // Track in analytics
      await _analytics.trackApplicationErrorLogged(
        errorType: errorType ?? 'application_error',
        context: context,
        errorMessage: errorMessage,
      );

    } catch (e) {
      print('Failed to log application error: $e');
    }
  }

  /// Get sync error history
  Future<List<SyncErrorEntry>> getSyncErrorHistory({int limit = 50}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorsJson = prefs.getString(_syncErrorsKey) ?? '[]';
      final errorsList = List<Map<String, dynamic>>.from(json.decode(errorsJson));

      return errorsList
          .take(limit)
          .map((data) => SyncErrorEntry.fromJson(data))
          .toList();
    } catch (e) {
      print('Error getting sync error history: $e');
      return [];
    }
  }

  /// Get application error history
  Future<List<ApplicationErrorEntry>> getApplicationErrorHistory({int limit = 50}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorsJson = prefs.getString(_errorLogsKey) ?? '[]';
      final errorsList = List<Map<String, dynamic>>.from(json.decode(errorsJson));

      return errorsList
          .take(limit)
          .map((data) => ApplicationErrorEntry.fromJson(data))
          .toList();
    } catch (e) {
      print('Error getting application error history: $e');
      return [];
    }
  }

  /// Get error statistics
  Future<ErrorStatistics> getErrorStatistics() async {
    try {
      final syncErrors = await getSyncErrorHistory(limit: _maxSyncErrors);
      final appErrors = await getApplicationErrorHistory(limit: _maxLogEntries);

      final now = DateTime.now();
      final last24Hours = now.subtract(const Duration(hours: 24));
      final lastWeek = now.subtract(const Duration(days: 7));

      // Count errors by time period
      final syncErrors24h = syncErrors.where((e) => e.timestamp.isAfter(last24Hours)).length;
      final syncErrorsWeek = syncErrors.where((e) => e.timestamp.isAfter(lastWeek)).length;
      final appErrors24h = appErrors.where((e) => e.timestamp.isAfter(last24Hours)).length;
      final appErrorsWeek = appErrors.where((e) => e.timestamp.isAfter(lastWeek)).length;

      // Count by error type
      final syncErrorsByType = <String, int>{};
      final appErrorsByType = <String, int>{};

      for (final error in syncErrors) {
        final type = error.errorType.toString();
        syncErrorsByType[type] = (syncErrorsByType[type] ?? 0) + 1;
      }

      for (final error in appErrors) {
        final type = error.errorType;
        appErrorsByType[type] = (appErrorsByType[type] ?? 0) + 1;
      }

      return ErrorStatistics(
        totalSyncErrors: syncErrors.length,
        totalApplicationErrors: appErrors.length,
        syncErrors24h: syncErrors24h,
        syncErrorsWeek: syncErrorsWeek,
        applicationErrors24h: appErrors24h,
        applicationErrorsWeek: appErrorsWeek,
        syncErrorsByType: syncErrorsByType,
        applicationErrorsByType: appErrorsByType,
        lastUpdated: now,
      );
    } catch (e) {
      print('Error getting error statistics: $e');
      return ErrorStatistics.empty();
    }
  }

  /// Clear old error logs
  Future<void> clearOldErrorLogs({Duration? olderThan}) async {
    try {
      final cutoffDate = DateTime.now().subtract(olderThan ?? const Duration(days: 30));
      
      // Clear sync errors
      final syncErrors = await getSyncErrorHistory(limit: _maxSyncErrors);
      final filteredSyncErrors = syncErrors
          .where((error) => error.timestamp.isAfter(cutoffDate))
          .toList();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_syncErrorsKey, 
          json.encode(filteredSyncErrors.map((e) => e.toJson()).toList()));

      // Clear application errors
      final appErrors = await getApplicationErrorHistory(limit: _maxLogEntries);
      final filteredAppErrors = appErrors
          .where((error) => error.timestamp.isAfter(cutoffDate))
          .toList();
      
      await prefs.setString(_errorLogsKey, 
          json.encode(filteredAppErrors.map((e) => e.toJson()).toList()));

    } catch (e) {
      print('Error clearing old error logs: $e');
    }
  }

  /// Export error logs for debugging
  Future<String> exportErrorLogs() async {
    try {
      final syncErrors = await getSyncErrorHistory(limit: _maxSyncErrors);
      final appErrors = await getApplicationErrorHistory(limit: _maxLogEntries);
      final statistics = await getErrorStatistics();

      final exportData = {
        'exportTimestamp': DateTime.now().toIso8601String(),
        'statistics': statistics.toJson(),
        'syncErrors': syncErrors.map((e) => e.toJson()).toList(),
        'applicationErrors': appErrors.map((e) => e.toJson()).toList(),
      };

      return json.encode(exportData);
    } catch (e) {
      print('Error exporting error logs: $e');
      return '{"error": "Failed to export error logs"}';
    }
  }

  /// Store sync error locally
  Future<void> _storeSyncError(SyncErrorEntry errorEntry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorsJson = prefs.getString(_syncErrorsKey) ?? '[]';
      final errorsList = List<Map<String, dynamic>>.from(json.decode(errorsJson));

      errorsList.insert(0, errorEntry.toJson());

      // Limit the number of stored errors
      if (errorsList.length > _maxSyncErrors) {
        errorsList.removeRange(_maxSyncErrors, errorsList.length);
      }

      await prefs.setString(_syncErrorsKey, json.encode(errorsList));
    } catch (e) {
      print('Error storing sync error: $e');
    }
  }

  /// Store application error locally
  Future<void> _storeApplicationError(ApplicationErrorEntry errorEntry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorsJson = prefs.getString(_errorLogsKey) ?? '[]';
      final errorsList = List<Map<String, dynamic>>.from(json.decode(errorsJson));

      errorsList.insert(0, errorEntry.toJson());

      // Limit the number of stored errors
      if (errorsList.length > _maxLogEntries) {
        errorsList.removeRange(_maxLogEntries, errorsList.length);
      }

      await prefs.setString(_errorLogsKey, json.encode(errorsList));
    } catch (e) {
      print('Error storing application error: $e');
    }
  }

  /// Log to Firebase Crashlytics (placeholder - requires firebase_crashlytics package)
  Future<void> _logToCrashlytics(SyncErrorEntry errorEntry) async {
    try {
      // TODO: Uncomment when firebase_crashlytics is added to dependencies
      // await FirebaseCrashlytics.instance.recordError(
      //   errorEntry.errorMessage,
      //   errorEntry.stackTrace != null ? StackTrace.fromString(errorEntry.stackTrace!) : null,
      //   information: [
      //     'Error Type: ${errorEntry.errorType}',
      //     'Operation Type: ${errorEntry.operationType}',
      //     'Context: ${errorEntry.context}',
      //     'Firebase UID: ${errorEntry.firebaseUid ?? 'N/A'}',
      //     'Additional Data: ${json.encode(errorEntry.additionalData)}',
      //   ],
      // );

      // For now, just print the error details
      print('Sync Error - ${errorEntry.errorType}: ${errorEntry.errorMessage}');
      print('Context: ${errorEntry.context}, UID: ${errorEntry.firebaseUid}');
    } catch (e) {
      print('Failed to log to Crashlytics: $e');
    }
  }

  /// Log application error to Firebase Crashlytics (placeholder)
  Future<void> _logApplicationErrorToCrashlytics(ApplicationErrorEntry errorEntry) async {
    try {
      // TODO: Uncomment when firebase_crashlytics is added to dependencies
      // await FirebaseCrashlytics.instance.recordError(
      //   errorEntry.errorMessage,
      //   errorEntry.stackTrace != null ? StackTrace.fromString(errorEntry.stackTrace!) : null,
      //   information: [
      //     'Error Type: ${errorEntry.errorType}',
      //     'Context: ${errorEntry.context}',
      //     'Additional Data: ${json.encode(errorEntry.additionalData)}',
      //   ],
      // );

      // For now, just print the error details
      print('App Error - ${errorEntry.errorType}: ${errorEntry.errorMessage}');
      print('Context: ${errorEntry.context}');
    } catch (e) {
      print('Failed to log application error to Crashlytics: $e');
    }
  }

  /// Generate unique error ID
  String _generateErrorId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// Get device information
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      return {
        'platform': Platform.operatingSystem,
        'version': Platform.operatingSystemVersion,
        'locale': Platform.localeName,
      };
    } catch (e) {
      return {'error': 'Failed to get device info'};
    }
  }

  /// Get app version
  Future<String> _getAppVersion() async {
    try {
      // This would typically come from package_info_plus
      return '1.0.0'; // Placeholder
    } catch (e) {
      return 'unknown';
    }
  }
}

/// Sync error entry model
class SyncErrorEntry {
  final String id;
  final DateTime timestamp;
  final SyncErrorType errorType;
  final SyncOperationType operationType;
  final String errorMessage;
  final String? firebaseUid;
  final String context;
  final Map<String, dynamic> additionalData;
  final String? stackTrace;
  final Map<String, dynamic> deviceInfo;
  final String appVersion;

  SyncErrorEntry({
    required this.id,
    required this.timestamp,
    required this.errorType,
    required this.operationType,
    required this.errorMessage,
    this.firebaseUid,
    required this.context,
    required this.additionalData,
    this.stackTrace,
    required this.deviceInfo,
    required this.appVersion,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'errorType': errorType.toString(),
      'operationType': operationType.toString(),
      'errorMessage': errorMessage,
      'firebaseUid': firebaseUid,
      'context': context,
      'additionalData': additionalData,
      'stackTrace': stackTrace,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
    };
  }

  factory SyncErrorEntry.fromJson(Map<String, dynamic> json) {
    return SyncErrorEntry(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      errorType: SyncErrorType.values.firstWhere(
        (e) => e.toString() == json['errorType'],
        orElse: () => SyncErrorType.unknownError,
      ),
      operationType: SyncOperationType.values.firstWhere(
        (e) => e.toString() == json['operationType'],
        orElse: () => SyncOperationType.dataSync,
      ),
      errorMessage: json['errorMessage'],
      firebaseUid: json['firebaseUid'],
      context: json['context'],
      additionalData: Map<String, dynamic>.from(json['additionalData'] ?? {}),
      stackTrace: json['stackTrace'],
      deviceInfo: Map<String, dynamic>.from(json['deviceInfo'] ?? {}),
      appVersion: json['appVersion'] ?? 'unknown',
    );
  }
}

/// Application error entry model
class ApplicationErrorEntry {
  final String id;
  final DateTime timestamp;
  final String errorType;
  final String errorMessage;
  final String context;
  final Map<String, dynamic> additionalData;
  final String? stackTrace;
  final Map<String, dynamic> deviceInfo;
  final String appVersion;

  ApplicationErrorEntry({
    required this.id,
    required this.timestamp,
    required this.errorType,
    required this.errorMessage,
    required this.context,
    required this.additionalData,
    this.stackTrace,
    required this.deviceInfo,
    required this.appVersion,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'errorType': errorType,
      'errorMessage': errorMessage,
      'context': context,
      'additionalData': additionalData,
      'stackTrace': stackTrace,
      'deviceInfo': deviceInfo,
      'appVersion': appVersion,
    };
  }

  factory ApplicationErrorEntry.fromJson(Map<String, dynamic> json) {
    return ApplicationErrorEntry(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      errorType: json['errorType'],
      errorMessage: json['errorMessage'],
      context: json['context'],
      additionalData: Map<String, dynamic>.from(json['additionalData'] ?? {}),
      stackTrace: json['stackTrace'],
      deviceInfo: Map<String, dynamic>.from(json['deviceInfo'] ?? {}),
      appVersion: json['appVersion'] ?? 'unknown',
    );
  }
}

/// Error statistics model
class ErrorStatistics {
  final int totalSyncErrors;
  final int totalApplicationErrors;
  final int syncErrors24h;
  final int syncErrorsWeek;
  final int applicationErrors24h;
  final int applicationErrorsWeek;
  final Map<String, int> syncErrorsByType;
  final Map<String, int> applicationErrorsByType;
  final DateTime lastUpdated;

  ErrorStatistics({
    required this.totalSyncErrors,
    required this.totalApplicationErrors,
    required this.syncErrors24h,
    required this.syncErrorsWeek,
    required this.applicationErrors24h,
    required this.applicationErrorsWeek,
    required this.syncErrorsByType,
    required this.applicationErrorsByType,
    required this.lastUpdated,
  });

  factory ErrorStatistics.empty() {
    return ErrorStatistics(
      totalSyncErrors: 0,
      totalApplicationErrors: 0,
      syncErrors24h: 0,
      syncErrorsWeek: 0,
      applicationErrors24h: 0,
      applicationErrorsWeek: 0,
      syncErrorsByType: {},
      applicationErrorsByType: {},
      lastUpdated: DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalSyncErrors': totalSyncErrors,
      'totalApplicationErrors': totalApplicationErrors,
      'syncErrors24h': syncErrors24h,
      'syncErrorsWeek': syncErrorsWeek,
      'applicationErrors24h': applicationErrors24h,
      'applicationErrorsWeek': applicationErrorsWeek,
      'syncErrorsByType': syncErrorsByType,
      'applicationErrorsByType': applicationErrorsByType,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}
