import 'package:flutter/material.dart';
import '../../utils/animation_utils.dart';

class AuthButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;
  final Color? backgroundColor;
  final Color? textColor;
  final Widget? icon;
  final bool isOutlined;

  const AuthButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isOutlined = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Use hardcoded colors to prevent dark mode adaptation
    final effectiveBackgroundColor = backgroundColor ??
        (isOutlined ? Colors.transparent : const Color(0xFFe92933)); // AAI red
    final effectiveTextColor = textColor ??
        (isOutlined ? const Color(0xFFe92933) : Colors.white);

    return AnimatedButton(
      onPressed: (isEnabled && !isLoading) ? (onPressed ?? () {}) : () {},
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          gradient: !isOutlined && isEnabled ? LinearGradient(
            colors: [
              effectiveBackgroundColor,
              effectiveBackgroundColor.withValues(alpha: 0.8),
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ) : null,
          color: isOutlined || !isEnabled ? effectiveBackgroundColor : null,
          borderRadius: BorderRadius.circular(8),
          border: isOutlined ? Border.all(
            color: Colors.grey,
            width: 1.5,
          ) : null,
          boxShadow: !isOutlined && isEnabled ? [
            BoxShadow(
              color: effectiveBackgroundColor.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ] : null,
        ),
        child: Center(
          child: isLoading
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(effectiveTextColor),
                  ),
                )
              : icon != null
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          icon!,
                          const SizedBox(width: 12),
                          Text(
                            text,
                            style: theme.textTheme.labelLarge?.copyWith(
                              color: effectiveTextColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    )
                  : Text(
                      text,
                      style: theme.textTheme.labelLarge?.copyWith(
                        color: effectiveTextColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
        ),
      ),
    );
  }
}

class SocialAuthButton extends StatelessWidget {
  final String text;
  final String iconPath;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isEnabled;

  const SocialAuthButton({
    super.key,
    required this.text,
    required this.iconPath,
    this.onPressed,
    this.isLoading = false,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedButton(
      onPressed: (isEnabled && !isLoading) ? (onPressed ?? () {}) : () {},
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colorScheme.outline,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: isLoading
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      _buildSocialIcon(iconPath, colorScheme),
                      const SizedBox(width: 12),
                      Text(
                        text,
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: colorScheme.onSurface,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildSocialIcon(String iconPath, ColorScheme colorScheme) {
    switch (iconPath) {
      case 'google':
        return SizedBox(
          width: 24,
          height: 24,
          child: Image.asset(
            'assets/logo/google-logo.webp',
            width: 24,
            height: 24,
            fit: BoxFit.contain,
          ),
        );
      case 'apple':
        return Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: colorScheme.onSurface.withValues(alpha: 0.1),
          ),
          child: Icon(
            Icons.apple,
            size: 20,
            color: colorScheme.onSurface,
          ),
        );
      case 'facebook':
        return Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.blue.withValues(alpha: 0.1),
          ),
          child: Icon(
            Icons.facebook,
            size: 20,
            color: Colors.blue,
          ),
        );
      default:
        return Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: colorScheme.primary.withValues(alpha: 0.1),
          ),
          child: Icon(
            Icons.login,
            size: 20,
            color: colorScheme.primary,
          ),
        );
    }
  }
}

class AuthTextButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isEnabled;
  final Color? textColor;

  const AuthTextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isEnabled = true,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      onTap: isEnabled ? onPressed : null,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Text(
          text,
          style: theme.textTheme.labelLarge?.copyWith(
            color: textColor ?? colorScheme.primary,
            fontWeight: FontWeight.w500,
            decoration: TextDecoration.underline,
            decorationColor: textColor ?? colorScheme.primary,
          ),
        ),
      ),
    );
  }
}

class OtpInputField extends StatefulWidget {
  final Function(String) onCompleted;
  final int length;
  final bool hasError;

  const OtpInputField({
    super.key,
    required this.onCompleted,
    this.length = 6,
    this.hasError = false,
  });

  @override
  State<OtpInputField> createState() => _OtpInputFieldState();
}

class _OtpInputFieldState extends State<OtpInputField> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(widget.length, (index) => TextEditingController());
    _focusNodes = List.generate(widget.length, (index) => FocusNode());
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(widget.length, (index) {
        return SizedBox(
          width: 48,
          height: 56,
          child: TextField(
            controller: _controllers[index],
            focusNode: _focusNodes[index],
            textAlign: TextAlign.center,
            keyboardType: TextInputType.number,
            maxLength: 1,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            decoration: InputDecoration(
              counterText: '',
              filled: true,
              fillColor: colorScheme.surface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: widget.hasError ? colorScheme.error : colorScheme.outline,
                  width: 1.5,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: widget.hasError ? colorScheme.error : colorScheme.outline,
                  width: 1.5,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: widget.hasError ? colorScheme.error : colorScheme.primary,
                  width: 2,
                ),
              ),
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                if (index < widget.length - 1) {
                  _focusNodes[index + 1].requestFocus();
                } else {
                  _focusNodes[index].unfocus();
                }
              } else if (value.isEmpty && index > 0) {
                _focusNodes[index - 1].requestFocus();
              }

              // Check if all fields are filled
              final otp = _controllers.map((c) => c.text).join();
              if (otp.length == widget.length) {
                widget.onCompleted(otp);
              }
            },
          ),
        );
      }),
    );
  }
}
