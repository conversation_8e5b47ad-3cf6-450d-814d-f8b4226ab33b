import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:aai/providers/profile_provider.dart';
import 'package:aai/services/profile_update_service.dart';
import 'package:aai/models/user_profile.dart';
import 'package:aai/utils/result.dart';

import 'profile_provider_test.mocks.dart';

@GenerateMocks([ProfileUpdateService, FirebaseAuth, User])
void main() {
  group('ProfileProvider', () {
    late ProviderContainer container;
    late MockProfileUpdateService mockProfileService;
    late MockFirebaseAuth mockAuth;
    late MockUser mockUser;

    setUp(() {
      mockProfileService = MockProfileUpdateService();
      mockAuth = MockFirebaseAuth();
      mockUser = MockUser();

      // Setup auth mock
      when(mockAuth.authStateChanges()).thenAnswer((_) => Stream.value(mockUser));
      when(mockUser.uid).thenReturn('test-uid');

      container = ProviderContainer(
        overrides: [
          // Override the profile service with our mock
          profileProvider.overrideWith((ref) => ProfileNotifier(mockProfileService, mockAuth)),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Optimistic Updates and Sync Version', () {
      test('should increment sync version in optimistic update', () async {
        // Arrange
        final initialProfile = UserProfile(
          id: 'test-id',
          firebaseUid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          syncVersion: 5,
        );

        final updateRequest = ProfileUpdateRequest(
          displayName: 'Updated Name',
          syncVersion: 5,
        );

        final updatedProfile = UserProfile(
          id: 'test-id',
          firebaseUid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Updated Name',
          syncVersion: 6, // Server increments sync version
        );

        // Mock successful profile load
        when(mockProfileService.getProfile())
            .thenAnswer((_) async => Result.success(initialProfile));

        // Mock successful profile update
        when(mockProfileService.updateProfile(any))
            .thenAnswer((_) async => Result.success(
              ProfileUpdateResult.success(
                profile: updatedProfile,
                message: 'Profile updated successfully',
                newSyncVersion: 6,
                completionPercentage: 50,
              ),
            ));

        // Act
        final notifier = container.read(profileProvider.notifier);
        
        // Load initial profile
        await notifier.loadProfile();
        
        // Verify initial state
        var state = container.read(profileProvider);
        expect(state.profile?.syncVersion, equals(5));
        expect(state.profile?.displayName, equals('Test User'));

        // Perform update with optimistic update enabled
        final success = await notifier.updateProfile(updateRequest, optimisticUpdate: true);

        // Assert
        expect(success, isTrue);
        
        // Verify final state has correct sync version
        state = container.read(profileProvider);
        expect(state.profile?.syncVersion, equals(6));
        expect(state.profile?.displayName, equals('Updated Name'));
      });

      test('should handle consecutive updates with correct sync versions', () async {
        // Arrange
        final initialProfile = UserProfile(
          id: 'test-id',
          firebaseUid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          syncVersion: 10,
        );

        // Mock successful profile load
        when(mockProfileService.getProfile())
            .thenAnswer((_) async => Result.success(initialProfile));

        // Mock first update
        when(mockProfileService.updateProfile(argThat(
          predicate<ProfileUpdateRequest>((req) => req.syncVersion == 10)
        ))).thenAnswer((_) async => Result.success(
          ProfileUpdateResult.success(
            profile: initialProfile.copyWith(
              displayName: 'First Update',
              syncVersion: 11,
            ),
            message: 'Profile updated successfully',
            newSyncVersion: 11,
            completionPercentage: 50,
          ),
        ));

        // Mock second update
        when(mockProfileService.updateProfile(argThat(
          predicate<ProfileUpdateRequest>((req) => req.syncVersion == 11)
        ))).thenAnswer((_) async => Result.success(
          ProfileUpdateResult.success(
            profile: initialProfile.copyWith(
              displayName: 'Second Update',
              syncVersion: 12,
            ),
            message: 'Profile updated successfully',
            newSyncVersion: 12,
            completionPercentage: 50,
          ),
        ));

        // Act
        final notifier = container.read(profileProvider.notifier);
        
        // Load initial profile
        await notifier.loadProfile();
        
        // First update
        final firstRequest = ProfileUpdateRequest(
          displayName: 'First Update',
          syncVersion: 10,
        );
        final firstSuccess = await notifier.updateProfile(firstRequest);
        
        // Second update (this should use sync version 11, not 10)
        var currentState = container.read(profileProvider);
        final secondRequest = ProfileUpdateRequest(
          displayName: 'Second Update',
          syncVersion: currentState.profile!.syncVersion, // Should be 11
        );
        final secondSuccess = await notifier.updateProfile(secondRequest);

        // Assert
        expect(firstSuccess, isTrue);
        expect(secondSuccess, isTrue);
        
        // Verify both service calls were made with correct sync versions
        verify(mockProfileService.updateProfile(argThat(
          predicate<ProfileUpdateRequest>((req) => req.syncVersion == 10)
        ))).called(1);
        
        verify(mockProfileService.updateProfile(argThat(
          predicate<ProfileUpdateRequest>((req) => req.syncVersion == 11)
        ))).called(1);
        
        // Verify final state
        final finalState = container.read(profileProvider);
        expect(finalState.profile?.syncVersion, equals(12));
        expect(finalState.profile?.displayName, equals('Second Update'));
      });

      test('should revert optimistic update on failure', () async {
        // Arrange
        final initialProfile = UserProfile(
          id: 'test-id',
          firebaseUid: 'test-uid',
          email: '<EMAIL>',
          displayName: 'Test User',
          syncVersion: 5,
        );

        final updateRequest = ProfileUpdateRequest(
          displayName: 'Failed Update',
          syncVersion: 5,
        );

        // Mock successful profile load
        when(mockProfileService.getProfile())
            .thenAnswer((_) async => Result.success(initialProfile));

        // Mock failed profile update
        when(mockProfileService.updateProfile(any))
            .thenAnswer((_) async => Result.success(
              ProfileUpdateResult.error(
                error: 'Update failed',
                message: 'Sync version conflict',
                errorCode: 'SYNC_CONFLICT',
                currentVersion: 6,
                providedVersion: 5,
              ),
            ));

        // Act
        final notifier = container.read(profileProvider.notifier);
        
        // Load initial profile
        await notifier.loadProfile();
        
        // Verify initial state
        var state = container.read(profileProvider);
        expect(state.profile?.syncVersion, equals(5));
        expect(state.profile?.displayName, equals('Test User'));

        // Perform update with optimistic update enabled
        final success = await notifier.updateProfile(updateRequest, optimisticUpdate: true);

        // Assert
        expect(success, isFalse);
        
        // Verify state was reverted (profile reloaded)
        state = container.read(profileProvider);
        expect(state.profile?.syncVersion, equals(5)); // Reverted to original
        expect(state.profile?.displayName, equals('Test User')); // Reverted to original
        expect(state.error, isNotNull);
      });
    });
  });
}
