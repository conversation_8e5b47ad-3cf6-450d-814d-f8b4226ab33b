-- Migration: Update subscription plans from "basic" to "Free"
-- Created: 2024-08-03
-- Description: Renames "basic" subscription plan to "Free" and updates existing users

-- Step 1: Add "Free" to the CHECK constraint (keeping "basic" temporarily for migration)
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS profiles_subscription_plan_check;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_subscription_plan_check 
CHECK (subscription_plan IN ('Free', 'basic', 'pro', 'enterprise'));

-- Step 2: Update existing users with "basic" plan to "Free" plan
UPDATE profiles 
SET 
    subscription_plan = 'Free',
    updated_at = NOW()
WHERE subscription_plan = 'basic';

-- Step 3: Update the default value for new users
ALTER TABLE profiles 
ALTER COLUMN subscription_plan SET DEFAULT 'Free';

-- Step 4: Remove "basic" from CHECK constraint (final cleanup)
ALTER TABLE profiles 
DROP CONSTRAINT profiles_subscription_plan_check;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_subscription_plan_check 
CHECK (subscription_plan IN ('Free', 'pro', 'enterprise'));

-- Step 5: Update table comments to reflect the change
COMMENT ON COLUMN profiles.subscription_plan IS 'Current subscription tier: Free (default), pro, or enterprise';

-- Step 6: Create function to get subscription plan statistics
CREATE OR REPLACE FUNCTION get_subscription_plan_stats()
RETURNS TABLE (
    plan_name TEXT,
    user_count BIGINT,
    percentage NUMERIC(5,2)
) AS $$
BEGIN
    RETURN QUERY
    WITH plan_counts AS (
        SELECT 
            subscription_plan,
            COUNT(*) as count
        FROM profiles 
        WHERE deleted_at IS NULL
        GROUP BY subscription_plan
    ),
    total_users AS (
        SELECT SUM(count) as total FROM plan_counts
    )
    SELECT 
        pc.subscription_plan::TEXT,
        pc.count,
        ROUND((pc.count * 100.0 / tu.total), 2) as percentage
    FROM plan_counts pc
    CROSS JOIN total_users tu
    ORDER BY pc.count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_subscription_plan_stats() TO service_role;

-- Add index for efficient subscription plan queries
CREATE INDEX IF NOT EXISTS idx_profiles_subscription_plan 
ON profiles(subscription_plan) 
WHERE deleted_at IS NULL;

-- Log the migration completion
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO updated_count 
    FROM profiles 
    WHERE subscription_plan = 'Free';
    
    RAISE NOTICE 'Migration completed: % users now have "Free" subscription plan', updated_count;
END $$;
