import 'package:flutter/material.dart';

import '../services/sync_monitoring_service.dart';
import '../services/manual_sync_service.dart';
import '../services/error_logging_service.dart';

/// Widget for displaying sync health dashboard
class SyncHealthDashboard extends StatefulWidget {
  const SyncHealthDashboard({super.key});

  @override
  State<SyncHealthDashboard> createState() => _SyncHealthDashboardState();
}

class _SyncHealthDashboardState extends State<SyncHealthDashboard> {
  final SyncMonitoringService _monitoringService = SyncMonitoringService();
  final ManualSyncService _manualSyncService = ManualSyncService();
  final ErrorLoggingService _errorLoggingService = ErrorLoggingService();

  SyncHealthReport? _healthReport;
  SyncStatusSummary? _statusSummary;
  ErrorStatistics? _errorStats;
  bool _isLoading = true;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final results = await Future.wait([
        _monitoringService.getCurrentHealth(),
        _manualSyncService.getSyncStatusSummary(),
        _errorLoggingService.getErrorStatistics(),
      ]);

      setState(() {
        _healthReport = results[0] as SyncHealthReport;
        _statusSummary = results[1] as SyncStatusSummary;
        _errorStats = results[2] as ErrorStatistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Failed to load dashboard data: $e');
    }
  }

  Future<void> _refreshDashboard() async {
    setState(() {
      _isRefreshing = true;
    });

    await _loadDashboardData();

    setState(() {
      _isRefreshing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sync Health Dashboard'),
        actions: [
          IconButton(
            icon: _isRefreshing 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
            onPressed: _isRefreshing ? null : _refreshDashboard,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _refreshDashboard,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOverallHealthCard(),
                    const SizedBox(height: 16),
                    _buildSyncStatusCard(),
                    const SizedBox(height: 16),
                    _buildErrorStatisticsCard(),
                    const SizedBox(height: 16),
                    _buildActiveAlertsCard(),
                    const SizedBox(height: 16),
                    _buildQuickActionsCard(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOverallHealthCard() {
    if (_healthReport == null) return const SizedBox.shrink();

    final health = _healthReport!.overallHealth;
    Color healthColor;
    IconData healthIcon;
    String healthText;

    switch (health) {
      case SyncHealthStatus.healthy:
        healthColor = Colors.green;
        healthIcon = Icons.check_circle;
        healthText = 'Healthy';
        break;
      case SyncHealthStatus.degraded:
        healthColor = Colors.orange;
        healthIcon = Icons.warning;
        healthText = 'Degraded';
        break;
      case SyncHealthStatus.critical:
        healthColor = Colors.red;
        healthIcon = Icons.error;
        healthText = 'Critical';
        break;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(healthIcon, color: healthColor, size: 32),
                const SizedBox(width: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Overall Health',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Text(
                      healthText,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: healthColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildMetricItem(
                  'Success Rate',
                  '${(_healthReport!.syncSuccessRate * 100).toStringAsFixed(1)}%',
                  Icons.trending_up,
                ),
                _buildMetricItem(
                  'Error Rate',
                  '${(_healthReport!.errorRate * 100).toStringAsFixed(1)}%',
                  Icons.trending_down,
                ),
                _buildMetricItem(
                  'Last Check',
                  _formatTimestamp(_healthReport!.timestamp),
                  Icons.access_time,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncStatusCard() {
    if (_statusSummary == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sync Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusItem(
                  'Queue Size',
                  _statusSummary!.queueSize.toString(),
                  Icons.queue,
                  _statusSummary!.queueSize > 50 ? Colors.orange : Colors.blue,
                ),
                _buildStatusItem(
                  'Failed Syncs',
                  _statusSummary!.failedSyncsCount.toString(),
                  Icons.error_outline,
                  _statusSummary!.failedSyncsCount > 10 ? Colors.red : Colors.grey,
                ),
                _buildStatusItem(
                  'Need Retry',
                  _statusSummary!.profilesNeedingRetryCount.toString(),
                  Icons.refresh,
                  _statusSummary!.profilesNeedingRetryCount > 20 ? Colors.orange : Colors.grey,
                ),
              ],
            ),
            if (_statusSummary!.isQueueProcessing) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Queue is currently processing...',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorStatisticsCard() {
    if (_errorStats == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Error Statistics',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildErrorMetric(
                  'Last 24h',
                  '${_errorStats!.syncErrors24h + _errorStats!.applicationErrors24h}',
                  Icons.today,
                ),
                _buildErrorMetric(
                  'Last Week',
                  '${_errorStats!.syncErrorsWeek + _errorStats!.applicationErrorsWeek}',
                  Icons.date_range,
                ),
                _buildErrorMetric(
                  'Total',
                  '${_errorStats!.totalSyncErrors + _errorStats!.totalApplicationErrors}',
                  Icons.error,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActiveAlertsCard() {
    if (_healthReport == null || _healthReport!.alerts.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 12),
              Text(
                'No Active Alerts',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Active Alerts (${_healthReport!.alerts.length})',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            ...(_healthReport!.alerts.take(3).map((alert) => _buildAlertItem(alert))),
            if (_healthReport!.alerts.length > 3)
              TextButton(
                onPressed: () => _showAllAlerts(),
                child: Text('View all ${_healthReport!.alerts.length} alerts'),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _syncCurrentUser,
                  icon: const Icon(Icons.sync),
                  label: const Text('Sync Current User'),
                ),
                ElevatedButton.icon(
                  onPressed: _processQueue,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Process Queue'),
                ),
                ElevatedButton.icon(
                  onPressed: _viewErrorLogs,
                  icon: const Icon(Icons.bug_report),
                  label: const Text('View Error Logs'),
                ),
                ElevatedButton.icon(
                  onPressed: _exportLogs,
                  icon: const Icon(Icons.download),
                  label: const Text('Export Logs'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildStatusItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, size: 24, color: color),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: color),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildErrorMetric(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Colors.red[300]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildAlertItem(SyncHealthAlert alert) {
    Color alertColor;
    IconData alertIcon;

    switch (alert.severity) {
      case SyncAlertSeverity.info:
        alertColor = Colors.blue;
        alertIcon = Icons.info;
        break;
      case SyncAlertSeverity.warning:
        alertColor = Colors.orange;
        alertIcon = Icons.warning;
        break;
      case SyncAlertSeverity.critical:
        alertColor = Colors.red;
        alertIcon = Icons.error;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: alertColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: alertColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(alertIcon, color: alertColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  alert.message,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  _formatTimestamp(alert.timestamp),
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Future<void> _syncCurrentUser() async {
    try {
      final result = await _manualSyncService.syncCurrentUser();
      if (result.isSuccess) {
        _showSuccessSnackBar('User sync completed successfully');
      } else {
        _showErrorSnackBar('User sync failed: ${result.errorMessage}');
      }
      await _refreshDashboard();
    } catch (e) {
      _showErrorSnackBar('Sync failed: $e');
    }
  }

  Future<void> _processQueue() async {
    try {
      final result = await _manualSyncService.processQueueManually();
      if (result.isSuccess) {
        _showSuccessSnackBar('Queue processed: ${result.processedCount} items');
      } else {
        _showErrorSnackBar('Queue processing failed: ${result.message}');
      }
      await _refreshDashboard();
    } catch (e) {
      _showErrorSnackBar('Queue processing failed: $e');
    }
  }

  void _viewErrorLogs() {
    // Navigate to error logs screen
    // This would be implemented based on your navigation structure
    _showInfoSnackBar('Error logs screen not implemented yet');
  }

  Future<void> _exportLogs() async {
    try {
      final logs = await _errorLoggingService.exportErrorLogs();
      // Handle the exported logs (save to file, share, etc.)
      _showSuccessSnackBar('Logs exported successfully');
    } catch (e) {
      _showErrorSnackBar('Export failed: $e');
    }
  }

  void _showAllAlerts() {
    // Show dialog or navigate to alerts screen
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('All Active Alerts'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: _healthReport!.alerts.length,
            itemBuilder: (context, index) => _buildAlertItem(_healthReport!.alerts[index]),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
