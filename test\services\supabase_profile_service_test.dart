import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

import 'package:aai/services/supabase_profile_service.dart';

// Generate mocks
@GenerateMocks([
  firebase_auth.User,
])
import 'supabase_profile_service_test.mocks.dart';

void main() {
  group('SupabaseProfileService', () {
    late SupabaseProfileService service;
    late MockUser mockFirebaseUser;

    setUp(() {
      mockFirebaseUser = MockUser();
      service = SupabaseProfileService();
    });

    group('validateFirebaseUserForSync', () {
      test('should return true for valid Firebase user with uid', () {
        // Arrange
        when(mockFirebaseUser.uid).thenReturn('valid-uid-123');

        // Act
        final isValid = service.validateFirebaseUserForSync(mockFirebaseUser);

        // Assert
        expect(isValid, true);
      });

      test('should return false for user with empty uid', () {
        // Arrange
        when(mockFirebaseUser.uid).thenReturn('');

        // Act
        final isValid = service.validateFirebaseUserForSync(mockFirebaseUser);

        // Assert
        expect(isValid, false);
      });

      test('should return false for user with null uid', () {
        // Arrange
        when(mockFirebaseUser.uid).thenReturn('');

        // Act
        final isValid = service.validateFirebaseUserForSync(mockFirebaseUser);

        // Assert
        expect(isValid, false);
      });

    });
  });
}
