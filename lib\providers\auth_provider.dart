import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/auth_state.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/session_manager.dart';
import '../services/email_verification_service.dart';
import '../services/onboarding_service.dart';
import '../services/firebase_supabase_sync_service.dart';
import '../services/analytics_service.dart';

// Auth Service Provider
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService.instance;
});

// Session Manager Provider
final sessionManagerProvider = Provider<SessionManager>((ref) {
  return SessionManager.instance;
});

// Email Verification Service Provider
final emailVerificationServiceProvider = Provider<EmailVerificationService>((ref) {
  return EmailVerificationService();
});

// Auth State Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier(this._authService, this._sessionManager, this._emailVerificationService) : super(const AuthState.initial()) {
    _initialize();
  }

  final AuthService _authService;
  final SessionManager _sessionManager;
  final EmailVerificationService _emailVerificationService;
  final FirebaseSupabaseSyncService _syncService = FirebaseSupabaseSyncService.instance;
  final AnalyticsService _analytics = AnalyticsService.instance;
  StreamSubscription? _authSubscription;
  Timer? _verificationCheckTimer;

  Future<void> _initialize() async {

    state = const AuthState.loading();

    try {
      // Check for existing session first
      final sessionUser = await _sessionManager.getCurrentUserSession();
      if (sessionUser != null) {
        // Found existing session, authenticating user
        state = AuthState.authenticated(sessionUser);
        return;
      }

      // Check Firebase auth state
      final firebaseUser = _authService.currentFirebaseUser;
      if (firebaseUser != null && firebaseUser.emailVerified) {
        // Found verified Firebase user, creating session
        final appUser = AppUser.fromFirebaseUser(firebaseUser);
        await _sessionManager.saveUserSession(appUser);
        state = AuthState.authenticated(appUser);
        return;
      }

      // No existing session or Firebase user - set to unauthenticated
      print('DEBUG: No existing session, setting to unauthenticated');
      state = const AuthState.unauthenticated();

      // Listen to Firebase auth state changes with email verification detection
      _authSubscription = _authService.firebaseAuthStateChanges.listen(
        (firebaseUser) async {



          if (firebaseUser != null && firebaseUser.emailVerified) {
            // User exists and email is verified - authenticate them
            // User email verified, authenticating automatically
            try {
              final appUser = AppUser.fromFirebaseUser(firebaseUser);
              await _sessionManager.saveUserSession(appUser);

              // Sync user profile to Supabase after automatic authentication
              try {
                await _syncService.syncUserToSupabase(firebaseUser);
              } catch (syncError) {
                print('Profile sync failed during automatic authentication: $syncError');
                // Don't fail authentication if sync fails
              }

              state = AuthState.authenticated(appUser);
              print('DEBUG: User authenticated after email verification');
            } catch (e) {
              print('DEBUG: Error authenticating verified user: $e');
              // Don't set error state here - user is in verification flow
            }
          } else if (firebaseUser != null && !firebaseUser.emailVerified) {
            // User exists but email not verified - keep in verification pending state
            print('DEBUG: User exists but email not verified, staying in verification pending');
            if (state.status != AuthStatus.verificationPending) {
              state = const AuthState.verificationPending();
            }
          } else if (firebaseUser == null) {
            // No user - clear session and set unauthenticated
            print('DEBUG: No Firebase user, clearing session and setting unauthenticated');
            await _sessionManager.clearSession();
            state = const AuthState.unauthenticated();
          }
        },
        // Removed onError handler - errors should be handled in individual auth methods
      );
    } catch (e) {
      print('DEBUG: Error in auth initialization: $e');
      state = AuthState.error(e.toString());
    }
  }

  // Email/Password Sign In
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    if (await _sessionManager.isAccountLocked(email)) {
      final remainingTime = await _sessionManager.getRemainingLockoutTime();
      if (remainingTime != null) {
        final minutes = remainingTime.inMinutes;
        state = AuthState.error('Account locked. Try again in $minutes minutes.');
        return;
      }
    }

    state = const AuthState.loading();

    try {
      final user = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Check if email is verified before saving session
      if (user.isEmailVerified) {
        await _sessionManager.saveUserSession(user, email);

        // Sync user profile to Supabase after successful authentication
        try {
          final firebaseUser = _authService.currentFirebaseUser;
          if (firebaseUser != null) {
            await _syncService.syncUserToSupabase(firebaseUser);
          }
        } catch (syncError) {
          print('Profile sync failed during sign-in: $syncError');
          // Don't fail authentication if sync fails
        }

        state = AuthState.authenticated(user);
      } else {
        // Email not verified - let Firebase auth state listener handle this
        // Don't set state here, the listener will set it to verificationPending
        print('DEBUG: Login successful but email not verified, letting auth state listener handle');
        _startVerificationChecking();
      }
    } catch (e) {
      // Extract proper error message from AuthException
      String errorMessage;
      String? errorCode;
      if (e is AuthException) {
        errorMessage = e.message;
        errorCode = e.code;
      } else {
        errorMessage = e.toString();
      }

      // Only record failed attempts for wrong password (registered accounts)
      // Don't count attempts for non-registered emails (user-not-found)
      if (errorCode == 'wrong-password') {
        await _sessionManager.recordFailedLoginAttempt(email);
        final remainingAttempts = await _sessionManager.getRemainingLoginAttempts(email);

        if (remainingAttempts > 0) {
          errorMessage += ' ($remainingAttempts attempts remaining)';
        }
      }

      state = AuthState.error(errorMessage);
    }
  }

  // Email/Password Registration with Firebase email verification
  Future<void> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    print('DEBUG: Starting createUserWithEmailAndPassword for $email');
    state = const AuthState.loading();

    try {
      print('DEBUG: Creating user account with Firebase email verification...');
      // Create user account and send Firebase email verification
      await _authService.createUserAccountWithEmailVerification(
        email: email,
        password: password,
        displayName: displayName,
      );

      print('DEBUG: User account created and verification email sent');
      // Set state to verification pending and start periodic checking
      state = const AuthState.verificationPending();
      _startVerificationChecking();
      print('DEBUG: State set to verificationPending and started verification checking');
    } catch (e) {
      print('DEBUG: Error occurred: $e');

      // Handle email already in use - redirect to verification
      if (e.toString().contains('email-already-in-use') ||
          e.toString().contains('Account exists but may need verification')) {
        print('DEBUG: Email already exists, redirecting to verification');
        // Set state to verification pending and start checking
        state = const AuthState.verificationPending();
        _startVerificationChecking();
        print('DEBUG: Redirected to verification for existing account');
      } else if (e.toString().contains('account-verified')) {
        state = AuthState.error('Account already exists and is verified. Please sign in.');
      } else {
        state = AuthState.error(e.toString());
      }
    }
  }

  // Google Sign In
  Future<void> signInWithGoogle() async {
    state = const AuthState.loading();

    try {
      final user = await _authService.signInWithGoogle();
      await _sessionManager.saveUserSession(user);

      // Track successful Google authentication
      await _analytics.trackAuthenticationMethod(
        authProvider: 'google',
        authAction: 'sign_in',
        isSuccess: true,
      );

      // Sync user profile to Supabase after successful Google authentication
      try {
        final firebaseUser = _authService.currentFirebaseUser;
        if (firebaseUser != null) {
          final syncResult = await _syncService.syncUserToSupabase(firebaseUser);

          // If sync fails, try retry mechanism
          if (!syncResult.isSuccess) {
            print('Initial sync failed, attempting retry: ${syncResult.errorMessage}');
            await _syncService.retrySyncWithBackoff(firebaseUser);
          }
        }
      } catch (syncError) {
        print('Profile sync failed during Google sign-in: $syncError');
        // Don't fail authentication if sync fails
      }

      state = AuthState.authenticated(user);
    } catch (e) {
      // Track failed Google authentication
      await _analytics.trackAuthenticationMethod(
        authProvider: 'google',
        authAction: 'sign_in',
        isSuccess: false,
        errorType: e.toString(),
      );

      state = AuthState.error(e.toString());
    }
  }

  // Apple Sign In
  Future<void> signInWithApple() async {
    state = const AuthState.loading();

    try {
      final user = await _authService.signInWithApple();
      await _sessionManager.saveUserSession(user);

      // Track successful Apple authentication
      await _analytics.trackAuthenticationMethod(
        authProvider: 'apple',
        authAction: 'sign_in',
        isSuccess: true,
      );

      // Sync user profile to Supabase after successful Apple authentication
      try {
        final firebaseUser = _authService.currentFirebaseUser;
        if (firebaseUser != null) {
          final syncResult = await _syncService.syncUserToSupabase(firebaseUser);

          // If sync fails, try retry mechanism
          if (!syncResult.isSuccess) {
            print('Initial sync failed, attempting retry: ${syncResult.errorMessage}');
            await _syncService.retrySyncWithBackoff(firebaseUser);
          }
        }
      } catch (syncError) {
        print('Profile sync failed during Apple sign-in: $syncError');
        // Don't fail authentication if sync fails
      }

      state = AuthState.authenticated(user);
    } catch (e) {
      // Track failed Apple authentication
      await _analytics.trackAuthenticationMethod(
        authProvider: 'apple',
        authAction: 'sign_in',
        isSuccess: false,
        errorType: e.toString(),
      );

      state = AuthState.error(e.toString());
    }
  }

  // Phone Authentication
  Future<void> verifyPhoneNumber({
    required String phoneNumber,
    required Function(String verificationId) onCodeSent,
    Function(AppUser user)? onAutoVerified,
    int? forceResendingToken,
  }) async {
    state = const AuthState.loading();

    try {
      await _authService.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        onCodeSent: (verificationId) {
          state = const AuthState.unauthenticated();
          onCodeSent(verificationId);
        },
        onError: (error) {
          state = AuthState.error(error);
        },
        onAutoVerified: (user) async {
          await _sessionManager.saveUserSession(user);

          // Sync user profile to Supabase after successful phone authentication
          try {
            final firebaseUser = _authService.currentFirebaseUser;
            if (firebaseUser != null) {
              final syncResult = await _syncService.syncUserToSupabase(firebaseUser);

              // If sync fails, try retry mechanism
              if (!syncResult.isSuccess) {
                print('Initial sync failed, attempting retry: ${syncResult.errorMessage}');
                await _syncService.retrySyncWithBackoff(firebaseUser);
              }
            }
          } catch (syncError) {
            print('Profile sync failed during phone auto-verification: $syncError');
            // Don't fail authentication if sync fails
          }

          state = AuthState.authenticated(user);
          onAutoVerified?.call(user);
        },
        forceResendingToken: forceResendingToken,
      );
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  Future<void> signInWithPhoneOtp({
    required String verificationId,
    required String otpCode,
  }) async {
    state = const AuthState.loading();
    
    try {
      final user = await _authService.signInWithPhoneOtp(
        verificationId: verificationId,
        otpCode: otpCode,
      );
      
      await _sessionManager.saveUserSession(user);

      // Sync user profile to Supabase after successful phone OTP authentication
      try {
        final firebaseUser = _authService.currentFirebaseUser;
        if (firebaseUser != null) {
          final syncResult = await _syncService.syncUserToSupabase(firebaseUser);

          // If sync fails, try retry mechanism
          if (!syncResult.isSuccess) {
            print('Initial sync failed, attempting retry: ${syncResult.errorMessage}');
            await _syncService.retrySyncWithBackoff(firebaseUser);
          }
        }
      } catch (syncError) {
        print('Profile sync failed during phone OTP sign-in: $syncError');
        // Don't fail authentication if sync fails
      }

      state = AuthState.authenticated(user);
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  // Password Reset
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _authService.sendPasswordResetEmail(email);
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  // Email Verification
  Future<void> sendEmailVerification() async {
    try {
      await _authService.sendEmailVerification();
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  Future<void> reloadUser() async {
    try {
      await _authService.reloadUser();
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        await _sessionManager.saveUserSession(currentUser);
        state = AuthState.authenticated(currentUser);
      }
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  // Email Verification with 6-digit code
  Future<void> sendEmailVerificationCode(String email) async {
    state = const AuthState.loading();

    try {
      await _emailVerificationService.sendVerificationCode(email);
      state = const AuthState.verificationPending();
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  // Check if user has verified their email and authenticate them
  Future<void> checkEmailVerificationAndAuthenticate({
    required String email,
    required String password,
  }) async {
    print('DEBUG: checkEmailVerificationAndAuthenticate called for $email');
    state = const AuthState.loading();

    try {
      // Try to sign in - this will work if email is verified
      final user = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      print('DEBUG: User signed in successfully after email verification');
      await _sessionManager.saveUserSession(user);

      // Sync user profile to Supabase after successful email verification authentication
      try {
        final firebaseUser = _authService.currentFirebaseUser;
        if (firebaseUser != null) {
          await _syncService.syncUserToSupabase(firebaseUser);
        }
      } catch (syncError) {
        print('Profile sync failed during email verification authentication: $syncError');
        // Don't fail authentication if sync fails
      }

      state = AuthState.authenticated(user);
    } catch (e) {
      print('DEBUG: Error during sign in after verification: $e');
      // If sign in fails, user might not have verified email yet
      state = AuthState.error('Please verify your email first, then try again.');
    }
  }

  // Resend Firebase email verification
  Future<void> resendEmailVerification({
    required String email,
    required String password,
  }) async {
    print('DEBUG: resendEmailVerification called for $email');
    state = const AuthState.loading();

    try {
      await _authService.resendEmailVerification(email, password);
      print('DEBUG: Verification email resent successfully');
      state = const AuthState.verificationPending();
    } catch (e) {
      print('DEBUG: Error resending verification email: $e');
      state = AuthState.error(e.toString());
    }
  }

  // Sign Out
  Future<void> signOut() async {
    print('DEBUG: signOut called');
    print('DEBUG: Current state before signOut: ${state.status}');
    state = const AuthState.loading();
    print('DEBUG: State set to loading');

    try {
      print('DEBUG: Calling auth service signOut');
      await _authService.signOut();
      print('DEBUG: Calling session manager clearSession');
      await _sessionManager.clearSession();
      print('DEBUG: Setting state to unauthenticated');
      state = const AuthState.unauthenticated();
      print('DEBUG: State set to unauthenticated, current state: ${state.status}');

      // Add a small delay to ensure state change is processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Force a final state notification to ensure main.dart rebuilds
      print('DEBUG: Forcing final state notification for navigation');
      state = const AuthState.unauthenticated();

      print('DEBUG: signOut completed successfully');
    } catch (e) {
      print('DEBUG: Error in signOut: $e');
      state = AuthState.error(e.toString());
    }
  }

  // Force clear everything and restart (for testing)
  Future<void> forceClearAndRestart() async {
    print('DEBUG: forceClearAndRestart called');

    try {
      // Clear Firebase auth
      await _authService.signOut();

      // Clear session
      await _sessionManager.clearSession();

      // Clear onboarding status to force restart
      await OnboardingService.instance.resetOnboarding();

      // Set to initial state
      state = const AuthState.initial();

      print('DEBUG: Force clear completed - app should restart flow');
    } catch (e) {
      print('DEBUG: Error in forceClearAndRestart: $e');
      state = AuthState.error(e.toString());
    }
  }

  // Update Activity
  Future<void> updateActivity() async {
    if (state.status == AuthStatus.authenticated) {
      await _sessionManager.updateActivity();
    }
  }

  // Clear Error
  void clearError() {
    if (state.status == AuthStatus.error) {
      state = const AuthState.unauthenticated();
    }
  }

  // Start periodic checking for email verification
  void _startVerificationChecking() {
    print('DEBUG: Starting periodic verification checking');
    _verificationCheckTimer?.cancel();

    _verificationCheckTimer = Timer.periodic(const Duration(seconds: 3), (timer) async {
      try {
        print('DEBUG: Checking email verification status...');

        // Get current Firebase user
        final currentUser = _authService.currentFirebaseUser;
        print('DEBUG: Current Firebase user: ${currentUser?.email ?? 'null'}');

        if (currentUser != null) {
          print('DEBUG: Reloading user to get latest verification status...');
          // Reload user to get latest verification status from server
          await currentUser.reload();

          // Get the reloaded user
          final reloadedUser = _authService.currentFirebaseUser;
          print('DEBUG: After reload - User: ${reloadedUser?.email ?? 'null'}, EmailVerified: ${reloadedUser?.emailVerified ?? false}');

          if (reloadedUser != null && reloadedUser.emailVerified) {
            print('DEBUG: ✅ Email verification detected! Stopping timer and authenticating user...');
            timer.cancel();
            _verificationCheckTimer = null;

            try {
              // Create app user and authenticate
              final appUser = AppUser.fromFirebaseUser(reloadedUser);
              await _sessionManager.saveUserSession(appUser);
              state = AuthState.authenticated(appUser);
              print('DEBUG: ✅ User authenticated successfully after email verification');
            } catch (authError) {
              print('DEBUG: ❌ Error during authentication: $authError');
              state = AuthState.error('Authentication failed after verification');
            }
          } else {
            print('DEBUG: ⏳ Email not yet verified, continuing to check...');
          }
        } else {
          print('DEBUG: ❌ No current Firebase user found during verification check');
          // Don't immediately error - user might be in the process of signing in
          // Only error after several failed attempts to avoid timing issues
          // For now, just continue checking
        }
      } catch (e) {
        print('DEBUG: ❌ Error during verification check: $e');
        // Don't set error state during verification checking - just continue
        // The user is already on the verification screen, so errors here shouldn't show
      }
    });

    print('DEBUG: Verification checking timer started (every 3 seconds)');
  }



  @override
  void dispose() {
    _authSubscription?.cancel();
    _verificationCheckTimer?.cancel();
    _sessionManager.dispose();
    super.dispose();
  }
}

// Auth State Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.read(authServiceProvider);
  final sessionManager = ref.read(sessionManagerProvider);
  final emailVerificationService = ref.read(emailVerificationServiceProvider);
  return AuthNotifier(authService, sessionManager, emailVerificationService);
});

// Current User Provider
final currentUserProvider = Provider<AppUser?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

// Is Authenticated Provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.status == AuthStatus.authenticated;
});

// Is Loading Provider
final isLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.isLoading;
});

// Account Lockout Provider
final accountLockoutProvider = FutureProvider<bool>((ref) async {
  final sessionManager = ref.read(sessionManagerProvider);
  return await sessionManager.isAccountLocked();
});

// Remaining Login Attempts Provider
final remainingLoginAttemptsProvider = FutureProvider<int>((ref) async {
  final sessionManager = ref.read(sessionManagerProvider);
  return await sessionManager.getRemainingLoginAttempts();
});
