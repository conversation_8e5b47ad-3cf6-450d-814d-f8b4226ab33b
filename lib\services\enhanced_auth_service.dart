import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'supabase_profile_service.dart';
import 'firebase_supabase_sync_service.dart';
import '../models/user_model.dart';
import '../models/sync_result.dart';
import '../models/profile_creation_result.dart';

class EnhancedAuthService {
  final firebase_auth.FirebaseAuth _firebaseAuth = firebase_auth.FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final SupabaseProfileService _profileService = SupabaseProfileService();
  final FirebaseSupabaseSyncService _syncService = FirebaseSupabaseSyncService.instance;

  /// Get current Firebase user
  firebase_auth.User? get currentUser => _firebaseAuth.currentUser;

  /// Stream of authentication state changes
  Stream<firebase_auth.User?> get authStateChanges => _firebaseAuth.authStateChanges();

  /// Sign up with email and password + automatic profile creation
  Future<AuthResult> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      // Step 1: Create Firebase Auth user
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = credential.user;
      if (user == null) {
        return AuthResult.failure('Failed to create user account');
      }

      // Step 2: Update display name if provided
      if (displayName != null && displayName.isNotEmpty) {
        await user.updateDisplayName(displayName);
        await user.reload();
      }

      // Step 3: Send email verification
      if (!user.emailVerified) {
        await user.sendEmailVerification();
      }

      // Step 4: Sync user to Supabase using new sync service
      final syncResult = await _syncService.syncUserToSupabase(user);

      if (syncResult.isSuccess) {
        return AuthResult.success(
          user: user,
          message: 'Account created successfully! Please verify your email.',
          profileCreated: true,
          profileData: _convertSyncResultToProfileResult(syncResult),
        );
      } else {
        // Profile creation failed, but Firebase user exists
        // Log the error but don't fail the signup
        print('Profile sync failed: ${syncResult.errorMessage}');
        return AuthResult.success(
          user: user,
          message: 'Account created successfully! Profile setup pending.',
          profileCreated: false,
          profileError: syncResult.errorMessage,
        );
      }
    } on firebase_auth.FirebaseAuthException catch (e) {
      return AuthResult.failure(_getFirebaseErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred: ${e.toString()}');
    }
  }

  /// Sign in with email and password + profile sync
  Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // Step 1: Sign in with Firebase
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = credential.user;
      if (user == null) {
        return AuthResult.failure('Failed to sign in');
      }

      // Step 2: Sync user data to Supabase profile using new sync service
      final syncResult = await _syncService.syncUserToSupabase(user);

      return AuthResult.success(
        user: user,
        message: 'Signed in successfully!',
        profileCreated: syncResult.isSuccess,
        profileError: syncResult.isSuccess ? null : syncResult.errorMessage,
      );
    } on firebase_auth.FirebaseAuthException catch (e) {
      return AuthResult.failure(_getFirebaseErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred: ${e.toString()}');
    }
  }

  /// Sign in with Google + automatic profile creation
  Future<AuthResult> signInWithGoogle() async {
    try {
      // Step 1: Google Sign In
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return AuthResult.failure('Google sign in was cancelled');
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Step 2: Sign in with Firebase
      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      final user = userCredential.user;

      if (user == null) {
        return AuthResult.failure('Failed to sign in with Google');
      }

      // Step 3: Check if this is a new user or existing user
      final isNewUser = userCredential.additionalUserInfo?.isNewUser ?? false;

      // Step 4: Sync user to Supabase using new sync service (handles both new and existing users)
      final syncResult = await _syncService.syncUserToSupabase(user);

      if (syncResult.isSuccess) {
        final message = isNewUser ? 'Welcome! Your account has been created.' : 'Welcome back!';
        return AuthResult.success(
          user: user,
          message: message,
          profileCreated: true,
          profileData: _convertSyncResultToProfileResult(syncResult),
        );
      } else {
        // Sync failed, but Firebase user exists
        print('Profile sync failed: ${syncResult.errorMessage}');
        return AuthResult.success(
          user: user,
          message: isNewUser ? 'Account created! Profile setup pending.' : 'Signed in! Profile sync pending.',
          profileCreated: false,
          profileError: syncResult.errorMessage,
        );
      }
    } catch (e) {
      return AuthResult.failure('Google sign in failed: ${e.toString()}');
    }
  }

  /// Sign in with Apple + automatic profile creation
  Future<AuthResult> signInWithApple() async {
    try {
      // Step 1: Apple Sign In - implement directly since we don't have AuthService dependency
      // Check if Apple Sign In is available
      if (!await SignInWithApple.isAvailable()) {
        return AuthResult.failure('Apple Sign In is not available on this device');
      }

      // Request Apple ID credential
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create Firebase credential
      final oauthCredential = firebase_auth.OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in with Firebase
      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);
      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        return AuthResult.failure('Apple sign in failed');
      }

      // Update display name if provided by Apple and not already set
      if (firebaseUser.displayName == null || firebaseUser.displayName!.isEmpty) {
        final fullName = appleCredential.givenName != null && appleCredential.familyName != null
            ? '${appleCredential.givenName} ${appleCredential.familyName}'
            : null;

        if (fullName != null && fullName.isNotEmpty) {
          await firebaseUser.updateDisplayName(fullName);
          await firebaseUser.reload();
        }
      }

      // Step 2: Check if this is a new user (first time signing in)
      final creationTime = firebaseUser.metadata.creationTime;
      final isNewUser = creationTime != null && creationTime.difference(DateTime.now()).inMinutes.abs() < 2;

      // Step 3: Sync user data to Supabase profile using new sync service
      final syncResult = await _syncService.syncUserToSupabase(firebaseUser);

      if (syncResult.isSuccess) {
        final message = isNewUser ? 'Welcome! Your account has been created.' : 'Welcome back!';
        return AuthResult.success(
          user: firebaseUser,
          message: message,
          profileCreated: true,
          profileData: _convertSyncResultToProfileResult(syncResult),
        );
      } else {
        // Sync failed, but Firebase user exists
        print('Profile sync failed: ${syncResult.errorMessage}');
        return AuthResult.success(
          user: firebaseUser,
          message: isNewUser ? 'Account created! Profile setup pending.' : 'Signed in! Profile sync pending.',
          profileCreated: false,
          profileError: syncResult.errorMessage,
        );
      }
    } catch (e) {
      return AuthResult.failure('Apple sign in failed: ${e.toString()}');
    }
  }

  /// Sign out
  Future<void> signOut() async {
    await Future.wait([
      _firebaseAuth.signOut(),
      _googleSignIn.signOut(),
    ]);
  }

  /// Send password reset email
  Future<AuthResult> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      return AuthResult.success(
        message: 'Password reset email sent successfully!',
      );
    } on firebase_auth.FirebaseAuthException catch (e) {
      return AuthResult.failure(_getFirebaseErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('Failed to send password reset email');
    }
  }

  /// Ensure user has a Supabase profile (for existing users)
  Future<bool> ensureProfileExists() async {
    final user = currentUser;
    if (user == null) return false;

    try {
      // Use new sync service to check and create profile if needed
      final syncResult = await _syncService.syncUserToSupabase(user);
      return syncResult.isSuccess;
    } catch (e) {
      print('Error ensuring profile exists: $e');
      return false;
    }
  }

  /// Get user profile with role information
  Future<AppUser?> getCurrentUserProfile() async {
    final user = currentUser;
    if (user == null) return null;

    return await _profileService.getProfileWithRole(user.uid);
  }

  /// Helper method to convert SyncResult to ProfileCreationResult for backward compatibility
  ProfileCreationResult _convertSyncResultToProfileResult(SyncResult syncResult) {
    if (syncResult.isSuccess) {
      return ProfileCreationResult.success(
        profileId: syncResult.profileId ?? '',
        firebaseUid: syncResult.firebaseUid ?? '',
        email: syncResult.email,
        displayName: syncResult.displayName,
        message: syncResult.message,
      );
    } else {
      return ProfileCreationResult.failure(
        errorMessage: syncResult.errorMessage ?? 'Unknown sync error',
        message: syncResult.message,
      );
    }
  }

  /// Helper method to convert Firebase errors to user-friendly messages
  String _getFirebaseErrorMessage(firebase_auth.FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'email-already-in-use':
        return 'An account already exists for this email.';
      case 'user-not-found':
        return 'No user found for this email.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }
}

/// Result class for authentication operations
class AuthResult {
  final bool isSuccess;
  final firebase_auth.User? user;
  final String message;
  final bool profileCreated;
  final ProfileCreationResult? profileData;
  final String? profileError;

  AuthResult._({
    required this.isSuccess,
    this.user,
    required this.message,
    this.profileCreated = false,
    this.profileData,
    this.profileError,
  });

  factory AuthResult.success({
    firebase_auth.User? user,
    required String message,
    bool profileCreated = false,
    ProfileCreationResult? profileData,
    String? profileError,
  }) {
    return AuthResult._(
      isSuccess: true,
      user: user,
      message: message,
      profileCreated: profileCreated,
      profileData: profileData,
      profileError: profileError,
    );
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(
      isSuccess: false,
      message: message,
    );
  }

  /// Check if authentication succeeded but profile creation failed
  bool get hasProfileIssue => isSuccess && !profileCreated && profileError != null;
}
