import 'dart:async';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_profile.dart';
import '../utils/result.dart';
// import '../utils/logger.dart';

/// Service for handling profile updates with comprehensive error handling and validation
class ProfileUpdateService {
  static final ProfileUpdateService _instance = ProfileUpdateService._internal();
  factory ProfileUpdateService() => _instance;
  ProfileUpdateService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Update user profile with optimistic locking and comprehensive error handling
  Future<Result<ProfileUpdateResult>> updateProfile(
    ProfileUpdateRequest request, {
    Duration timeout = const Duration(seconds: 30),
  }) async {
    try {
      print('🔄 ProfileUpdateService: Starting profile update...');

      // Validate user authentication
      final user = _auth.currentUser;
      if (user == null) {
        print('❌ ProfileUpdateService: User not authenticated');
        return Result.failure(
          'User not authenticated',
          'AUTH_ERROR',
        );
      }

      print('✅ ProfileUpdateService: User authenticated - UID: ${user.uid}');
      print('📧 ProfileUpdateService: User email: ${user.email}');

      // Validate request data
      final validationResult = _validateUpdateRequest(request);
      if (validationResult != null) {
        print('❌ ProfileUpdateService: Request validation failed: $validationResult');
        return Result.failure(validationResult, 'VALIDATION_ERROR');
      }

      print('✅ ProfileUpdateService: Request validation passed');

      // Logger.info('Updating profile for user: ${user.uid}');

      // Convert request to Supabase parameters
      final params = request.toSupabaseParams(user.uid);
      print('📋 ProfileUpdateService: Update parameters: $params');

      // Call Supabase RPC function with timeout
      print('🔗 ProfileUpdateService: Calling update_user_profile RPC...');
      final response = await _supabase
          .rpc('update_user_profile', params: params)
          .timeout(timeout);

      print('📥 ProfileUpdateService: RPC response received: $response');

      // Parse response
      if (response == null) {
        print('❌ ProfileUpdateService: No response from server');
        return Result.failure(
          'No response from server',
          'SERVER_ERROR',
        );
      }

      print('🔄 ProfileUpdateService: Parsing response...');
      final responseData = response as Map<String, dynamic>;
      print('📊 ProfileUpdateService: Response data: $responseData');

      if (responseData['success'] == true) {
        print('✅ ProfileUpdateService: Update successful!');

        // Success - parse updated profile data
        final profileData = responseData['profile_data'] as Map<String, dynamic>;

        // Add the profile_id to the profile_data since it's missing
        if (responseData['profile_id'] != null) {
          profileData['id'] = responseData['profile_id'];
        }

        // CRITICAL FIX: Add sync_version from response root to profile_data
        // The update RPC returns sync_version at root level, not in profile_data
        if (responseData['sync_version'] != null) {
          profileData['sync_version'] = responseData['sync_version'];
          print('🔧 ProfileUpdateService: Added sync_version ${responseData['sync_version']} to profile_data');
        }

        final updatedProfile = UserProfile.fromSupabase(profileData);

        print('📈 ProfileUpdateService: New sync version: ${responseData['sync_version']}');
        print('📊 ProfileUpdateService: Completion percentage: ${responseData['completion_percentage']}');

        return Result.success(
          ProfileUpdateResult.success(
            profile: updatedProfile,
            message: responseData['message'] as String,
            newSyncVersion: responseData['sync_version'] as int,
            completionPercentage: responseData['completion_percentage'] as int,
          ),
        );
      } else {
        print('❌ ProfileUpdateService: Update failed from database');

        // Error response from database
        final errorCode = responseData['error'] as String?;
        final message = responseData['message'] as String;

        print('❌ ProfileUpdateService: Error code: $errorCode');
        print('❌ ProfileUpdateService: Error message: $message');

        return Result.success(
          ProfileUpdateResult.error(
            error: errorCode ?? 'UNKNOWN_ERROR',
            message: message,
            errorCode: errorCode,
            currentVersion: responseData['current_version'] as int?,
            providedVersion: responseData['provided_version'] as int?,
          ),
        );
      }
    } on TimeoutException {
      print('⏰ ProfileUpdateService: Request timed out');
      return Result.failure(
        'Request timed out. Please check your connection and try again.',
        'TIMEOUT_ERROR',
      );
    } on PostgrestException catch (e) {
      print('❌ ProfileUpdateService: PostgrestException occurred');
      print('   Code: ${e.code}');
      print('   Message: ${e.message}');
      print('   Details: ${e.details}');
      print('   Hint: ${e.hint}');
      return Result.failure(
        _handlePostgrestError(e),
        'DATABASE_ERROR',
      );
    } on AuthException catch (e) {
      print('❌ ProfileUpdateService: AuthException occurred: ${e.message}');
      return Result.failure(
        'Authentication failed. Please log in again.',
        'AUTH_ERROR',
      );
    } catch (e) {
      print('❌ ProfileUpdateService: Unexpected error occurred: $e');
      print('❌ ProfileUpdateService: Error type: ${e.runtimeType}');
      return Result.failure(
        'An unexpected error occurred. Please try again.',
        'UNKNOWN_ERROR',
      );
    }
  }

  /// Get complete user profile data
  Future<Result<UserProfile>> getProfile({
    Duration timeout = const Duration(seconds: 15),
  }) async {
    try {
      // Validate user authentication
      final user = _auth.currentUser;
      if (user == null) {
        return Result.failure(
          'User not authenticated',
          'AUTH_ERROR',
        );
      }

      // Logger.info('Fetching profile for user: ${user.uid}');

      // Call Supabase RPC function
      final response = await _supabase
          .rpc('get_user_profile', params: {'p_firebase_uid': user.uid})
          .timeout(timeout);

      if (response == null) {
        return Result.failure(
          'No response from server',
          'SERVER_ERROR',
        );
      }

      final responseData = response as Map<String, dynamic>;
      
      if (responseData['success'] == true) {
        final profileData = responseData['profile_data'] as Map<String, dynamic>;

        // Add the profile_id to the profile_data since it's missing
        if (responseData['profile_id'] != null) {
          profileData['id'] = responseData['profile_id'];
        }

        final profile = UserProfile.fromSupabase(profileData);
        
        // Logger.info('Profile fetched successfully');
        return Result.success(profile);
      } else {
        final errorCode = responseData['error'] as String?;
        final message = responseData['message'] as String;

        // Logger.warning('Failed to fetch profile: $errorCode - $message');
        return Result.failure(message, errorCode ?? 'DATABASE_ERROR');
      }
    } on TimeoutException {
      // Logger.error('Profile fetch timed out');
      return Result.failure(
        'Request timed out. Please check your connection and try again.',
        'TIMEOUT_ERROR',
      );
    } on PostgrestException catch (e) {
      // Logger.error('Supabase error during profile fetch: ${e.message}');
      return Result.failure(
        _handlePostgrestError(e),
        'DATABASE_ERROR',
      );
    } on AuthException {
      // Logger.error('Authentication error during profile fetch');
      return Result.failure(
        'Authentication failed. Please log in again.',
        'AUTH_ERROR',
      );
    } catch (e) {
      // Logger.error('Unexpected error during profile fetch: $e');
      return Result.failure(
        'An unexpected error occurred. Please try again.',
        'UNKNOWN_ERROR',
      );
    }
  }

  /// Update last active timestamp
  Future<Result<void>> updateLastActive() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return Result.failure('User not authenticated', 'AUTH_ERROR');

      await _supabase.rpc('update_last_active', params: {
        'p_firebase_uid': user.uid,
      });

      return Result.success(null);
    } catch (e) {
      // Logger.warning('Failed to update last active: $e');
      // Don't fail the entire operation for this
      return Result.success(null);
    }
  }

  /// Validate profile update request
  String? _validateUpdateRequest(ProfileUpdateRequest request) {
    // Email validation
    if (request.email != null && request.email!.isNotEmpty) {
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (!emailRegex.hasMatch(request.email!)) {
        return 'Please enter a valid email address';
      }
    }

    // Phone number validation
    if (request.phoneNumber != null && request.phoneNumber!.isNotEmpty) {
      final cleanPhone = request.phoneNumber!.replaceAll(RegExp(r'[^\d+]'), '');
      if (cleanPhone.length < 10) {
        return 'Please enter a valid phone number';
      }
    }

    // Date of birth validation
    if (request.dateOfBirth != null) {
      final now = DateTime.now();
      final age = now.year - request.dateOfBirth!.year;
      if (age < 0 || age > 150) {
        return 'Please enter a valid date of birth';
      }
    }

    // Gender validation
    if (request.gender != null && request.gender!.isNotEmpty) {
      const validGenders = ['male', 'female', 'other', 'prefer_not_to_say'];
      if (!validGenders.contains(request.gender!.toLowerCase())) {
        return 'Please select a valid gender option';
      }
    }

    // Annual income validation
    if (request.annualIncome != null && request.annualIncome! < 0) {
      return 'Annual income cannot be negative';
    }

    return null; // No validation errors
  }

  /// Validate email format
  bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(email);
  }

  /// Validate phone number format
  bool isValidPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) return false;
    // Remove all non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    // Should have at least 10 digits
    return digitsOnly.length >= 10;
  }

  /// Validate date of birth
  bool isValidDateOfBirth(DateTime dateOfBirth) {
    final now = DateTime.now();
    final age = now.year - dateOfBirth.year;

    // Check if birthday has occurred this year
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      return age - 1 >= 0 && age - 1 <= 120;
    }

    return age >= 0 && age <= 120;
  }

  /// Validate gender value
  bool isValidGender(String gender) {
    const validGenders = ['male', 'female', 'other', 'prefer_not_to_say'];
    return validGenders.contains(gender.toLowerCase());
  }

  /// Validate annual income
  bool isValidAnnualIncome(double income) {
    return income >= 0 && income.isFinite && !income.isNaN;
  }

  /// Handle Postgrest errors with user-friendly messages
  String _handlePostgrestError(PostgrestException error) {
    switch (error.code) {
      case '23505': // Unique violation
        if (error.message.contains('email')) {
          return 'This email address is already in use by another account';
        } else if (error.message.contains('phone')) {
          return 'This phone number is already in use by another account';
        }
        return 'This information is already in use by another account';

      case '23514': // Check constraint violation
        return 'Invalid data provided. Please check your input and try again';

      case '42501': // Insufficient privilege (RLS)
        return 'You do not have permission to update this profile';

      case 'PGRST116': // Row not found
        return 'Profile not found. Please try logging out and back in';

      default:
        return 'A database error occurred. Please try again later';
    }
  }
}

/// Extension methods for easier profile updates
extension ProfileUpdateExtensions on UserProfile {
  /// Create an update request with only the changed fields
  ProfileUpdateRequest createUpdateRequest({
    String? displayName,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? gender,
    String? nativeLanguage,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    String? occupation,
    String? companyName,
    double? annualIncome,
  }) {
    return ProfileUpdateRequest(
      displayName: displayName != this.displayName ? displayName : null,
      firstName: firstName != this.firstName ? firstName : null,
      lastName: lastName != this.lastName ? lastName : null,
      email: email != this.email ? email : null,
      phoneNumber: phoneNumber != this.phoneNumber ? phoneNumber : null,
      dateOfBirth: dateOfBirth != this.dateOfBirth ? dateOfBirth : null,
      gender: gender != this.gender ? gender : null,
      nativeLanguage: nativeLanguage != this.nativeLanguage ? nativeLanguage : null,
      addressLine1: addressLine1 != this.addressLine1 ? addressLine1 : null,
      addressLine2: addressLine2 != this.addressLine2 ? addressLine2 : null,
      city: city != this.city ? city : null,
      state: state != this.state ? state : null,
      postalCode: postalCode != this.postalCode ? postalCode : null,
      country: country != this.country ? country : null,
      occupation: occupation != this.occupation ? occupation : null,
      companyName: companyName != this.companyName ? companyName : null,
      annualIncome: annualIncome != this.annualIncome ? annualIncome : null,
      syncVersion: syncVersion,
    );
  }
}
