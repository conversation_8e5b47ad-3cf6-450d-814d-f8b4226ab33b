import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

import '../models/sync_result.dart';
import 'firebase_supabase_sync_service.dart';
import 'sync_queue_service.dart';
import 'sync_monitoring_service.dart';
import 'analytics_service.dart';

/// Service for manual sync operations and retry functionality
class ManualSyncService {
  static final ManualSyncService _instance = ManualSyncService._internal();
  factory ManualSyncService() => _instance;
  ManualSyncService._internal();

  final FirebaseSupabaseSyncService _syncService = FirebaseSupabaseSyncService.instance;
  final SyncQueueService _queueService = SyncQueueService();
  final SyncMonitoringService _monitoringService = SyncMonitoringService();
  final AnalyticsService _analytics = AnalyticsService.instance;

  /// Manually sync current user
  Future<SyncResult> syncCurrentUser() async {
    try {
      final currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return SyncResult.authFailure(
          operationType: SyncOperationType.dataSync,
          errorMessage: 'No authenticated user found',
        );
      }

      // Track manual sync attempt
      await _analytics.trackManualSyncAttempted(
        firebaseUid: currentUser.uid,
        authProvider: _getAuthProvider(currentUser),
      );

      // Attempt sync with retry
      final result = await _syncService.retrySyncWithBackoff(currentUser);

      // Track result
      await _analytics.trackManualSyncCompleted(
        firebaseUid: currentUser.uid,
        authProvider: _getAuthProvider(currentUser),
        isSuccess: result.isSuccess,
        errorType: result.isSuccess ? null : result.errorType.toString(),
      );

      return result;
    } catch (e) {
      return SyncResult.failure(
        operationType: SyncOperationType.dataSync,
        errorType: SyncErrorType.unknownError,
        errorMessage: 'Manual sync failed: $e',
        message: 'Manual sync operation failed',
      );
    }
  }

  /// Retry a specific failed sync
  Future<SyncResult> retryFailedSync(String firebaseUid) async {
    try {
      await _analytics.trackManualRetryAttempted(
        firebaseUid: firebaseUid,
        retryType: 'failed_sync',
      );

      final result = await _queueService.retryFailedSync(firebaseUid);

      await _analytics.trackManualRetryCompleted(
        firebaseUid: firebaseUid,
        retryType: 'failed_sync',
        isSuccess: result.isSuccess,
        errorType: result.isSuccess ? null : result.errorType.toString(),
      );

      return result;
    } catch (e) {
      return SyncResult.failure(
        operationType: SyncOperationType.dataSync,
        errorType: SyncErrorType.unknownError,
        errorMessage: 'Failed sync retry failed: $e',
        message: 'Retry operation failed',
        firebaseUid: firebaseUid,
      );
    }
  }

  /// Process entire sync queue manually
  Future<ManualSyncBatchResult> processQueueManually() async {
    try {
      await _analytics.trackManualQueueProcessingStarted();

      final initialStatus = await _queueService.getQueueStatus();
      final initialQueueSize = initialStatus['queueSize'] ?? 0;

      await _queueService.processQueue();

      final finalStatus = await _queueService.getQueueStatus();
      final finalQueueSize = finalStatus['queueSize'] ?? 0;
      
      final processedCount = initialQueueSize - finalQueueSize;
      final isSuccess = processedCount > 0 || initialQueueSize == 0;

      await _analytics.trackManualQueueProcessingCompleted(
        initialQueueSize: initialQueueSize,
        processedCount: processedCount,
        remainingCount: finalQueueSize,
        isSuccess: isSuccess,
      );

      return ManualSyncBatchResult(
        isSuccess: isSuccess,
        initialQueueSize: initialQueueSize,
        processedCount: processedCount,
        remainingCount: finalQueueSize,
        message: isSuccess 
            ? 'Successfully processed $processedCount items'
            : 'Queue processing completed with issues',
      );
    } catch (e) {
      await _analytics.trackManualQueueProcessingCompleted(
        initialQueueSize: 0,
        processedCount: 0,
        remainingCount: 0,
        isSuccess: false,
        errorMessage: e.toString(),
      );

      return ManualSyncBatchResult(
        isSuccess: false,
        initialQueueSize: 0,
        processedCount: 0,
        remainingCount: 0,
        message: 'Queue processing failed: $e',
      );
    }
  }

  /// Get sync status summary for UI
  Future<SyncStatusSummary> getSyncStatusSummary() async {
    try {
      // Get queue status
      final queueStatus = await _queueService.getQueueStatus();
      
      // Get failed syncs
      final failedSyncs = await _queueService.getFailedSyncs();
      
      // Get health report
      final healthReport = await _monitoringService.getCurrentHealth();
      
      // Get profiles needing retry
      final profilesNeedingRetry = await _syncService.getProfilesNeedingRetry();

      return SyncStatusSummary(
        queueSize: queueStatus['queueSize'] ?? 0,
        failedSyncsCount: failedSyncs.length,
        profilesNeedingRetryCount: profilesNeedingRetry.length,
        overallHealth: healthReport.overallHealth,
        errorRate: healthReport.errorRate,
        activeAlertsCount: healthReport.alerts.length,
        lastHealthCheck: healthReport.timestamp,
        isQueueProcessing: queueStatus['isProcessing'] ?? false,
      );
    } catch (e) {
      return SyncStatusSummary.error('Failed to get sync status: $e');
    }
  }

  /// Get detailed failed syncs for UI
  Future<List<FailedSyncItem>> getFailedSyncsForUI() async {
    try {
      final failedSyncs = await _queueService.getFailedSyncs();
      
      return failedSyncs.map((item) => FailedSyncItem(
        firebaseUid: item['firebaseUid'] ?? '',
        email: item['email'] ?? '',
        displayName: item['displayName'] ?? '',
        authProvider: item['authProvider'] ?? '',
        lastError: item['lastError'] ?? '',
        retryCount: item['retryCount'] ?? 0,
        lastAttempt: item['lastAttempt'] != null 
            ? DateTime.parse(item['lastAttempt'])
            : DateTime.now(),
        addedAt: item['addedAt'] != null
            ? DateTime.parse(item['addedAt'])
            : DateTime.now(),
      )).toList();
    } catch (e) {
      print('Error getting failed syncs for UI: $e');
      return [];
    }
  }

  /// Clear all failed syncs (admin function)
  Future<bool> clearAllFailedSyncs() async {
    try {
      await _analytics.trackManualClearFailedSyncs();
      
      // This would require implementing a clear method in SyncQueueService
      // For now, we'll return true to indicate the operation was attempted
      return true;
    } catch (e) {
      print('Error clearing failed syncs: $e');
      return false;
    }
  }

  /// Force sync for a specific user (admin function)
  Future<SyncResult> forceSyncUser(String firebaseUid) async {
    try {
      await _analytics.trackManualForceSync(firebaseUid: firebaseUid);

      // This would require getting user data and forcing a sync
      // For now, we'll return a placeholder result
      return SyncResult.validationFailure(
        operationType: SyncOperationType.dataSync,
        errorMessage: 'Force sync not implemented yet',
        firebaseUid: firebaseUid,
      );
    } catch (e) {
      return SyncResult.failure(
        operationType: SyncOperationType.dataSync,
        errorType: SyncErrorType.unknownError,
        errorMessage: 'Force sync failed: $e',
        message: 'Force sync operation failed',
        firebaseUid: firebaseUid,
      );
    }
  }

  /// Get auth provider from Firebase user
  String _getAuthProvider(firebase_auth.User user) {
    if (user.providerData.isNotEmpty) {
      final providerId = user.providerData.first.providerId;
      switch (providerId) {
        case 'google.com':
          return 'google';
        case 'apple.com':
          return 'apple';
        case 'phone':
          return 'phone';
        case 'password':
          return 'email';
        default:
          return 'unknown';
      }
    }
    return 'unknown';
  }
}

/// Result of manual batch sync operation
class ManualSyncBatchResult {
  final bool isSuccess;
  final int initialQueueSize;
  final int processedCount;
  final int remainingCount;
  final String message;

  ManualSyncBatchResult({
    required this.isSuccess,
    required this.initialQueueSize,
    required this.processedCount,
    required this.remainingCount,
    required this.message,
  });
}

/// Summary of sync status for UI display
class SyncStatusSummary {
  final int queueSize;
  final int failedSyncsCount;
  final int profilesNeedingRetryCount;
  final SyncHealthStatus overallHealth;
  final double errorRate;
  final int activeAlertsCount;
  final DateTime lastHealthCheck;
  final bool isQueueProcessing;
  final String? error;

  SyncStatusSummary({
    required this.queueSize,
    required this.failedSyncsCount,
    required this.profilesNeedingRetryCount,
    required this.overallHealth,
    required this.errorRate,
    required this.activeAlertsCount,
    required this.lastHealthCheck,
    required this.isQueueProcessing,
    this.error,
  });

  factory SyncStatusSummary.error(String error) {
    return SyncStatusSummary(
      queueSize: 0,
      failedSyncsCount: 0,
      profilesNeedingRetryCount: 0,
      overallHealth: SyncHealthStatus.critical,
      errorRate: 1.0,
      activeAlertsCount: 0,
      lastHealthCheck: DateTime.now(),
      isQueueProcessing: false,
      error: error,
    );
  }
}

/// Failed sync item for UI display
class FailedSyncItem {
  final String firebaseUid;
  final String email;
  final String displayName;
  final String authProvider;
  final String lastError;
  final int retryCount;
  final DateTime lastAttempt;
  final DateTime addedAt;

  FailedSyncItem({
    required this.firebaseUid,
    required this.email,
    required this.displayName,
    required this.authProvider,
    required this.lastError,
    required this.retryCount,
    required this.lastAttempt,
    required this.addedAt,
  });
}
