import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

/// Utility class for mapping Firebase Auth provider IDs to app-specific provider names
class AuthProviderMapper {
  static AuthProviderMapper? _instance;
  static AuthProviderMapper get instance => _instance ??= AuthProviderMapper._();
  
  AuthProviderMapper._();

  /// Map Firebase provider ID to app provider name
  String mapFirebaseProviderToAppProvider(firebase_auth.User firebaseUser) {
    // Get the primary provider (first one in the list)
    final providerData = firebaseUser.providerData;
    
    if (providerData.isEmpty) {
      // Fallback to checking if user has email/password
      if (firebaseUser.email != null) {
        return 'email';
      }
      return 'unknown';
    }

    // Get the first (primary) provider
    final primaryProvider = providerData.first;
    return mapProviderIdToAppProvider(primaryProvider.providerId);
  }

  /// Map Firebase provider ID string to app provider name
  String mapProviderIdToAppProvider(String providerId) {
    switch (providerId) {
      case 'google.com':
        return 'google';
      case 'apple.com':
        return 'apple';
      case 'phone':
        return 'phone';
      case 'password':
      case 'firebase':
        return 'email';
      case 'facebook.com':
        return 'facebook';
      case 'twitter.com':
        return 'twitter';
      case 'github.com':
        return 'github';
      case 'microsoft.com':
        return 'microsoft';
      default:
        return 'email'; // Default fallback
    }
  }

  /// Get all providers for a Firebase user
  List<String> getAllProvidersForUser(firebase_auth.User firebaseUser) {
    return firebaseUser.providerData
        .map((provider) => mapProviderIdToAppProvider(provider.providerId))
        .toSet() // Remove duplicates
        .toList();
  }

  /// Check if user signed in with a specific provider
  bool userSignedInWithProvider(firebase_auth.User firebaseUser, String appProvider) {
    final userProviders = getAllProvidersForUser(firebaseUser);
    return userProviders.contains(appProvider);
  }

  /// Get provider display name for UI
  String getProviderDisplayName(String appProvider) {
    switch (appProvider) {
      case 'google':
        return 'Google';
      case 'apple':
        return 'Apple';
      case 'phone':
        return 'Phone';
      case 'email':
        return 'Email';
      case 'facebook':
        return 'Facebook';
      case 'twitter':
        return 'Twitter';
      case 'github':
        return 'GitHub';
      case 'microsoft':
        return 'Microsoft';
      default:
        return 'Unknown';
    }
  }

  /// Get provider icon name for UI (assuming you have icon assets)
  String getProviderIconName(String appProvider) {
    switch (appProvider) {
      case 'google':
        return 'google_icon';
      case 'apple':
        return 'apple_icon';
      case 'phone':
        return 'phone_icon';
      case 'email':
        return 'email_icon';
      case 'facebook':
        return 'facebook_icon';
      case 'twitter':
        return 'twitter_icon';
      case 'github':
        return 'github_icon';
      case 'microsoft':
        return 'microsoft_icon';
      default:
        return 'default_icon';
    }
  }

  /// Validate if provider is supported by the app
  bool isProviderSupported(String appProvider) {
    const supportedProviders = [
      'google',
      'apple',
      'phone',
      'email',
      'facebook',
      'twitter',
      'github',
      'microsoft',
    ];
    return supportedProviders.contains(appProvider);
  }

  /// Get provider priority for display order (lower number = higher priority)
  int getProviderPriority(String appProvider) {
    switch (appProvider) {
      case 'google':
        return 1;
      case 'apple':
        return 2;
      case 'email':
        return 3;
      case 'phone':
        return 4;
      case 'facebook':
        return 5;
      case 'microsoft':
        return 6;
      case 'twitter':
        return 7;
      case 'github':
        return 8;
      default:
        return 99;
    }
  }

  /// Sort providers by priority
  List<String> sortProvidersByPriority(List<String> providers) {
    final providersCopy = List<String>.from(providers);
    providersCopy.sort((a, b) => getProviderPriority(a).compareTo(getProviderPriority(b)));
    return providersCopy;
  }

  /// Get provider-specific data extraction rules
  Map<String, dynamic> extractProviderSpecificData(firebase_auth.User firebaseUser) {
    final primaryProvider = mapFirebaseProviderToAppProvider(firebaseUser);
    final data = <String, dynamic>{
      'primaryProvider': primaryProvider,
      'allProviders': getAllProvidersForUser(firebaseUser),
    };

    switch (primaryProvider) {
      case 'google':
        data['googleSpecific'] = {
          'hasGooglePhoto': firebaseUser.photoURL?.contains('googleusercontent.com') ?? false,
          'googleDisplayName': firebaseUser.displayName,
        };
        break;
      case 'apple':
        data['appleSpecific'] = {
          'isPrivateEmail': firebaseUser.email?.contains('privaterelay.appleid.com') ?? false,
          'appleDisplayName': firebaseUser.displayName,
        };
        break;
      case 'phone':
        data['phoneSpecific'] = {
          'phoneNumber': firebaseUser.phoneNumber,
          'isPhoneVerified': firebaseUser.phoneNumber != null,
        };
        break;
      case 'email':
        data['emailSpecific'] = {
          'email': firebaseUser.email,
          'isEmailVerified': firebaseUser.emailVerified,
        };
        break;
    }

    return data;
  }

  /// Check if user needs additional verification based on provider
  bool needsAdditionalVerification(firebase_auth.User firebaseUser) {
    final primaryProvider = mapFirebaseProviderToAppProvider(firebaseUser);
    
    switch (primaryProvider) {
      case 'email':
        return !firebaseUser.emailVerified;
      case 'phone':
        return firebaseUser.phoneNumber == null;
      case 'google':
      case 'apple':
        return false; // These providers handle verification
      default:
        return false;
    }
  }

  /// Get recommended next steps for user based on provider
  List<String> getRecommendedNextSteps(firebase_auth.User firebaseUser) {
    final steps = <String>[];
    final primaryProvider = mapFirebaseProviderToAppProvider(firebaseUser);

    if (needsAdditionalVerification(firebaseUser)) {
      switch (primaryProvider) {
        case 'email':
          steps.add('verify_email');
          break;
        case 'phone':
          steps.add('add_phone_number');
          break;
      }
    }

    if (firebaseUser.displayName == null || firebaseUser.displayName!.isEmpty) {
      steps.add('complete_profile');
    }

    if (firebaseUser.photoURL == null) {
      steps.add('add_profile_photo');
    }

    return steps;
  }
}
