# Firebase to Supabase Sync Analysis & Fixes

## 📋 **INVESTIGATION SUMMARY**

This document provides a comprehensive analysis of the default values and behaviors in the Firebase to Supabase profile synchronization system for the "All About Insurance" app.

---

## 🔍 **FINDINGS & EXPLANATIONS**

### **1. 🔑 Role ID Field**

**Status:** ✅ **CORRECT BEHAVIOR**

**Current Logic:**
- New users are automatically assigned a default role during profile creation
- Primary assignment: "Customer Service Representative" (CSR) role
- Fallback: First active role by hierarchy level if CSR doesn't exist

**Code Location:** `supabase_profiles_schema.sql` lines 365-378

**Why This Is Correct:**
- Ensures all users have appropriate permissions from signup
- CSR role is suitable for customer-facing insurance app users
- Provides graceful fallback if default role is missing

---

### **2. 📦 Subscription Plan**

**Status:** ✅ **CORRECT BEHAVIOR**

**Current Logic:**
- Default value: `'Free'` (updated from 'basic')
- Status: `'active'` (not `'inactive'` as initially set)
- Billing period: `'monthly'`

**Code Location:** `supabase_profiles_schema.sql` lines 80-84, 427-429

**Why This Is Correct:**
- Standard freemium model approach
- Users start with free features immediately available
- Can be upgraded to 'pro' or 'enterprise' plans later

---

### **3. 📅 Subscription End Date**

**Status:** ✅ **CORRECT BEHAVIOR**

**Current Logic:**
- Default value: `NULL`
- Only set when user has a paid subscription with expiration

**Code Location:** `supabase_profiles_schema.sql` line 83

**Why This Is Correct:**
- Free plans don't have expiration dates
- NULL indicates unlimited access to free features
- End dates only apply to time-limited paid subscriptions

---

### **4. ⏰ Last Sign-In Tracking**

**Status:** ❌ **BUG IDENTIFIED & FIXED**

**Problem:**
- Flutter code was passing `p_last_sign_in_at` parameter
- SQL function wasn't accepting this parameter
- Subsequent logins weren't updating the timestamp properly

**Root Cause:**
- Parameter mismatch between Flutter service and SQL function
- Missing auth provider parameter in sync function

---

## 🔧 **FIXES IMPLEMENTED**

### **Fix 1: Updated `sync_firebase_user_data` Function**

**Changes Made:**
```sql
-- Added missing parameters
CREATE OR REPLACE FUNCTION public.sync_firebase_user_data(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_is_email_verified BOOLEAN DEFAULT NULL,
    p_auth_provider TEXT DEFAULT NULL,        -- ✅ NEW
    p_last_sign_in_at TEXT DEFAULT NULL       -- ✅ NEW
)
```

**Updated Logic:**
```sql
-- Now uses provided timestamp instead of always NOW()
last_sign_in_at = COALESCE(p_last_sign_in_at::TIMESTAMPTZ, NOW()),
auth_provider = COALESCE(p_auth_provider, auth_provider),
```

### **Fix 2: Updated `handle_user_signup` Function**

**Changes Made:**
```sql
-- Added missing parameters for signup
CREATE OR REPLACE FUNCTION public.handle_user_signup(
    p_firebase_uid TEXT,
    p_email TEXT,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_auth_provider TEXT DEFAULT 'email',
    p_is_email_verified BOOLEAN DEFAULT TRUE,  -- ✅ NEW
    p_last_sign_in_at TEXT DEFAULT NULL        -- ✅ NEW
)
```

### **Fix 3: Updated Flutter Service**

**Changes Made:**
```dart
// Now passes auth provider and last sign-in timestamp
final userData = {
  'p_firebase_uid': firebaseUser.uid,
  'p_email': firebaseUser.email,
  'p_display_name': firebaseUser.displayName,
  'p_phone_number': firebaseUser.phoneNumber,
  'p_photo_url': firebaseUser.photoURL,
  'p_is_email_verified': firebaseUser.emailVerified,
  'p_auth_provider': authProvider,                    // ✅ NEW
  'p_last_sign_in_at': DateTime.now().toIso8601String(), // ✅ NEW
};
```

---

## ✅ **VERIFICATION STEPS**

### **Test the Fix:**

1. **Sign out and sign in again** with Google authentication
2. **Check Supabase profiles table** - `last_sign_in_at` should update with each login
3. **Verify auth provider** is correctly set to 'google' (not 'email')

### **Expected Behavior After Fix:**
- ✅ `last_sign_in_at` updates on every login
- ✅ `auth_provider` correctly reflects actual sign-in method
- ✅ All other fields remain properly synced

---

## 📊 **SUMMARY TABLE**

| Field | Status | Default Value | Behavior | Action Needed |
|-------|--------|---------------|----------|---------------|
| `role_id` | ✅ Updated | NULL | Manual assignment | Deploy changes |
| `subscription_plan` | ✅ Updated | 'Free' | Active free plan | Deploy changes |
| `subscription_end_date` | ✅ Correct | NULL | No expiration | None |
| `last_sign_in_at` | ✅ Fixed | Current timestamp | Updates on login | Deploy SQL fixes |

---

## 🚀 **DEPLOYMENT NOTES**

1. **Apply SQL changes** to Supabase database
2. **Redeploy Flutter app** with updated service
3. **Test with actual Google sign-in** to verify fix
4. **Monitor logs** for any sync errors

The fixes ensure proper tracking of user sign-in activity while maintaining all existing functionality and default values.
