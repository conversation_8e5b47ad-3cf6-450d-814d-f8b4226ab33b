import 'package:firebase_analytics/firebase_analytics.dart';
import '../models/sync_result.dart';

/// Service for tracking analytics events throughout the app
class AnalyticsService {
  static AnalyticsService? _instance;
  static AnalyticsService get instance => _instance ??= AnalyticsService._();
  
  AnalyticsService._();
  
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  
  /// Track user sync to Supabase event
  Future<void> trackUserSyncedToSupabase({
    required SyncResult syncResult,
    required Duration syncDuration,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'user_synced_to_supabase',
        parameters: {
          'operation_type': syncResult.operationType.name,
          'auth_provider': syncResult.authProvider ?? 'unknown',
          'sync_duration_ms': syncDuration.inMilliseconds,
          'profile_id': syncResult.profileId ?? 'unknown',
          'firebase_uid': syncResult.firebaseUid ?? 'unknown',
          'email_verified': syncResult.additionalData?['emailVerified'] ?? false,
          'has_display_name': syncResult.displayName != null,
          'has_phone_number': syncResult.additionalData?['phoneNumber'] != null,
          'has_photo_url': syncResult.additionalData?['photoURL'] != null,
          'sync_timestamp': syncResult.syncTimestamp?.toIso8601String() ?? DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track user_synced_to_supabase event: $e');
    }
  }
  
  /// Track profile sync failure event
  Future<void> trackProfileSyncFailed({
    required SyncResult syncResult,
    required Duration syncDuration,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'profile_sync_failed',
        parameters: {
          'operation_type': syncResult.operationType.name,
          'auth_provider': syncResult.authProvider ?? 'unknown',
          'error_type': syncResult.errorType?.name ?? 'unknown',
          'error_code': syncResult.errorCode ?? 'unknown',
          'error_message': syncResult.errorMessage ?? 'Unknown error',
          'sync_duration_ms': syncDuration.inMilliseconds,
          'firebase_uid': syncResult.firebaseUid ?? 'unknown',
          'sync_timestamp': syncResult.syncTimestamp?.toIso8601String() ?? DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track profile_sync_failed event: $e');
    }
  }
  
  /// Track profile creation success event
  Future<void> trackProfileCreationSuccess({
    required SyncResult syncResult,
    required Duration syncDuration,
    bool isNewUser = true,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'profile_creation_success',
        parameters: {
          'auth_provider': syncResult.authProvider ?? 'unknown',
          'profile_id': syncResult.profileId ?? 'unknown',
          'firebase_uid': syncResult.firebaseUid ?? 'unknown',
          'is_new_user': isNewUser,
          'creation_duration_ms': syncDuration.inMilliseconds,
          'email_verified': syncResult.additionalData?['emailVerified'] ?? false,
          'has_display_name': syncResult.displayName != null,
          'has_phone_number': syncResult.additionalData?['phoneNumber'] != null,
          'has_photo_url': syncResult.additionalData?['photoURL'] != null,
          'sync_timestamp': syncResult.syncTimestamp?.toIso8601String() ?? DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track profile_creation_success event: $e');
    }
  }
  
  /// Track sync retry attempt event
  Future<void> trackSyncRetryAttempted({
    required String firebaseUid,
    required String authProvider,
    required int retryAttempt,
    required String lastErrorType,
    required String lastErrorMessage,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'sync_retry_attempted',
        parameters: {
          'firebase_uid': firebaseUid,
          'auth_provider': authProvider,
          'retry_attempt': retryAttempt,
          'last_error_type': lastErrorType,
          'last_error_message': lastErrorMessage,
          'retry_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track sync_retry_attempted event: $e');
    }
  }
  
  /// Track profile completion prompt shown event
  Future<void> trackProfileCompletionPromptShown({
    required String firebaseUid,
    required String authProvider,
    required List<String> missingFields,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'profile_completion_prompt_shown',
        parameters: {
          'firebase_uid': firebaseUid,
          'auth_provider': authProvider,
          'missing_fields_count': missingFields.length,
          'missing_fields': missingFields.join(','),
          'prompt_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track profile_completion_prompt_shown event: $e');
    }
  }

  /// Track sync queued for later event
  Future<void> trackSyncQueuedForLater({
    required String firebaseUid,
    required String authProvider,
    required int queueSize,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'sync_queued_for_later',
        parameters: {
          'firebase_uid': firebaseUid,
          'auth_provider': authProvider,
          'queue_size': queueSize,
          'queued_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track sync_queued_for_later event: $e');
    }
  }

  /// Track sync queue processed event
  Future<void> trackSyncQueueProcessed({
    required String firebaseUid,
    required String authProvider,
    required int retryCount,
    required bool isSuccess,
    String? errorType,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'sync_queue_processed',
        parameters: {
          'firebase_uid': firebaseUid,
          'auth_provider': authProvider,
          'retry_count': retryCount,
          'is_success': isSuccess,
          'error_type': errorType ?? '',
          'processed_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track sync_queue_processed event: $e');
    }
  }

  /// Track manual sync retry event
  Future<void> trackSyncManualRetry({
    required String firebaseUid,
    required String authProvider,
    required bool isSuccess,
    String? errorType,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'sync_manual_retry',
        parameters: {
          'firebase_uid': firebaseUid,
          'auth_provider': authProvider,
          'is_success': isSuccess,
          'error_type': errorType ?? '',
          'retry_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track sync_manual_retry event: $e');
    }
  }

  /// Track sync health check event
  Future<void> trackSyncHealthCheck({
    required String overallHealth,
    required int queueSize,
    required int failedSyncs,
    required double errorRate,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'sync_health_check',
        parameters: {
          'overall_health': overallHealth,
          'queue_size': queueSize,
          'failed_syncs': failedSyncs,
          'error_rate': errorRate,
          'check_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track sync_health_check event: $e');
    }
  }

  /// Track manual sync attempted event
  Future<void> trackManualSyncAttempted({
    required String firebaseUid,
    required String authProvider,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'manual_sync_attempted',
        parameters: {
          'firebase_uid': firebaseUid,
          'auth_provider': authProvider,
          'attempt_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_sync_attempted event: $e');
    }
  }

  /// Track manual sync completed event
  Future<void> trackManualSyncCompleted({
    required String firebaseUid,
    required String authProvider,
    required bool isSuccess,
    String? errorType,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'manual_sync_completed',
        parameters: {
          'firebase_uid': firebaseUid,
          'auth_provider': authProvider,
          'is_success': isSuccess,
          'error_type': errorType ?? '',
          'completion_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_sync_completed event: $e');
    }
  }

  /// Track manual retry attempted event
  Future<void> trackManualRetryAttempted({
    required String firebaseUid,
    required String retryType,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'manual_retry_attempted',
        parameters: {
          'firebase_uid': firebaseUid,
          'retry_type': retryType,
          'attempt_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_retry_attempted event: $e');
    }
  }

  /// Track manual retry completed event
  Future<void> trackManualRetryCompleted({
    required String firebaseUid,
    required String retryType,
    required bool isSuccess,
    String? errorType,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'manual_retry_completed',
        parameters: {
          'firebase_uid': firebaseUid,
          'retry_type': retryType,
          'is_success': isSuccess,
          'error_type': errorType ?? '',
          'completion_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_retry_completed event: $e');
    }
  }

  /// Track manual queue processing started event
  Future<void> trackManualQueueProcessingStarted() async {
    try {
      await _analytics.logEvent(
        name: 'manual_queue_processing_started',
        parameters: {
          'start_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_queue_processing_started event: $e');
    }
  }

  /// Track manual queue processing completed event
  Future<void> trackManualQueueProcessingCompleted({
    required int initialQueueSize,
    required int processedCount,
    required int remainingCount,
    required bool isSuccess,
    String? errorMessage,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'manual_queue_processing_completed',
        parameters: {
          'initial_queue_size': initialQueueSize,
          'processed_count': processedCount,
          'remaining_count': remainingCount,
          'is_success': isSuccess,
          'error_message': errorMessage ?? '',
          'completion_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_queue_processing_completed event: $e');
    }
  }

  /// Track manual clear failed syncs event
  Future<void> trackManualClearFailedSyncs() async {
    try {
      await _analytics.logEvent(
        name: 'manual_clear_failed_syncs',
        parameters: {
          'clear_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_clear_failed_syncs event: $e');
    }
  }

  /// Track manual force sync event
  Future<void> trackManualForceSync({
    required String firebaseUid,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'manual_force_sync',
        parameters: {
          'firebase_uid': firebaseUid,
          'force_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track manual_force_sync event: $e');
    }
  }

  /// Track sync error logged event
  Future<void> trackSyncErrorLogged({
    required String errorType,
    required String operationType,
    required String context,
    String? firebaseUid,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'sync_error_logged',
        parameters: {
          'error_type': errorType,
          'operation_type': operationType,
          'context': context,
          'firebase_uid': firebaseUid ?? '',
          'log_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track sync_error_logged event: $e');
    }
  }

  /// Track application error logged event
  Future<void> trackApplicationErrorLogged({
    required String errorType,
    required String context,
    required String errorMessage,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'application_error_logged',
        parameters: {
          'error_type': errorType,
          'context': context,
          'error_message': errorMessage.length > 100
              ? errorMessage.substring(0, 100)
              : errorMessage,
          'log_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track application_error_logged event: $e');
    }
  }
  
  /// Track profile fields updated manually event
  Future<void> trackProfileFieldsUpdatedManually({
    required String firebaseUid,
    required List<String> updatedFields,
    required String updateSource,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'profile_fields_updated_manually',
        parameters: {
          'firebase_uid': firebaseUid,
          'updated_fields_count': updatedFields.length,
          'updated_fields': updatedFields.join(','),
          'update_source': updateSource, // e.g., 'profile_screen', 'onboarding', 'settings'
          'update_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track profile_fields_updated_manually event: $e');
    }
  }
  
  /// Track authentication method usage
  Future<void> trackAuthenticationMethod({
    required String authProvider,
    required String authAction, // 'sign_in', 'sign_up'
    required bool isSuccess,
    String? errorType,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'authentication_method_used',
        parameters: {
          'auth_provider': authProvider,
          'auth_action': authAction,
          'is_success': isSuccess,
          'error_type': errorType ?? 'none',
          'auth_timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      print('Failed to track authentication_method_used event: $e');
    }
  }
  
  /// Set user properties for analytics
  Future<void> setUserProperties({
    required String firebaseUid,
    String? authProvider,
    bool? emailVerified,
    bool? hasDisplayName,
    bool? hasPhoneNumber,
    String? userRole,
  }) async {
    try {
      await _analytics.setUserId(id: firebaseUid);
      
      if (authProvider != null) {
        await _analytics.setUserProperty(name: 'auth_provider', value: authProvider);
      }
      
      if (emailVerified != null) {
        await _analytics.setUserProperty(name: 'email_verified', value: emailVerified.toString());
      }
      
      if (hasDisplayName != null) {
        await _analytics.setUserProperty(name: 'has_display_name', value: hasDisplayName.toString());
      }
      
      if (hasPhoneNumber != null) {
        await _analytics.setUserProperty(name: 'has_phone_number', value: hasPhoneNumber.toString());
      }
      
      if (userRole != null) {
        await _analytics.setUserProperty(name: 'user_role', value: userRole);
      }
    } catch (e) {
      print('Failed to set user properties: $e');
    }
  }
  
  /// Enable or disable analytics collection
  Future<void> setAnalyticsEnabled(bool enabled) async {
    try {
      await _analytics.setAnalyticsCollectionEnabled(enabled);
    } catch (e) {
      print('Failed to set analytics collection enabled: $e');
    }
  }
  
  /// Reset analytics data (e.g., on sign out)
  Future<void> resetAnalyticsData() async {
    try {
      await _analytics.resetAnalyticsData();
    } catch (e) {
      print('Failed to reset analytics data: $e');
    }
  }
}
