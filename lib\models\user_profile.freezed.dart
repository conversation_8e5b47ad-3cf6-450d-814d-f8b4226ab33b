// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) {
  return _UserProfile.fromJson(json);
}

/// @nodoc
mixin _$UserProfile {
  String get id => throw _privateConstructorUsedError;
  String get firebaseUid => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get displayName => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get photoUrl => throw _privateConstructorUsedError;
  bool get isEmailVerified => throw _privateConstructorUsedError;
  String get authProvider => throw _privateConstructorUsedError;
  String? get role =>
      throw _privateConstructorUsedError; // Personal information
  DateTime? get dateOfBirth => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  String? get nativeLanguage =>
      throw _privateConstructorUsedError; // Address information
  String? get addressLine1 => throw _privateConstructorUsedError;
  String? get addressLine2 => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  String? get state => throw _privateConstructorUsedError;
  String? get postalCode => throw _privateConstructorUsedError;
  String get country =>
      throw _privateConstructorUsedError; // Professional information
  String? get occupation => throw _privateConstructorUsedError;
  String? get companyName => throw _privateConstructorUsedError;
  double? get annualIncome =>
      throw _privateConstructorUsedError; // App preferences
  bool get pushNotificationsEnabled => throw _privateConstructorUsedError;
  bool get emailNotificationsEnabled => throw _privateConstructorUsedError;
  bool get smsNotificationsEnabled => throw _privateConstructorUsedError;
  bool get locationServicesEnabled => throw _privateConstructorUsedError;
  bool get analyticsEnabled => throw _privateConstructorUsedError;
  bool get darkModeEnabled => throw _privateConstructorUsedError;
  bool get biometricEnabled => throw _privateConstructorUsedError;
  bool get autoDownloadEnabled => throw _privateConstructorUsedError;
  bool get profileCompletionDontAskAgain =>
      throw _privateConstructorUsedError; // Subscription information
  String get subscriptionPlan => throw _privateConstructorUsedError;
  String get subscriptionStatus => throw _privateConstructorUsedError;
  DateTime? get subscriptionStartDate => throw _privateConstructorUsedError;
  DateTime? get subscriptionEndDate =>
      throw _privateConstructorUsedError; // Profile completion and onboarding
  int get profileCompletionPercentage => throw _privateConstructorUsedError;
  bool get onboardingCompleted => throw _privateConstructorUsedError;
  bool get termsAccepted => throw _privateConstructorUsedError;
  bool get privacyPolicyAccepted =>
      throw _privateConstructorUsedError; // Sync tracking
  int get syncVersion => throw _privateConstructorUsedError;
  DateTime? get lastSignInAt => throw _privateConstructorUsedError;
  DateTime? get lastActiveAt => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this UserProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserProfileCopyWith<UserProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserProfileCopyWith<$Res> {
  factory $UserProfileCopyWith(
    UserProfile value,
    $Res Function(UserProfile) then,
  ) = _$UserProfileCopyWithImpl<$Res, UserProfile>;
  @useResult
  $Res call({
    String id,
    String firebaseUid,
    String? email,
    String? displayName,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? photoUrl,
    bool isEmailVerified,
    String authProvider,
    String? role,
    DateTime? dateOfBirth,
    String? gender,
    String? nativeLanguage,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String country,
    String? occupation,
    String? companyName,
    double? annualIncome,
    bool pushNotificationsEnabled,
    bool emailNotificationsEnabled,
    bool smsNotificationsEnabled,
    bool locationServicesEnabled,
    bool analyticsEnabled,
    bool darkModeEnabled,
    bool biometricEnabled,
    bool autoDownloadEnabled,
    bool profileCompletionDontAskAgain,
    String subscriptionPlan,
    String subscriptionStatus,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
    int profileCompletionPercentage,
    bool onboardingCompleted,
    bool termsAccepted,
    bool privacyPolicyAccepted,
    int syncVersion,
    DateTime? lastSignInAt,
    DateTime? lastActiveAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class _$UserProfileCopyWithImpl<$Res, $Val extends UserProfile>
    implements $UserProfileCopyWith<$Res> {
  _$UserProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firebaseUid = null,
    Object? email = freezed,
    Object? displayName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? phoneNumber = freezed,
    Object? photoUrl = freezed,
    Object? isEmailVerified = null,
    Object? authProvider = null,
    Object? role = freezed,
    Object? dateOfBirth = freezed,
    Object? gender = freezed,
    Object? nativeLanguage = freezed,
    Object? addressLine1 = freezed,
    Object? addressLine2 = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = null,
    Object? occupation = freezed,
    Object? companyName = freezed,
    Object? annualIncome = freezed,
    Object? pushNotificationsEnabled = null,
    Object? emailNotificationsEnabled = null,
    Object? smsNotificationsEnabled = null,
    Object? locationServicesEnabled = null,
    Object? analyticsEnabled = null,
    Object? darkModeEnabled = null,
    Object? biometricEnabled = null,
    Object? autoDownloadEnabled = null,
    Object? profileCompletionDontAskAgain = null,
    Object? subscriptionPlan = null,
    Object? subscriptionStatus = null,
    Object? subscriptionStartDate = freezed,
    Object? subscriptionEndDate = freezed,
    Object? profileCompletionPercentage = null,
    Object? onboardingCompleted = null,
    Object? termsAccepted = null,
    Object? privacyPolicyAccepted = null,
    Object? syncVersion = null,
    Object? lastSignInAt = freezed,
    Object? lastActiveAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            firebaseUid: null == firebaseUid
                ? _value.firebaseUid
                : firebaseUid // ignore: cast_nullable_to_non_nullable
                      as String,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
            displayName: freezed == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                      as String?,
            firstName: freezed == firstName
                ? _value.firstName
                : firstName // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastName: freezed == lastName
                ? _value.lastName
                : lastName // ignore: cast_nullable_to_non_nullable
                      as String?,
            phoneNumber: freezed == phoneNumber
                ? _value.phoneNumber
                : phoneNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            photoUrl: freezed == photoUrl
                ? _value.photoUrl
                : photoUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            isEmailVerified: null == isEmailVerified
                ? _value.isEmailVerified
                : isEmailVerified // ignore: cast_nullable_to_non_nullable
                      as bool,
            authProvider: null == authProvider
                ? _value.authProvider
                : authProvider // ignore: cast_nullable_to_non_nullable
                      as String,
            role: freezed == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as String?,
            dateOfBirth: freezed == dateOfBirth
                ? _value.dateOfBirth
                : dateOfBirth // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            gender: freezed == gender
                ? _value.gender
                : gender // ignore: cast_nullable_to_non_nullable
                      as String?,
            nativeLanguage: freezed == nativeLanguage
                ? _value.nativeLanguage
                : nativeLanguage // ignore: cast_nullable_to_non_nullable
                      as String?,
            addressLine1: freezed == addressLine1
                ? _value.addressLine1
                : addressLine1 // ignore: cast_nullable_to_non_nullable
                      as String?,
            addressLine2: freezed == addressLine2
                ? _value.addressLine2
                : addressLine2 // ignore: cast_nullable_to_non_nullable
                      as String?,
            city: freezed == city
                ? _value.city
                : city // ignore: cast_nullable_to_non_nullable
                      as String?,
            state: freezed == state
                ? _value.state
                : state // ignore: cast_nullable_to_non_nullable
                      as String?,
            postalCode: freezed == postalCode
                ? _value.postalCode
                : postalCode // ignore: cast_nullable_to_non_nullable
                      as String?,
            country: null == country
                ? _value.country
                : country // ignore: cast_nullable_to_non_nullable
                      as String,
            occupation: freezed == occupation
                ? _value.occupation
                : occupation // ignore: cast_nullable_to_non_nullable
                      as String?,
            companyName: freezed == companyName
                ? _value.companyName
                : companyName // ignore: cast_nullable_to_non_nullable
                      as String?,
            annualIncome: freezed == annualIncome
                ? _value.annualIncome
                : annualIncome // ignore: cast_nullable_to_non_nullable
                      as double?,
            pushNotificationsEnabled: null == pushNotificationsEnabled
                ? _value.pushNotificationsEnabled
                : pushNotificationsEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            emailNotificationsEnabled: null == emailNotificationsEnabled
                ? _value.emailNotificationsEnabled
                : emailNotificationsEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            smsNotificationsEnabled: null == smsNotificationsEnabled
                ? _value.smsNotificationsEnabled
                : smsNotificationsEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            locationServicesEnabled: null == locationServicesEnabled
                ? _value.locationServicesEnabled
                : locationServicesEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            analyticsEnabled: null == analyticsEnabled
                ? _value.analyticsEnabled
                : analyticsEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            darkModeEnabled: null == darkModeEnabled
                ? _value.darkModeEnabled
                : darkModeEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            biometricEnabled: null == biometricEnabled
                ? _value.biometricEnabled
                : biometricEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            autoDownloadEnabled: null == autoDownloadEnabled
                ? _value.autoDownloadEnabled
                : autoDownloadEnabled // ignore: cast_nullable_to_non_nullable
                      as bool,
            profileCompletionDontAskAgain: null == profileCompletionDontAskAgain
                ? _value.profileCompletionDontAskAgain
                : profileCompletionDontAskAgain // ignore: cast_nullable_to_non_nullable
                      as bool,
            subscriptionPlan: null == subscriptionPlan
                ? _value.subscriptionPlan
                : subscriptionPlan // ignore: cast_nullable_to_non_nullable
                      as String,
            subscriptionStatus: null == subscriptionStatus
                ? _value.subscriptionStatus
                : subscriptionStatus // ignore: cast_nullable_to_non_nullable
                      as String,
            subscriptionStartDate: freezed == subscriptionStartDate
                ? _value.subscriptionStartDate
                : subscriptionStartDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            subscriptionEndDate: freezed == subscriptionEndDate
                ? _value.subscriptionEndDate
                : subscriptionEndDate // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            profileCompletionPercentage: null == profileCompletionPercentage
                ? _value.profileCompletionPercentage
                : profileCompletionPercentage // ignore: cast_nullable_to_non_nullable
                      as int,
            onboardingCompleted: null == onboardingCompleted
                ? _value.onboardingCompleted
                : onboardingCompleted // ignore: cast_nullable_to_non_nullable
                      as bool,
            termsAccepted: null == termsAccepted
                ? _value.termsAccepted
                : termsAccepted // ignore: cast_nullable_to_non_nullable
                      as bool,
            privacyPolicyAccepted: null == privacyPolicyAccepted
                ? _value.privacyPolicyAccepted
                : privacyPolicyAccepted // ignore: cast_nullable_to_non_nullable
                      as bool,
            syncVersion: null == syncVersion
                ? _value.syncVersion
                : syncVersion // ignore: cast_nullable_to_non_nullable
                      as int,
            lastSignInAt: freezed == lastSignInAt
                ? _value.lastSignInAt
                : lastSignInAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            lastActiveAt: freezed == lastActiveAt
                ? _value.lastActiveAt
                : lastActiveAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserProfileImplCopyWith<$Res>
    implements $UserProfileCopyWith<$Res> {
  factory _$$UserProfileImplCopyWith(
    _$UserProfileImpl value,
    $Res Function(_$UserProfileImpl) then,
  ) = __$$UserProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String firebaseUid,
    String? email,
    String? displayName,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? photoUrl,
    bool isEmailVerified,
    String authProvider,
    String? role,
    DateTime? dateOfBirth,
    String? gender,
    String? nativeLanguage,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String country,
    String? occupation,
    String? companyName,
    double? annualIncome,
    bool pushNotificationsEnabled,
    bool emailNotificationsEnabled,
    bool smsNotificationsEnabled,
    bool locationServicesEnabled,
    bool analyticsEnabled,
    bool darkModeEnabled,
    bool biometricEnabled,
    bool autoDownloadEnabled,
    bool profileCompletionDontAskAgain,
    String subscriptionPlan,
    String subscriptionStatus,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
    int profileCompletionPercentage,
    bool onboardingCompleted,
    bool termsAccepted,
    bool privacyPolicyAccepted,
    int syncVersion,
    DateTime? lastSignInAt,
    DateTime? lastActiveAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  });
}

/// @nodoc
class __$$UserProfileImplCopyWithImpl<$Res>
    extends _$UserProfileCopyWithImpl<$Res, _$UserProfileImpl>
    implements _$$UserProfileImplCopyWith<$Res> {
  __$$UserProfileImplCopyWithImpl(
    _$UserProfileImpl _value,
    $Res Function(_$UserProfileImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firebaseUid = null,
    Object? email = freezed,
    Object? displayName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? phoneNumber = freezed,
    Object? photoUrl = freezed,
    Object? isEmailVerified = null,
    Object? authProvider = null,
    Object? role = freezed,
    Object? dateOfBirth = freezed,
    Object? gender = freezed,
    Object? nativeLanguage = freezed,
    Object? addressLine1 = freezed,
    Object? addressLine2 = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = null,
    Object? occupation = freezed,
    Object? companyName = freezed,
    Object? annualIncome = freezed,
    Object? pushNotificationsEnabled = null,
    Object? emailNotificationsEnabled = null,
    Object? smsNotificationsEnabled = null,
    Object? locationServicesEnabled = null,
    Object? analyticsEnabled = null,
    Object? darkModeEnabled = null,
    Object? biometricEnabled = null,
    Object? autoDownloadEnabled = null,
    Object? profileCompletionDontAskAgain = null,
    Object? subscriptionPlan = null,
    Object? subscriptionStatus = null,
    Object? subscriptionStartDate = freezed,
    Object? subscriptionEndDate = freezed,
    Object? profileCompletionPercentage = null,
    Object? onboardingCompleted = null,
    Object? termsAccepted = null,
    Object? privacyPolicyAccepted = null,
    Object? syncVersion = null,
    Object? lastSignInAt = freezed,
    Object? lastActiveAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(
      _$UserProfileImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        firebaseUid: null == firebaseUid
            ? _value.firebaseUid
            : firebaseUid // ignore: cast_nullable_to_non_nullable
                  as String,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        displayName: freezed == displayName
            ? _value.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String?,
        firstName: freezed == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastName: freezed == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String?,
        phoneNumber: freezed == phoneNumber
            ? _value.phoneNumber
            : phoneNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        photoUrl: freezed == photoUrl
            ? _value.photoUrl
            : photoUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        isEmailVerified: null == isEmailVerified
            ? _value.isEmailVerified
            : isEmailVerified // ignore: cast_nullable_to_non_nullable
                  as bool,
        authProvider: null == authProvider
            ? _value.authProvider
            : authProvider // ignore: cast_nullable_to_non_nullable
                  as String,
        role: freezed == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as String?,
        dateOfBirth: freezed == dateOfBirth
            ? _value.dateOfBirth
            : dateOfBirth // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        gender: freezed == gender
            ? _value.gender
            : gender // ignore: cast_nullable_to_non_nullable
                  as String?,
        nativeLanguage: freezed == nativeLanguage
            ? _value.nativeLanguage
            : nativeLanguage // ignore: cast_nullable_to_non_nullable
                  as String?,
        addressLine1: freezed == addressLine1
            ? _value.addressLine1
            : addressLine1 // ignore: cast_nullable_to_non_nullable
                  as String?,
        addressLine2: freezed == addressLine2
            ? _value.addressLine2
            : addressLine2 // ignore: cast_nullable_to_non_nullable
                  as String?,
        city: freezed == city
            ? _value.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String?,
        state: freezed == state
            ? _value.state
            : state // ignore: cast_nullable_to_non_nullable
                  as String?,
        postalCode: freezed == postalCode
            ? _value.postalCode
            : postalCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        country: null == country
            ? _value.country
            : country // ignore: cast_nullable_to_non_nullable
                  as String,
        occupation: freezed == occupation
            ? _value.occupation
            : occupation // ignore: cast_nullable_to_non_nullable
                  as String?,
        companyName: freezed == companyName
            ? _value.companyName
            : companyName // ignore: cast_nullable_to_non_nullable
                  as String?,
        annualIncome: freezed == annualIncome
            ? _value.annualIncome
            : annualIncome // ignore: cast_nullable_to_non_nullable
                  as double?,
        pushNotificationsEnabled: null == pushNotificationsEnabled
            ? _value.pushNotificationsEnabled
            : pushNotificationsEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        emailNotificationsEnabled: null == emailNotificationsEnabled
            ? _value.emailNotificationsEnabled
            : emailNotificationsEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        smsNotificationsEnabled: null == smsNotificationsEnabled
            ? _value.smsNotificationsEnabled
            : smsNotificationsEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        locationServicesEnabled: null == locationServicesEnabled
            ? _value.locationServicesEnabled
            : locationServicesEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        analyticsEnabled: null == analyticsEnabled
            ? _value.analyticsEnabled
            : analyticsEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        darkModeEnabled: null == darkModeEnabled
            ? _value.darkModeEnabled
            : darkModeEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        biometricEnabled: null == biometricEnabled
            ? _value.biometricEnabled
            : biometricEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        autoDownloadEnabled: null == autoDownloadEnabled
            ? _value.autoDownloadEnabled
            : autoDownloadEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
        profileCompletionDontAskAgain: null == profileCompletionDontAskAgain
            ? _value.profileCompletionDontAskAgain
            : profileCompletionDontAskAgain // ignore: cast_nullable_to_non_nullable
                  as bool,
        subscriptionPlan: null == subscriptionPlan
            ? _value.subscriptionPlan
            : subscriptionPlan // ignore: cast_nullable_to_non_nullable
                  as String,
        subscriptionStatus: null == subscriptionStatus
            ? _value.subscriptionStatus
            : subscriptionStatus // ignore: cast_nullable_to_non_nullable
                  as String,
        subscriptionStartDate: freezed == subscriptionStartDate
            ? _value.subscriptionStartDate
            : subscriptionStartDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        subscriptionEndDate: freezed == subscriptionEndDate
            ? _value.subscriptionEndDate
            : subscriptionEndDate // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        profileCompletionPercentage: null == profileCompletionPercentage
            ? _value.profileCompletionPercentage
            : profileCompletionPercentage // ignore: cast_nullable_to_non_nullable
                  as int,
        onboardingCompleted: null == onboardingCompleted
            ? _value.onboardingCompleted
            : onboardingCompleted // ignore: cast_nullable_to_non_nullable
                  as bool,
        termsAccepted: null == termsAccepted
            ? _value.termsAccepted
            : termsAccepted // ignore: cast_nullable_to_non_nullable
                  as bool,
        privacyPolicyAccepted: null == privacyPolicyAccepted
            ? _value.privacyPolicyAccepted
            : privacyPolicyAccepted // ignore: cast_nullable_to_non_nullable
                  as bool,
        syncVersion: null == syncVersion
            ? _value.syncVersion
            : syncVersion // ignore: cast_nullable_to_non_nullable
                  as int,
        lastSignInAt: freezed == lastSignInAt
            ? _value.lastSignInAt
            : lastSignInAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        lastActiveAt: freezed == lastActiveAt
            ? _value.lastActiveAt
            : lastActiveAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserProfileImpl implements _UserProfile {
  const _$UserProfileImpl({
    required this.id,
    required this.firebaseUid,
    this.email,
    this.displayName,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.photoUrl,
    this.isEmailVerified = false,
    this.authProvider = 'email',
    this.role,
    this.dateOfBirth,
    this.gender,
    this.nativeLanguage,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.postalCode,
    this.country = 'India',
    this.occupation,
    this.companyName,
    this.annualIncome,
    this.pushNotificationsEnabled = true,
    this.emailNotificationsEnabled = false,
    this.smsNotificationsEnabled = true,
    this.locationServicesEnabled = true,
    this.analyticsEnabled = false,
    this.darkModeEnabled = false,
    this.biometricEnabled = true,
    this.autoDownloadEnabled = false,
    this.profileCompletionDontAskAgain = false,
    this.subscriptionPlan = 'Free',
    this.subscriptionStatus = 'active',
    this.subscriptionStartDate,
    this.subscriptionEndDate,
    this.profileCompletionPercentage = 0,
    this.onboardingCompleted = false,
    this.termsAccepted = false,
    this.privacyPolicyAccepted = false,
    this.syncVersion = 1,
    this.lastSignInAt,
    this.lastActiveAt,
    this.createdAt,
    this.updatedAt,
  });

  factory _$UserProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String firebaseUid;
  @override
  final String? email;
  @override
  final String? displayName;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? phoneNumber;
  @override
  final String? photoUrl;
  @override
  @JsonKey()
  final bool isEmailVerified;
  @override
  @JsonKey()
  final String authProvider;
  @override
  final String? role;
  // Personal information
  @override
  final DateTime? dateOfBirth;
  @override
  final String? gender;
  @override
  final String? nativeLanguage;
  // Address information
  @override
  final String? addressLine1;
  @override
  final String? addressLine2;
  @override
  final String? city;
  @override
  final String? state;
  @override
  final String? postalCode;
  @override
  @JsonKey()
  final String country;
  // Professional information
  @override
  final String? occupation;
  @override
  final String? companyName;
  @override
  final double? annualIncome;
  // App preferences
  @override
  @JsonKey()
  final bool pushNotificationsEnabled;
  @override
  @JsonKey()
  final bool emailNotificationsEnabled;
  @override
  @JsonKey()
  final bool smsNotificationsEnabled;
  @override
  @JsonKey()
  final bool locationServicesEnabled;
  @override
  @JsonKey()
  final bool analyticsEnabled;
  @override
  @JsonKey()
  final bool darkModeEnabled;
  @override
  @JsonKey()
  final bool biometricEnabled;
  @override
  @JsonKey()
  final bool autoDownloadEnabled;
  @override
  @JsonKey()
  final bool profileCompletionDontAskAgain;
  // Subscription information
  @override
  @JsonKey()
  final String subscriptionPlan;
  @override
  @JsonKey()
  final String subscriptionStatus;
  @override
  final DateTime? subscriptionStartDate;
  @override
  final DateTime? subscriptionEndDate;
  // Profile completion and onboarding
  @override
  @JsonKey()
  final int profileCompletionPercentage;
  @override
  @JsonKey()
  final bool onboardingCompleted;
  @override
  @JsonKey()
  final bool termsAccepted;
  @override
  @JsonKey()
  final bool privacyPolicyAccepted;
  // Sync tracking
  @override
  @JsonKey()
  final int syncVersion;
  @override
  final DateTime? lastSignInAt;
  @override
  final DateTime? lastActiveAt;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'UserProfile(id: $id, firebaseUid: $firebaseUid, email: $email, displayName: $displayName, firstName: $firstName, lastName: $lastName, phoneNumber: $phoneNumber, photoUrl: $photoUrl, isEmailVerified: $isEmailVerified, authProvider: $authProvider, role: $role, dateOfBirth: $dateOfBirth, gender: $gender, nativeLanguage: $nativeLanguage, addressLine1: $addressLine1, addressLine2: $addressLine2, city: $city, state: $state, postalCode: $postalCode, country: $country, occupation: $occupation, companyName: $companyName, annualIncome: $annualIncome, pushNotificationsEnabled: $pushNotificationsEnabled, emailNotificationsEnabled: $emailNotificationsEnabled, smsNotificationsEnabled: $smsNotificationsEnabled, locationServicesEnabled: $locationServicesEnabled, analyticsEnabled: $analyticsEnabled, darkModeEnabled: $darkModeEnabled, biometricEnabled: $biometricEnabled, autoDownloadEnabled: $autoDownloadEnabled, profileCompletionDontAskAgain: $profileCompletionDontAskAgain, subscriptionPlan: $subscriptionPlan, subscriptionStatus: $subscriptionStatus, subscriptionStartDate: $subscriptionStartDate, subscriptionEndDate: $subscriptionEndDate, profileCompletionPercentage: $profileCompletionPercentage, onboardingCompleted: $onboardingCompleted, termsAccepted: $termsAccepted, privacyPolicyAccepted: $privacyPolicyAccepted, syncVersion: $syncVersion, lastSignInAt: $lastSignInAt, lastActiveAt: $lastActiveAt, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firebaseUid, firebaseUid) ||
                other.firebaseUid == firebaseUid) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.photoUrl, photoUrl) ||
                other.photoUrl == photoUrl) &&
            (identical(other.isEmailVerified, isEmailVerified) ||
                other.isEmailVerified == isEmailVerified) &&
            (identical(other.authProvider, authProvider) ||
                other.authProvider == authProvider) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.nativeLanguage, nativeLanguage) ||
                other.nativeLanguage == nativeLanguage) &&
            (identical(other.addressLine1, addressLine1) ||
                other.addressLine1 == addressLine1) &&
            (identical(other.addressLine2, addressLine2) ||
                other.addressLine2 == addressLine2) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.annualIncome, annualIncome) ||
                other.annualIncome == annualIncome) &&
            (identical(
                  other.pushNotificationsEnabled,
                  pushNotificationsEnabled,
                ) ||
                other.pushNotificationsEnabled == pushNotificationsEnabled) &&
            (identical(
                  other.emailNotificationsEnabled,
                  emailNotificationsEnabled,
                ) ||
                other.emailNotificationsEnabled == emailNotificationsEnabled) &&
            (identical(
                  other.smsNotificationsEnabled,
                  smsNotificationsEnabled,
                ) ||
                other.smsNotificationsEnabled == smsNotificationsEnabled) &&
            (identical(
                  other.locationServicesEnabled,
                  locationServicesEnabled,
                ) ||
                other.locationServicesEnabled == locationServicesEnabled) &&
            (identical(other.analyticsEnabled, analyticsEnabled) ||
                other.analyticsEnabled == analyticsEnabled) &&
            (identical(other.darkModeEnabled, darkModeEnabled) ||
                other.darkModeEnabled == darkModeEnabled) &&
            (identical(other.biometricEnabled, biometricEnabled) ||
                other.biometricEnabled == biometricEnabled) &&
            (identical(other.autoDownloadEnabled, autoDownloadEnabled) ||
                other.autoDownloadEnabled == autoDownloadEnabled) &&
            (identical(
                  other.profileCompletionDontAskAgain,
                  profileCompletionDontAskAgain,
                ) ||
                other.profileCompletionDontAskAgain ==
                    profileCompletionDontAskAgain) &&
            (identical(other.subscriptionPlan, subscriptionPlan) ||
                other.subscriptionPlan == subscriptionPlan) &&
            (identical(other.subscriptionStatus, subscriptionStatus) ||
                other.subscriptionStatus == subscriptionStatus) &&
            (identical(other.subscriptionStartDate, subscriptionStartDate) ||
                other.subscriptionStartDate == subscriptionStartDate) &&
            (identical(other.subscriptionEndDate, subscriptionEndDate) ||
                other.subscriptionEndDate == subscriptionEndDate) &&
            (identical(
                  other.profileCompletionPercentage,
                  profileCompletionPercentage,
                ) ||
                other.profileCompletionPercentage ==
                    profileCompletionPercentage) &&
            (identical(other.onboardingCompleted, onboardingCompleted) ||
                other.onboardingCompleted == onboardingCompleted) &&
            (identical(other.termsAccepted, termsAccepted) ||
                other.termsAccepted == termsAccepted) &&
            (identical(other.privacyPolicyAccepted, privacyPolicyAccepted) ||
                other.privacyPolicyAccepted == privacyPolicyAccepted) &&
            (identical(other.syncVersion, syncVersion) ||
                other.syncVersion == syncVersion) &&
            (identical(other.lastSignInAt, lastSignInAt) ||
                other.lastSignInAt == lastSignInAt) &&
            (identical(other.lastActiveAt, lastActiveAt) ||
                other.lastActiveAt == lastActiveAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    id,
    firebaseUid,
    email,
    displayName,
    firstName,
    lastName,
    phoneNumber,
    photoUrl,
    isEmailVerified,
    authProvider,
    role,
    dateOfBirth,
    gender,
    nativeLanguage,
    addressLine1,
    addressLine2,
    city,
    state,
    postalCode,
    country,
    occupation,
    companyName,
    annualIncome,
    pushNotificationsEnabled,
    emailNotificationsEnabled,
    smsNotificationsEnabled,
    locationServicesEnabled,
    analyticsEnabled,
    darkModeEnabled,
    biometricEnabled,
    autoDownloadEnabled,
    profileCompletionDontAskAgain,
    subscriptionPlan,
    subscriptionStatus,
    subscriptionStartDate,
    subscriptionEndDate,
    profileCompletionPercentage,
    onboardingCompleted,
    termsAccepted,
    privacyPolicyAccepted,
    syncVersion,
    lastSignInAt,
    lastActiveAt,
    createdAt,
    updatedAt,
  ]);

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      __$$UserProfileImplCopyWithImpl<_$UserProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserProfileImplToJson(this);
  }
}

abstract class _UserProfile implements UserProfile {
  const factory _UserProfile({
    required final String id,
    required final String firebaseUid,
    final String? email,
    final String? displayName,
    final String? firstName,
    final String? lastName,
    final String? phoneNumber,
    final String? photoUrl,
    final bool isEmailVerified,
    final String authProvider,
    final String? role,
    final DateTime? dateOfBirth,
    final String? gender,
    final String? nativeLanguage,
    final String? addressLine1,
    final String? addressLine2,
    final String? city,
    final String? state,
    final String? postalCode,
    final String country,
    final String? occupation,
    final String? companyName,
    final double? annualIncome,
    final bool pushNotificationsEnabled,
    final bool emailNotificationsEnabled,
    final bool smsNotificationsEnabled,
    final bool locationServicesEnabled,
    final bool analyticsEnabled,
    final bool darkModeEnabled,
    final bool biometricEnabled,
    final bool autoDownloadEnabled,
    final bool profileCompletionDontAskAgain,
    final String subscriptionPlan,
    final String subscriptionStatus,
    final DateTime? subscriptionStartDate,
    final DateTime? subscriptionEndDate,
    final int profileCompletionPercentage,
    final bool onboardingCompleted,
    final bool termsAccepted,
    final bool privacyPolicyAccepted,
    final int syncVersion,
    final DateTime? lastSignInAt,
    final DateTime? lastActiveAt,
    final DateTime? createdAt,
    final DateTime? updatedAt,
  }) = _$UserProfileImpl;

  factory _UserProfile.fromJson(Map<String, dynamic> json) =
      _$UserProfileImpl.fromJson;

  @override
  String get id;
  @override
  String get firebaseUid;
  @override
  String? get email;
  @override
  String? get displayName;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get phoneNumber;
  @override
  String? get photoUrl;
  @override
  bool get isEmailVerified;
  @override
  String get authProvider;
  @override
  String? get role; // Personal information
  @override
  DateTime? get dateOfBirth;
  @override
  String? get gender;
  @override
  String? get nativeLanguage; // Address information
  @override
  String? get addressLine1;
  @override
  String? get addressLine2;
  @override
  String? get city;
  @override
  String? get state;
  @override
  String? get postalCode;
  @override
  String get country; // Professional information
  @override
  String? get occupation;
  @override
  String? get companyName;
  @override
  double? get annualIncome; // App preferences
  @override
  bool get pushNotificationsEnabled;
  @override
  bool get emailNotificationsEnabled;
  @override
  bool get smsNotificationsEnabled;
  @override
  bool get locationServicesEnabled;
  @override
  bool get analyticsEnabled;
  @override
  bool get darkModeEnabled;
  @override
  bool get biometricEnabled;
  @override
  bool get autoDownloadEnabled;
  @override
  bool get profileCompletionDontAskAgain; // Subscription information
  @override
  String get subscriptionPlan;
  @override
  String get subscriptionStatus;
  @override
  DateTime? get subscriptionStartDate;
  @override
  DateTime? get subscriptionEndDate; // Profile completion and onboarding
  @override
  int get profileCompletionPercentage;
  @override
  bool get onboardingCompleted;
  @override
  bool get termsAccepted;
  @override
  bool get privacyPolicyAccepted; // Sync tracking
  @override
  int get syncVersion;
  @override
  DateTime? get lastSignInAt;
  @override
  DateTime? get lastActiveAt;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProfileUpdateRequest _$ProfileUpdateRequestFromJson(Map<String, dynamic> json) {
  return _ProfileUpdateRequest.fromJson(json);
}

/// @nodoc
mixin _$ProfileUpdateRequest {
  String? get displayName => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  DateTime? get dateOfBirth => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  String? get nativeLanguage => throw _privateConstructorUsedError;
  String? get addressLine1 => throw _privateConstructorUsedError;
  String? get addressLine2 => throw _privateConstructorUsedError;
  String? get city => throw _privateConstructorUsedError;
  String? get state => throw _privateConstructorUsedError;
  String? get postalCode => throw _privateConstructorUsedError;
  String? get country => throw _privateConstructorUsedError;
  String? get occupation => throw _privateConstructorUsedError;
  String? get companyName => throw _privateConstructorUsedError;
  double? get annualIncome => throw _privateConstructorUsedError;
  bool? get profileCompletionDontAskAgain => throw _privateConstructorUsedError;
  int? get syncVersion => throw _privateConstructorUsedError;

  /// Serializes this ProfileUpdateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileUpdateRequestCopyWith<ProfileUpdateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileUpdateRequestCopyWith<$Res> {
  factory $ProfileUpdateRequestCopyWith(
    ProfileUpdateRequest value,
    $Res Function(ProfileUpdateRequest) then,
  ) = _$ProfileUpdateRequestCopyWithImpl<$Res, ProfileUpdateRequest>;
  @useResult
  $Res call({
    String? displayName,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? gender,
    String? nativeLanguage,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    String? occupation,
    String? companyName,
    double? annualIncome,
    bool? profileCompletionDontAskAgain,
    int? syncVersion,
  });
}

/// @nodoc
class _$ProfileUpdateRequestCopyWithImpl<
  $Res,
  $Val extends ProfileUpdateRequest
>
    implements $ProfileUpdateRequestCopyWith<$Res> {
  _$ProfileUpdateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? gender = freezed,
    Object? nativeLanguage = freezed,
    Object? addressLine1 = freezed,
    Object? addressLine2 = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = freezed,
    Object? occupation = freezed,
    Object? companyName = freezed,
    Object? annualIncome = freezed,
    Object? profileCompletionDontAskAgain = freezed,
    Object? syncVersion = freezed,
  }) {
    return _then(
      _value.copyWith(
            displayName: freezed == displayName
                ? _value.displayName
                : displayName // ignore: cast_nullable_to_non_nullable
                      as String?,
            firstName: freezed == firstName
                ? _value.firstName
                : firstName // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastName: freezed == lastName
                ? _value.lastName
                : lastName // ignore: cast_nullable_to_non_nullable
                      as String?,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
            phoneNumber: freezed == phoneNumber
                ? _value.phoneNumber
                : phoneNumber // ignore: cast_nullable_to_non_nullable
                      as String?,
            dateOfBirth: freezed == dateOfBirth
                ? _value.dateOfBirth
                : dateOfBirth // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
            gender: freezed == gender
                ? _value.gender
                : gender // ignore: cast_nullable_to_non_nullable
                      as String?,
            nativeLanguage: freezed == nativeLanguage
                ? _value.nativeLanguage
                : nativeLanguage // ignore: cast_nullable_to_non_nullable
                      as String?,
            addressLine1: freezed == addressLine1
                ? _value.addressLine1
                : addressLine1 // ignore: cast_nullable_to_non_nullable
                      as String?,
            addressLine2: freezed == addressLine2
                ? _value.addressLine2
                : addressLine2 // ignore: cast_nullable_to_non_nullable
                      as String?,
            city: freezed == city
                ? _value.city
                : city // ignore: cast_nullable_to_non_nullable
                      as String?,
            state: freezed == state
                ? _value.state
                : state // ignore: cast_nullable_to_non_nullable
                      as String?,
            postalCode: freezed == postalCode
                ? _value.postalCode
                : postalCode // ignore: cast_nullable_to_non_nullable
                      as String?,
            country: freezed == country
                ? _value.country
                : country // ignore: cast_nullable_to_non_nullable
                      as String?,
            occupation: freezed == occupation
                ? _value.occupation
                : occupation // ignore: cast_nullable_to_non_nullable
                      as String?,
            companyName: freezed == companyName
                ? _value.companyName
                : companyName // ignore: cast_nullable_to_non_nullable
                      as String?,
            annualIncome: freezed == annualIncome
                ? _value.annualIncome
                : annualIncome // ignore: cast_nullable_to_non_nullable
                      as double?,
            profileCompletionDontAskAgain:
                freezed == profileCompletionDontAskAgain
                ? _value.profileCompletionDontAskAgain
                : profileCompletionDontAskAgain // ignore: cast_nullable_to_non_nullable
                      as bool?,
            syncVersion: freezed == syncVersion
                ? _value.syncVersion
                : syncVersion // ignore: cast_nullable_to_non_nullable
                      as int?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileUpdateRequestImplCopyWith<$Res>
    implements $ProfileUpdateRequestCopyWith<$Res> {
  factory _$$ProfileUpdateRequestImplCopyWith(
    _$ProfileUpdateRequestImpl value,
    $Res Function(_$ProfileUpdateRequestImpl) then,
  ) = __$$ProfileUpdateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String? displayName,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? gender,
    String? nativeLanguage,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    String? occupation,
    String? companyName,
    double? annualIncome,
    bool? profileCompletionDontAskAgain,
    int? syncVersion,
  });
}

/// @nodoc
class __$$ProfileUpdateRequestImplCopyWithImpl<$Res>
    extends _$ProfileUpdateRequestCopyWithImpl<$Res, _$ProfileUpdateRequestImpl>
    implements _$$ProfileUpdateRequestImplCopyWith<$Res> {
  __$$ProfileUpdateRequestImplCopyWithImpl(
    _$ProfileUpdateRequestImpl _value,
    $Res Function(_$ProfileUpdateRequestImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? displayName = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? dateOfBirth = freezed,
    Object? gender = freezed,
    Object? nativeLanguage = freezed,
    Object? addressLine1 = freezed,
    Object? addressLine2 = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? postalCode = freezed,
    Object? country = freezed,
    Object? occupation = freezed,
    Object? companyName = freezed,
    Object? annualIncome = freezed,
    Object? profileCompletionDontAskAgain = freezed,
    Object? syncVersion = freezed,
  }) {
    return _then(
      _$ProfileUpdateRequestImpl(
        displayName: freezed == displayName
            ? _value.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String?,
        firstName: freezed == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastName: freezed == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        phoneNumber: freezed == phoneNumber
            ? _value.phoneNumber
            : phoneNumber // ignore: cast_nullable_to_non_nullable
                  as String?,
        dateOfBirth: freezed == dateOfBirth
            ? _value.dateOfBirth
            : dateOfBirth // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        gender: freezed == gender
            ? _value.gender
            : gender // ignore: cast_nullable_to_non_nullable
                  as String?,
        nativeLanguage: freezed == nativeLanguage
            ? _value.nativeLanguage
            : nativeLanguage // ignore: cast_nullable_to_non_nullable
                  as String?,
        addressLine1: freezed == addressLine1
            ? _value.addressLine1
            : addressLine1 // ignore: cast_nullable_to_non_nullable
                  as String?,
        addressLine2: freezed == addressLine2
            ? _value.addressLine2
            : addressLine2 // ignore: cast_nullable_to_non_nullable
                  as String?,
        city: freezed == city
            ? _value.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String?,
        state: freezed == state
            ? _value.state
            : state // ignore: cast_nullable_to_non_nullable
                  as String?,
        postalCode: freezed == postalCode
            ? _value.postalCode
            : postalCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        country: freezed == country
            ? _value.country
            : country // ignore: cast_nullable_to_non_nullable
                  as String?,
        occupation: freezed == occupation
            ? _value.occupation
            : occupation // ignore: cast_nullable_to_non_nullable
                  as String?,
        companyName: freezed == companyName
            ? _value.companyName
            : companyName // ignore: cast_nullable_to_non_nullable
                  as String?,
        annualIncome: freezed == annualIncome
            ? _value.annualIncome
            : annualIncome // ignore: cast_nullable_to_non_nullable
                  as double?,
        profileCompletionDontAskAgain: freezed == profileCompletionDontAskAgain
            ? _value.profileCompletionDontAskAgain
            : profileCompletionDontAskAgain // ignore: cast_nullable_to_non_nullable
                  as bool?,
        syncVersion: freezed == syncVersion
            ? _value.syncVersion
            : syncVersion // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileUpdateRequestImpl implements _ProfileUpdateRequest {
  const _$ProfileUpdateRequestImpl({
    this.displayName,
    this.firstName,
    this.lastName,
    this.email,
    this.phoneNumber,
    this.dateOfBirth,
    this.gender,
    this.nativeLanguage,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.occupation,
    this.companyName,
    this.annualIncome,
    this.profileCompletionDontAskAgain,
    this.syncVersion,
  });

  factory _$ProfileUpdateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileUpdateRequestImplFromJson(json);

  @override
  final String? displayName;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? email;
  @override
  final String? phoneNumber;
  @override
  final DateTime? dateOfBirth;
  @override
  final String? gender;
  @override
  final String? nativeLanguage;
  @override
  final String? addressLine1;
  @override
  final String? addressLine2;
  @override
  final String? city;
  @override
  final String? state;
  @override
  final String? postalCode;
  @override
  final String? country;
  @override
  final String? occupation;
  @override
  final String? companyName;
  @override
  final double? annualIncome;
  @override
  final bool? profileCompletionDontAskAgain;
  @override
  final int? syncVersion;

  @override
  String toString() {
    return 'ProfileUpdateRequest(displayName: $displayName, firstName: $firstName, lastName: $lastName, email: $email, phoneNumber: $phoneNumber, dateOfBirth: $dateOfBirth, gender: $gender, nativeLanguage: $nativeLanguage, addressLine1: $addressLine1, addressLine2: $addressLine2, city: $city, state: $state, postalCode: $postalCode, country: $country, occupation: $occupation, companyName: $companyName, annualIncome: $annualIncome, profileCompletionDontAskAgain: $profileCompletionDontAskAgain, syncVersion: $syncVersion)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileUpdateRequestImpl &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.nativeLanguage, nativeLanguage) ||
                other.nativeLanguage == nativeLanguage) &&
            (identical(other.addressLine1, addressLine1) ||
                other.addressLine1 == addressLine1) &&
            (identical(other.addressLine2, addressLine2) ||
                other.addressLine2 == addressLine2) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.postalCode, postalCode) ||
                other.postalCode == postalCode) &&
            (identical(other.country, country) || other.country == country) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.companyName, companyName) ||
                other.companyName == companyName) &&
            (identical(other.annualIncome, annualIncome) ||
                other.annualIncome == annualIncome) &&
            (identical(
                  other.profileCompletionDontAskAgain,
                  profileCompletionDontAskAgain,
                ) ||
                other.profileCompletionDontAskAgain ==
                    profileCompletionDontAskAgain) &&
            (identical(other.syncVersion, syncVersion) ||
                other.syncVersion == syncVersion));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    displayName,
    firstName,
    lastName,
    email,
    phoneNumber,
    dateOfBirth,
    gender,
    nativeLanguage,
    addressLine1,
    addressLine2,
    city,
    state,
    postalCode,
    country,
    occupation,
    companyName,
    annualIncome,
    profileCompletionDontAskAgain,
    syncVersion,
  ]);

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileUpdateRequestImplCopyWith<_$ProfileUpdateRequestImpl>
  get copyWith =>
      __$$ProfileUpdateRequestImplCopyWithImpl<_$ProfileUpdateRequestImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileUpdateRequestImplToJson(this);
  }
}

abstract class _ProfileUpdateRequest implements ProfileUpdateRequest {
  const factory _ProfileUpdateRequest({
    final String? displayName,
    final String? firstName,
    final String? lastName,
    final String? email,
    final String? phoneNumber,
    final DateTime? dateOfBirth,
    final String? gender,
    final String? nativeLanguage,
    final String? addressLine1,
    final String? addressLine2,
    final String? city,
    final String? state,
    final String? postalCode,
    final String? country,
    final String? occupation,
    final String? companyName,
    final double? annualIncome,
    final bool? profileCompletionDontAskAgain,
    final int? syncVersion,
  }) = _$ProfileUpdateRequestImpl;

  factory _ProfileUpdateRequest.fromJson(Map<String, dynamic> json) =
      _$ProfileUpdateRequestImpl.fromJson;

  @override
  String? get displayName;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get email;
  @override
  String? get phoneNumber;
  @override
  DateTime? get dateOfBirth;
  @override
  String? get gender;
  @override
  String? get nativeLanguage;
  @override
  String? get addressLine1;
  @override
  String? get addressLine2;
  @override
  String? get city;
  @override
  String? get state;
  @override
  String? get postalCode;
  @override
  String? get country;
  @override
  String? get occupation;
  @override
  String? get companyName;
  @override
  double? get annualIncome;
  @override
  bool? get profileCompletionDontAskAgain;
  @override
  int? get syncVersion;

  /// Create a copy of ProfileUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileUpdateRequestImplCopyWith<_$ProfileUpdateRequestImpl>
  get copyWith => throw _privateConstructorUsedError;
}

ProfileUpdateResult _$ProfileUpdateResultFromJson(Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'success':
      return ProfileUpdateSuccess.fromJson(json);
    case 'error':
      return ProfileUpdateError.fromJson(json);

    default:
      throw CheckedFromJsonException(
        json,
        'runtimeType',
        'ProfileUpdateResult',
        'Invalid union type "${json['runtimeType']}"!',
      );
  }
}

/// @nodoc
mixin _$ProfileUpdateResult {
  String get message => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )
    success,
    required TResult Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )
    error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )?
    success,
    TResult? Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )?
    error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )?
    success,
    TResult Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )?
    error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileUpdateSuccess value) success,
    required TResult Function(ProfileUpdateError value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileUpdateSuccess value)? success,
    TResult? Function(ProfileUpdateError value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileUpdateSuccess value)? success,
    TResult Function(ProfileUpdateError value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Serializes this ProfileUpdateResult to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProfileUpdateResultCopyWith<ProfileUpdateResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProfileUpdateResultCopyWith<$Res> {
  factory $ProfileUpdateResultCopyWith(
    ProfileUpdateResult value,
    $Res Function(ProfileUpdateResult) then,
  ) = _$ProfileUpdateResultCopyWithImpl<$Res, ProfileUpdateResult>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class _$ProfileUpdateResultCopyWithImpl<$Res, $Val extends ProfileUpdateResult>
    implements $ProfileUpdateResultCopyWith<$Res> {
  _$ProfileUpdateResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _value.copyWith(
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ProfileUpdateSuccessImplCopyWith<$Res>
    implements $ProfileUpdateResultCopyWith<$Res> {
  factory _$$ProfileUpdateSuccessImplCopyWith(
    _$ProfileUpdateSuccessImpl value,
    $Res Function(_$ProfileUpdateSuccessImpl) then,
  ) = __$$ProfileUpdateSuccessImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    UserProfile profile,
    String message,
    int newSyncVersion,
    int completionPercentage,
  });

  $UserProfileCopyWith<$Res> get profile;
}

/// @nodoc
class __$$ProfileUpdateSuccessImplCopyWithImpl<$Res>
    extends _$ProfileUpdateResultCopyWithImpl<$Res, _$ProfileUpdateSuccessImpl>
    implements _$$ProfileUpdateSuccessImplCopyWith<$Res> {
  __$$ProfileUpdateSuccessImplCopyWithImpl(
    _$ProfileUpdateSuccessImpl _value,
    $Res Function(_$ProfileUpdateSuccessImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profile = null,
    Object? message = null,
    Object? newSyncVersion = null,
    Object? completionPercentage = null,
  }) {
    return _then(
      _$ProfileUpdateSuccessImpl(
        profile: null == profile
            ? _value.profile
            : profile // ignore: cast_nullable_to_non_nullable
                  as UserProfile,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        newSyncVersion: null == newSyncVersion
            ? _value.newSyncVersion
            : newSyncVersion // ignore: cast_nullable_to_non_nullable
                  as int,
        completionPercentage: null == completionPercentage
            ? _value.completionPercentage
            : completionPercentage // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserProfileCopyWith<$Res> get profile {
    return $UserProfileCopyWith<$Res>(_value.profile, (value) {
      return _then(_value.copyWith(profile: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileUpdateSuccessImpl implements ProfileUpdateSuccess {
  const _$ProfileUpdateSuccessImpl({
    required this.profile,
    required this.message,
    required this.newSyncVersion,
    required this.completionPercentage,
    final String? $type,
  }) : $type = $type ?? 'success';

  factory _$ProfileUpdateSuccessImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileUpdateSuccessImplFromJson(json);

  @override
  final UserProfile profile;
  @override
  final String message;
  @override
  final int newSyncVersion;
  @override
  final int completionPercentage;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ProfileUpdateResult.success(profile: $profile, message: $message, newSyncVersion: $newSyncVersion, completionPercentage: $completionPercentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileUpdateSuccessImpl &&
            (identical(other.profile, profile) || other.profile == profile) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.newSyncVersion, newSyncVersion) ||
                other.newSyncVersion == newSyncVersion) &&
            (identical(other.completionPercentage, completionPercentage) ||
                other.completionPercentage == completionPercentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    profile,
    message,
    newSyncVersion,
    completionPercentage,
  );

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileUpdateSuccessImplCopyWith<_$ProfileUpdateSuccessImpl>
  get copyWith =>
      __$$ProfileUpdateSuccessImplCopyWithImpl<_$ProfileUpdateSuccessImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )
    success,
    required TResult Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )
    error,
  }) {
    return success(profile, message, newSyncVersion, completionPercentage);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )?
    success,
    TResult? Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )?
    error,
  }) {
    return success?.call(
      profile,
      message,
      newSyncVersion,
      completionPercentage,
    );
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )?
    success,
    TResult Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )?
    error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(profile, message, newSyncVersion, completionPercentage);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileUpdateSuccess value) success,
    required TResult Function(ProfileUpdateError value) error,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileUpdateSuccess value)? success,
    TResult? Function(ProfileUpdateError value)? error,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileUpdateSuccess value)? success,
    TResult Function(ProfileUpdateError value)? error,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileUpdateSuccessImplToJson(this);
  }
}

abstract class ProfileUpdateSuccess implements ProfileUpdateResult {
  const factory ProfileUpdateSuccess({
    required final UserProfile profile,
    required final String message,
    required final int newSyncVersion,
    required final int completionPercentage,
  }) = _$ProfileUpdateSuccessImpl;

  factory ProfileUpdateSuccess.fromJson(Map<String, dynamic> json) =
      _$ProfileUpdateSuccessImpl.fromJson;

  UserProfile get profile;
  @override
  String get message;
  int get newSyncVersion;
  int get completionPercentage;

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileUpdateSuccessImplCopyWith<_$ProfileUpdateSuccessImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProfileUpdateErrorImplCopyWith<$Res>
    implements $ProfileUpdateResultCopyWith<$Res> {
  factory _$$ProfileUpdateErrorImplCopyWith(
    _$ProfileUpdateErrorImpl value,
    $Res Function(_$ProfileUpdateErrorImpl) then,
  ) = __$$ProfileUpdateErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String error,
    String message,
    String? errorCode,
    int? currentVersion,
    int? providedVersion,
  });
}

/// @nodoc
class __$$ProfileUpdateErrorImplCopyWithImpl<$Res>
    extends _$ProfileUpdateResultCopyWithImpl<$Res, _$ProfileUpdateErrorImpl>
    implements _$$ProfileUpdateErrorImplCopyWith<$Res> {
  __$$ProfileUpdateErrorImplCopyWithImpl(
    _$ProfileUpdateErrorImpl _value,
    $Res Function(_$ProfileUpdateErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
    Object? message = null,
    Object? errorCode = freezed,
    Object? currentVersion = freezed,
    Object? providedVersion = freezed,
  }) {
    return _then(
      _$ProfileUpdateErrorImpl(
        error: null == error
            ? _value.error
            : error // ignore: cast_nullable_to_non_nullable
                  as String,
        message: null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        errorCode: freezed == errorCode
            ? _value.errorCode
            : errorCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        currentVersion: freezed == currentVersion
            ? _value.currentVersion
            : currentVersion // ignore: cast_nullable_to_non_nullable
                  as int?,
        providedVersion: freezed == providedVersion
            ? _value.providedVersion
            : providedVersion // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$ProfileUpdateErrorImpl implements ProfileUpdateError {
  const _$ProfileUpdateErrorImpl({
    required this.error,
    required this.message,
    this.errorCode,
    this.currentVersion,
    this.providedVersion,
    final String? $type,
  }) : $type = $type ?? 'error';

  factory _$ProfileUpdateErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProfileUpdateErrorImplFromJson(json);

  @override
  final String error;
  @override
  final String message;
  @override
  final String? errorCode;
  @override
  final int? currentVersion;
  @override
  final int? providedVersion;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'ProfileUpdateResult.error(error: $error, message: $message, errorCode: $errorCode, currentVersion: $currentVersion, providedVersion: $providedVersion)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProfileUpdateErrorImpl &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.currentVersion, currentVersion) ||
                other.currentVersion == currentVersion) &&
            (identical(other.providedVersion, providedVersion) ||
                other.providedVersion == providedVersion));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    error,
    message,
    errorCode,
    currentVersion,
    providedVersion,
  );

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProfileUpdateErrorImplCopyWith<_$ProfileUpdateErrorImpl> get copyWith =>
      __$$ProfileUpdateErrorImplCopyWithImpl<_$ProfileUpdateErrorImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )
    success,
    required TResult Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )
    error,
  }) {
    return error(
      this.error,
      message,
      errorCode,
      currentVersion,
      providedVersion,
    );
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )?
    success,
    TResult? Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )?
    error,
  }) {
    return error?.call(
      this.error,
      message,
      errorCode,
      currentVersion,
      providedVersion,
    );
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(
      UserProfile profile,
      String message,
      int newSyncVersion,
      int completionPercentage,
    )?
    success,
    TResult Function(
      String error,
      String message,
      String? errorCode,
      int? currentVersion,
      int? providedVersion,
    )?
    error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(
        this.error,
        message,
        errorCode,
        currentVersion,
        providedVersion,
      );
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ProfileUpdateSuccess value) success,
    required TResult Function(ProfileUpdateError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ProfileUpdateSuccess value)? success,
    TResult? Function(ProfileUpdateError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ProfileUpdateSuccess value)? success,
    TResult Function(ProfileUpdateError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$ProfileUpdateErrorImplToJson(this);
  }
}

abstract class ProfileUpdateError implements ProfileUpdateResult {
  const factory ProfileUpdateError({
    required final String error,
    required final String message,
    final String? errorCode,
    final int? currentVersion,
    final int? providedVersion,
  }) = _$ProfileUpdateErrorImpl;

  factory ProfileUpdateError.fromJson(Map<String, dynamic> json) =
      _$ProfileUpdateErrorImpl.fromJson;

  String get error;
  @override
  String get message;
  String? get errorCode;
  int? get currentVersion;
  int? get providedVersion;

  /// Create a copy of ProfileUpdateResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProfileUpdateErrorImplCopyWith<_$ProfileUpdateErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
