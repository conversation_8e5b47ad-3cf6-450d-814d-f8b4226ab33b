import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;

/// Enum for different types of sync operations
enum SyncOperationType {
  profileCreation,
  profileUpdate,
  dataSync,
}

/// Enum for sync error types
enum SyncErrorType {
  networkError,
  databaseError,
  validationError,
  authenticationError,
  unknownError,
}

/// Result class for Firebase to Supabase sync operations
class SyncResult {
  final bool isSuccess;
  final SyncOperationType operationType;
  final String? profileId;
  final String? firebaseUid;
  final String? email;
  final String? displayName;
  final String? authProvider;
  final DateTime? syncTimestamp;
  final SyncErrorType? errorType;
  final String? errorCode;
  final String? errorMessage;
  final String message;
  final Map<String, dynamic>? additionalData;

  SyncResult._({
    required this.isSuccess,
    required this.operationType,
    this.profileId,
    this.firebaseUid,
    this.email,
    this.displayName,
    this.authProvider,
    this.syncTimestamp,
    this.errorType,
    this.errorCode,
    this.errorMessage,
    required this.message,
    this.additionalData,
  });

  /// Factory constructor for successful sync operations
  factory SyncResult.success({
    required SyncOperationType operationType,
    String? profileId,
    String? firebaseUid,
    String? email,
    String? displayName,
    String? authProvider,
    DateTime? syncTimestamp,
    required String message,
    Map<String, dynamic>? additionalData,
  }) {
    return SyncResult._(
      isSuccess: true,
      operationType: operationType,
      profileId: profileId,
      firebaseUid: firebaseUid,
      email: email,
      displayName: displayName,
      authProvider: authProvider,
      syncTimestamp: syncTimestamp ?? DateTime.now(),
      message: message,
      additionalData: additionalData,
    );
  }

  /// Factory constructor for failed sync operations
  factory SyncResult.failure({
    required SyncOperationType operationType,
    required SyncErrorType errorType,
    String? errorCode,
    required String errorMessage,
    required String message,
    String? firebaseUid,
    Map<String, dynamic>? additionalData,
  }) {
    return SyncResult._(
      isSuccess: false,
      operationType: operationType,
      errorType: errorType,
      errorCode: errorCode,
      errorMessage: errorMessage,
      message: message,
      firebaseUid: firebaseUid,
      additionalData: additionalData,
    );
  }

  /// Factory constructor for network-related failures
  factory SyncResult.networkFailure({
    required SyncOperationType operationType,
    required String errorMessage,
    String? firebaseUid,
  }) {
    return SyncResult.failure(
      operationType: operationType,
      errorType: SyncErrorType.networkError,
      errorCode: 'network_error',
      errorMessage: errorMessage,
      message: 'Network error during sync operation',
      firebaseUid: firebaseUid,
    );
  }

  /// Factory constructor for database-related failures
  factory SyncResult.databaseFailure({
    required SyncOperationType operationType,
    required String errorMessage,
    String? errorCode,
    String? firebaseUid,
  }) {
    return SyncResult.failure(
      operationType: operationType,
      errorType: SyncErrorType.databaseError,
      errorCode: errorCode ?? 'database_error',
      errorMessage: errorMessage,
      message: 'Database error during sync operation',
      firebaseUid: firebaseUid,
    );
  }

  /// Factory constructor for validation-related failures
  factory SyncResult.validationFailure({
    required SyncOperationType operationType,
    required String errorMessage,
    String? firebaseUid,
  }) {
    return SyncResult.failure(
      operationType: operationType,
      errorType: SyncErrorType.validationError,
      errorCode: 'validation_error',
      errorMessage: errorMessage,
      message: 'Validation error during sync operation',
      firebaseUid: firebaseUid,
    );
  }

  /// Factory constructor for authentication-related failures
  factory SyncResult.authFailure({
    required SyncOperationType operationType,
    required String errorMessage,
    String? firebaseUid,
  }) {
    return SyncResult.failure(
      operationType: operationType,
      errorType: SyncErrorType.authenticationError,
      errorCode: 'auth_error',
      errorMessage: errorMessage,
      message: 'Authentication error during sync operation',
      firebaseUid: firebaseUid,
    );
  }

  /// Create a SyncResult from Firebase User for successful profile creation
  factory SyncResult.fromFirebaseUser({
    required firebase_auth.User firebaseUser,
    required SyncOperationType operationType,
    String? profileId,
    String? authProvider,
    required String message,
  }) {
    return SyncResult.success(
      operationType: operationType,
      profileId: profileId,
      firebaseUid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      authProvider: authProvider,
      message: message,
      additionalData: {
        'emailVerified': firebaseUser.emailVerified,
        'phoneNumber': firebaseUser.phoneNumber,
        'photoURL': firebaseUser.photoURL,
        'creationTime': firebaseUser.metadata.creationTime?.toIso8601String(),
        'lastSignInTime': firebaseUser.metadata.lastSignInTime?.toIso8601String(),
      },
    );
  }

  /// Convert to JSON for logging and analytics
  Map<String, dynamic> toJson() {
    return {
      'isSuccess': isSuccess,
      'operationType': operationType.name,
      'profileId': profileId,
      'firebaseUid': firebaseUid,
      'email': email,
      'displayName': displayName,
      'authProvider': authProvider,
      'syncTimestamp': syncTimestamp?.toIso8601String(),
      'errorType': errorType?.name,
      'errorCode': errorCode,
      'errorMessage': errorMessage,
      'message': message,
      'additionalData': additionalData,
    };
  }

  /// Create from JSON (for testing and debugging)
  factory SyncResult.fromJson(Map<String, dynamic> json) {
    return SyncResult._(
      isSuccess: json['isSuccess'] ?? false,
      operationType: SyncOperationType.values.firstWhere(
        (e) => e.name == json['operationType'],
        orElse: () => SyncOperationType.dataSync,
      ),
      profileId: json['profileId'],
      firebaseUid: json['firebaseUid'],
      email: json['email'],
      displayName: json['displayName'],
      authProvider: json['authProvider'],
      syncTimestamp: json['syncTimestamp'] != null
          ? DateTime.parse(json['syncTimestamp'])
          : null,
      errorType: json['errorType'] != null
          ? SyncErrorType.values.firstWhere(
              (e) => e.name == json['errorType'],
              orElse: () => SyncErrorType.unknownError,
            )
          : null,
      errorCode: json['errorCode'],
      errorMessage: json['errorMessage'],
      message: json['message'] ?? '',
      additionalData: json['additionalData'],
    );
  }

  /// Check if the sync operation was a profile creation
  bool get isProfileCreation => operationType == SyncOperationType.profileCreation;

  /// Check if the sync operation was a profile update
  bool get isProfileUpdate => operationType == SyncOperationType.profileUpdate;

  /// Check if the sync operation was a data sync
  bool get isDataSync => operationType == SyncOperationType.dataSync;

  /// Get a user-friendly error description
  String get userFriendlyError {
    if (isSuccess) return message;
    
    switch (errorType) {
      case SyncErrorType.networkError:
        return 'Network connection issue. Please check your internet connection and try again.';
      case SyncErrorType.databaseError:
        return 'Database error occurred. Please try again later.';
      case SyncErrorType.validationError:
        return 'Invalid data provided. Please check your information and try again.';
      case SyncErrorType.authenticationError:
        return 'Authentication error. Please sign in again.';
      case SyncErrorType.unknownError:
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  @override
  String toString() {
    return 'SyncResult(isSuccess: $isSuccess, operationType: $operationType, '
           'message: $message, errorType: $errorType, errorMessage: $errorMessage)';
  }
}
