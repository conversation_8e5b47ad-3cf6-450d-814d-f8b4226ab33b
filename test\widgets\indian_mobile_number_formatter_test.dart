import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import '../../lib/screens/profile/enhanced_profile_screen.dart';

void main() {
  group('IndianMobileNumberFormatter Tests', () {
    late IndianMobileNumberFormatter formatter;

    setUp(() {
      formatter = IndianMobileNumberFormatter();
    });

    test('should allow valid Indian mobile numbers starting with 6', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '6123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals('6123456789'));
    });

    test('should allow valid Indian mobile numbers starting with 7', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '7123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals('7123456789'));
    });

    test('should allow valid Indian mobile numbers starting with 8', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '8123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals('8123456789'));
    });

    test('should allow valid Indian mobile numbers starting with 9', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '9123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals('9123456789'));
    });

    test('should reject mobile numbers starting with 0', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '0123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should reject mobile numbers starting with 1', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '1123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should reject mobile numbers starting with 2', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '2123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should reject mobile numbers starting with 3', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '3123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should reject mobile numbers starting with 4', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '4123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should reject mobile numbers starting with 5', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '5123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should limit to 10 digits maximum', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '612345678901234');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals('6123456789'));
      expect(result.text.length, equals(10));
    });

    test('should remove non-digit characters', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '6123-456-789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals('6123456789'));
    });

    test('should allow empty input', () {
      const oldValue = TextEditingValue(text: '6123');
      const newValue = TextEditingValue(text: '');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should preserve valid partial input', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '612345');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals('612345'));
    });

    test('should reject invalid first digit when typing incrementally', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '0');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.text, equals(''));
    });

    test('should maintain cursor position correctly', () {
      const oldValue = TextEditingValue(text: '');
      const newValue = TextEditingValue(text: '6123456789');
      
      final result = formatter.formatEditUpdate(oldValue, newValue);
      
      expect(result.selection.baseOffset, equals(10));
      expect(result.selection.extentOffset, equals(10));
    });
  });
}
