import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/user_profile.dart';
import '../../providers/profile_provider.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../utils/app_colors.dart';
import '../../utils/toast_utils.dart';
import '../../utils/profile_debug_helper.dart';

class ProfileEditScreen extends ConsumerStatefulWidget {
  const ProfileEditScreen({super.key});

  @override
  ConsumerState<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends ConsumerState<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  
  // Text controllers for form fields
  late final TextEditingController _displayNameController;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _cityController;
  late final TextEditingController _stateController;
  late final TextEditingController _occupationController;
  late final TextEditingController _companyController;
  late final TextEditingController _incomeController;
  
  // Selected values
  DateTime? _selectedDateOfBirth;
  String? _selectedGender;
  String? _selectedLanguage;
  
  // Form state
  bool _hasChanges = false;
  
  @override
  void initState() {
    super.initState();
    _initializeControllers();
    
    // Load profile data when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }
  
  void _initializeControllers() {
    _displayNameController = TextEditingController();
    _firstNameController = TextEditingController();
    _lastNameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    _cityController = TextEditingController();
    _stateController = TextEditingController();
    _occupationController = TextEditingController();
    _companyController = TextEditingController();
    _incomeController = TextEditingController();
    
    // Add listeners to detect changes
    _displayNameController.addListener(_onFieldChanged);
    _firstNameController.addListener(_onFieldChanged);
    _lastNameController.addListener(_onFieldChanged);
    _emailController.addListener(_onFieldChanged);
    _phoneController.addListener(_onFieldChanged);
    _cityController.addListener(_onFieldChanged);
    _stateController.addListener(_onFieldChanged);
    _occupationController.addListener(_onFieldChanged);
    _companyController.addListener(_onFieldChanged);
    _incomeController.addListener(_onFieldChanged);
  }
  
  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }
  
  void _populateFields(UserProfile profile) {
    _displayNameController.text = profile.displayName ?? '';
    _firstNameController.text = profile.firstName ?? '';
    _lastNameController.text = profile.lastName ?? '';
    _emailController.text = profile.email ?? '';
    _phoneController.text = profile.phoneNumber ?? '';
    _cityController.text = profile.city ?? '';
    _stateController.text = profile.state ?? '';
    _occupationController.text = profile.occupation ?? '';
    _companyController.text = profile.companyName ?? '';
    _incomeController.text = profile.annualIncome?.toString() ?? '';
    
    _selectedDateOfBirth = profile.dateOfBirth;
    _selectedGender = profile.gender;
    _selectedLanguage = profile.nativeLanguage;
    
    _hasChanges = false;
  }
  
  @override
  void dispose() {
    _displayNameController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _occupationController.dispose();
    _companyController.dispose();
    _incomeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(profileProvider);
    
    // Listen to profile changes and populate fields
    ref.listen<ProfileState>(profileProvider, (previous, next) {
      if (next.profile != null && previous?.profile != next.profile) {
        _populateFields(next.profile!);
      }
      
      // Show success/error messages
      if (next.successMessage != null) {
        ToastUtils.showSuccess(context, next.successMessage!);
        ref.read(profileProvider.notifier).clearSuccessMessage();
        setState(() {
          _hasChanges = false;
        });
      }
      
      if (next.error != null) {
        ToastUtils.showError(context, next.error!);
        ref.read(profileProvider.notifier).clearError();
      }
    });
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Edit Profile'),
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.primary),
          onPressed: () => _handleBackPress(),
        ),
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: profileState.isUpdating ? null : _saveProfile,
              child: profileState.isUpdating
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text(
                      'Save',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: profileState.isLoading,
        child: profileState.profile == null
            ? const Center(child: Text('Loading profile...'))
            : _buildProfileForm(profileState.profile!),
      ),
    );
  }
  
  Widget _buildProfileForm(UserProfile profile) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionHeader('Basic Information'),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _displayNameController,
              label: 'Display Name',
              hint: 'Enter your display name',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Display name is required';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _firstNameController,
                    label: 'First Name',
                    hint: 'Enter your first name',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _lastNameController,
                    label: 'Last Name',
                    hint: 'Enter your last name',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _emailController,
              label: 'Email',
              hint: 'Enter your email address',
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value?.isNotEmpty ?? false) {
                  final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
                  if (!emailRegex.hasMatch(value!)) {
                    return 'Please enter a valid email address';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _phoneController,
              label: 'Phone Number',
              hint: 'Enter your phone number',
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Personal Information'),
            const SizedBox(height: 16),
            
            _buildDateOfBirthField(),
            const SizedBox(height: 16),
            
            _buildGenderField(),
            const SizedBox(height: 16),
            
            _buildLanguageField(),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Location'),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _cityController,
                    label: 'City',
                    hint: 'Enter your city',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomTextField(
                    controller: _stateController,
                    label: 'State',
                    hint: 'Enter your state',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            _buildSectionHeader('Professional Information'),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _occupationController,
              label: 'Occupation',
              hint: 'Enter your occupation',
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _companyController,
              label: 'Company Name',
              hint: 'Enter your company name',
            ),
            const SizedBox(height: 16),
            
            CustomTextField(
              controller: _incomeController,
              label: 'Annual Income',
              hint: 'Enter your annual income',
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value?.isNotEmpty ?? false) {
                  final income = double.tryParse(value!);
                  if (income == null || income < 0) {
                    return 'Please enter a valid income amount';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 32),
            
            if (_hasChanges)
              SizedBox(
                width: double.infinity,
                child: CustomButton(
                  text: 'Save Changes',
                  onPressed: _saveProfile,
                  isLoading: ref.watch(profileProvider).isUpdating,
                ),
              ),

            // DEBUG: Temporary debug button
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  print('\n🚀 DEBUG: Running profile debug tests...');
                  await ProfileDebugHelper.runAllTests();
                },
                icon: const Icon(Icons.bug_report),
                label: const Text('🔍 Debug Profile Update'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildDateOfBirthField() {
    return InkWell(
      onTap: _selectDateOfBirth,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Date of Birth',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _selectedDateOfBirth != null
                        ? '${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}'
                        : 'Select your date of birth',
                    style: TextStyle(
                      fontSize: 16,
                      color: _selectedDateOfBirth != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.calendar_today, color: AppColors.primary),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderField() {
    const genderOptions = [
      {'value': 'male', 'label': 'Male'},
      {'value': 'female', 'label': 'Female'},
      {'value': 'other', 'label': 'Other'},
      {'value': 'prefer_not_to_say', 'label': 'Prefer not to say'},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Gender',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedGender,
              hint: const Text('Select your gender'),
              isExpanded: true,
              items: genderOptions.map((option) {
                return DropdownMenuItem<String>(
                  value: option['value'],
                  child: Text(option['label']!),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedGender = value;
                  _hasChanges = true;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageField() {
    const languageOptions = [
      {'value': 'hindi', 'label': 'Hindi'},
      {'value': 'english', 'label': 'English'},
      {'value': 'bengali', 'label': 'Bengali'},
      {'value': 'telugu', 'label': 'Telugu'},
      {'value': 'marathi', 'label': 'Marathi'},
      {'value': 'tamil', 'label': 'Tamil'},
      {'value': 'gujarati', 'label': 'Gujarati'},
      {'value': 'urdu', 'label': 'Urdu'},
      {'value': 'kannada', 'label': 'Kannada'},
      {'value': 'odia', 'label': 'Odia'},
      {'value': 'punjabi', 'label': 'Punjabi'},
      {'value': 'malayalam', 'label': 'Malayalam'},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.border),
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Native Language',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedLanguage,
              hint: const Text('Select your native language'),
              isExpanded: true,
              items: languageOptions.map((option) {
                return DropdownMenuItem<String>(
                  value: option['value'],
                  child: Text(option['label']!),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLanguage = value;
                  _hasChanges = true;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDateOfBirth() async {
    final now = DateTime.now();
    final initialDate = _selectedDateOfBirth ?? DateTime(now.year - 25);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(now.year - 100),
      lastDate: now,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        _selectedDateOfBirth = selectedDate;
        _hasChanges = true;
      });
    }
  }

  Future<void> _saveProfile() async {
    print('\n🔄 ProfileEditScreen: Starting save profile...');

    if (!_formKey.currentState!.validate()) {
      print('❌ ProfileEditScreen: Form validation failed');
      return;
    }

    final profile = ref.read(profileProvider).profile;
    if (profile == null) {
      print('❌ ProfileEditScreen: No profile found in provider');
      return;
    }

    print('✅ ProfileEditScreen: Current profile sync version: ${profile.syncVersion}');

    // Create update request with only changed fields
    final request = ProfileUpdateRequest(
      displayName: _displayNameController.text.isNotEmpty ? _displayNameController.text : null,
      firstName: _firstNameController.text.isNotEmpty ? _firstNameController.text : null,
      lastName: _lastNameController.text.isNotEmpty ? _lastNameController.text : null,
      email: _emailController.text.isNotEmpty ? _emailController.text : null,
      phoneNumber: _phoneController.text.isNotEmpty ? _phoneController.text : null,
      dateOfBirth: _selectedDateOfBirth,
      gender: _selectedGender,
      nativeLanguage: _selectedLanguage,
      city: _cityController.text.isNotEmpty ? _cityController.text : null,
      state: _stateController.text.isNotEmpty ? _stateController.text : null,
      occupation: _occupationController.text.isNotEmpty ? _occupationController.text : null,
      companyName: _companyController.text.isNotEmpty ? _companyController.text : null,
      annualIncome: _incomeController.text.isNotEmpty ? double.tryParse(_incomeController.text) : null,
      syncVersion: profile.syncVersion,
    );

    print('📋 ProfileEditScreen: Update request created:');
    print('   Display Name: ${request.displayName}');
    print('   First Name: ${request.firstName}');
    print('   Last Name: ${request.lastName}');
    print('   Email: ${request.email}');
    print('   Phone: ${request.phoneNumber}');
    print('   Sync Version: ${request.syncVersion}');

    print('🔄 ProfileEditScreen: Calling provider updateProfile...');
    final success = await ref.read(profileProvider.notifier).updateProfile(request);

    print('📥 ProfileEditScreen: Update result: $success');

    if (success) {
      print('✅ ProfileEditScreen: Update successful, scrolling to top');
      // Scroll to top to show success message
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      print('❌ ProfileEditScreen: Update failed');
    }
  }

  Future<bool> _handleBackPress() async {
    if (!_hasChanges) {
      Navigator.of(context).pop();
      return true;
    }

    final shouldDiscard = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Discard Changes?'),
        content: const Text('You have unsaved changes. Are you sure you want to go back?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Discard'),
          ),
        ],
      ),
    );

    if (shouldDiscard == true) {
      Navigator.of(context).pop();
      return true;
    }

    return false;
  }
}
