import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../lib/services/profile_update_service.dart';
import '../../lib/models/user_profile.dart';

// Mock classes
class MockSupabaseClient extends Mock implements SupabaseClient {}
class MockGoTrueClient extends Mock implements GoTrueClient {}
class MockUser extends Mock implements User {}
class MockPostgrestClient extends Mock implements PostgrestClient {}
class MockPostgrestFilterBuilder extends Mock implements PostgrestFilterBuilder {}

void main() {
  group('ProfileUpdateService', () {
    late ProfileUpdateService service;
    late MockSupabaseClient mockSupabase;
    late MockGoTrueClient mockAuth;
    late MockUser mockUser;
    late MockPostgrestClient mockFrom;
    late MockPostgrestFilterBuilder mockSelect;

    setUp(() {
      mockSupabase = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      mockUser = MockUser();
      mockFrom = MockPostgrestClient();
      mockSelect = MockPostgrestFilterBuilder();
      
      // Setup mock relationships
      when(mockSupabase.auth).thenReturn(mockAuth);
      when(mockAuth.currentUser).thenReturn(mockUser);
      when(mockUser.id).thenReturn('test-user-id');
      
      service = ProfileUpdateService();
      // Note: In a real implementation, you'd inject the mock client
    });

    group('Validation', () {
      test('should validate email format correctly', () {
        expect(service.isValidEmail('<EMAIL>'), isTrue);
        expect(service.isValidEmail('invalid-email'), isFalse);
        expect(service.isValidEmail(''), isFalse);
        expect(service.isValidEmail('test@'), isFalse);
        expect(service.isValidEmail('@example.com'), isFalse);
      });

      test('should validate phone number format correctly', () {
        expect(service.isValidPhoneNumber('+************'), isTrue);
        expect(service.isValidPhoneNumber('9876543210'), isTrue);
        expect(service.isValidPhoneNumber('123'), isFalse);
        expect(service.isValidPhoneNumber(''), isFalse);
        expect(service.isValidPhoneNumber('abcd'), isFalse);
      });

      test('should validate date of birth correctly', () {
        final validDate = DateTime(1990, 1, 1);
        final futureDate = DateTime.now().add(const Duration(days: 1));
        final tooOldDate = DateTime(1900, 1, 1);

        expect(service.isValidDateOfBirth(validDate), isTrue);
        expect(service.isValidDateOfBirth(futureDate), isFalse);
        expect(service.isValidDateOfBirth(tooOldDate), isFalse);
      });

      test('should validate gender correctly', () {
        expect(service.isValidGender('male'), isTrue);
        expect(service.isValidGender('female'), isTrue);
        expect(service.isValidGender('other'), isTrue);
        expect(service.isValidGender('prefer_not_to_say'), isTrue);
        expect(service.isValidGender('invalid'), isFalse);
        expect(service.isValidGender(''), isFalse);
      });

      test('should validate annual income correctly', () {
        expect(service.isValidAnnualIncome(50000), isTrue);
        expect(service.isValidAnnualIncome(0), isTrue);
        expect(service.isValidAnnualIncome(-1000), isFalse);
        expect(service.isValidAnnualIncome(double.infinity), isFalse);
        expect(service.isValidAnnualIncome(double.nan), isFalse);
      });
    });

    group('ProfileUpdateRequest', () {
      test('should create valid update request', () {
        final request = ProfileUpdateRequest(
          displayName: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '+************',
          syncVersion: 1,
        );

        expect(request.displayName, equals('John Doe'));
        expect(request.email, equals('<EMAIL>'));
        expect(request.phoneNumber, equals('+************'));
        expect(request.syncVersion, equals(1));
      });

      test('should convert to Supabase parameters correctly', () {
        final request = ProfileUpdateRequest(
          displayName: 'John Doe',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phoneNumber: '+************',
          dateOfBirth: DateTime(1990, 1, 1),
          gender: 'male',
          nativeLanguage: 'english',
          city: 'Mumbai',
          state: 'Maharashtra',
          occupation: 'Engineer',
          companyName: 'Tech Corp',
          annualIncome: 1000000.0,
          syncVersion: 1,
        );

        final params = request.toSupabaseParams('test-firebase-uid');

        expect(params['display_name'], equals('John Doe'));
        expect(params['first_name'], equals('John'));
        expect(params['last_name'], equals('Doe'));
        expect(params['email'], equals('<EMAIL>'));
        expect(params['phone_number'], equals('+************'));
        expect(params['date_of_birth'], equals('1990-01-01'));
        expect(params['gender'], equals('male'));
        expect(params['native_language'], equals('english'));
        expect(params['city'], equals('Mumbai'));
        expect(params['state'], equals('Maharashtra'));
        expect(params['occupation'], equals('Engineer'));
        expect(params['company_name'], equals('Tech Corp'));
        expect(params['annual_income'], equals(1000000.0));
        expect(params['sync_version'], equals(1));
      });
    });

    group('UserProfile', () {
      test('should create UserProfile from Supabase response', () {
        final supabaseData = {
          'id': 'profile-id',
          'firebase_uid': 'firebase-uid',
          'email': '<EMAIL>',
          'display_name': 'Test User',
          'first_name': 'Test',
          'last_name': 'User',
          'phone_number': '+************',
          'photo_url': 'https://example.com/photo.jpg',
          'is_email_verified': true,
          'auth_provider': 'google',
          'role': 'user',
          'date_of_birth': '1990-01-01',
          'gender': 'male',
          'native_language': 'english',
          'city': 'Mumbai',
          'state': 'Maharashtra',
          'country': 'India',
          'occupation': 'Engineer',
          'company_name': 'Tech Corp',
          'annual_income': 1000000.0,
          'subscription_plan': 'Free',
          'sync_version': 1,
          'profile_completion_percentage': 85,
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
          'last_sign_in_at': '2024-01-01T00:00:00Z',
        };

        final profile = UserProfile.fromSupabase(supabaseData);

        expect(profile.id, equals('profile-id'));
        expect(profile.firebaseUid, equals('firebase-uid'));
        expect(profile.email, equals('<EMAIL>'));
        expect(profile.displayName, equals('Test User'));
        expect(profile.firstName, equals('Test'));
        expect(profile.lastName, equals('User'));
        expect(profile.phoneNumber, equals('+************'));
        expect(profile.photoUrl, equals('https://example.com/photo.jpg'));
        expect(profile.isEmailVerified, isTrue);
        expect(profile.authProvider, equals('google'));
        expect(profile.role, equals('user'));
        expect(profile.dateOfBirth, equals(DateTime(1990, 1, 1)));
        expect(profile.gender, equals('male'));
        expect(profile.nativeLanguage, equals('english'));
        expect(profile.city, equals('Mumbai'));
        expect(profile.state, equals('Maharashtra'));
        expect(profile.country, equals('India'));
        expect(profile.occupation, equals('Engineer'));
        expect(profile.companyName, equals('Tech Corp'));
        expect(profile.annualIncome, equals(1000000.0));
        expect(profile.subscriptionPlan, equals('Free'));
        expect(profile.syncVersion, equals(1));
        expect(profile.profileCompletionPercentage, equals(85));
      });

      test('should handle null values in Supabase response', () {
        final supabaseData = {
          'id': 'profile-id',
          'firebase_uid': 'firebase-uid',
          'email': null,
          'display_name': null,
          'sync_version': 0,
          'profile_completion_percentage': 0,
          'country': 'India',
          'subscription_plan': 'Free',
          'auth_provider': 'email',
        };

        final profile = UserProfile.fromSupabase(supabaseData);

        expect(profile.id, equals('profile-id'));
        expect(profile.firebaseUid, equals('firebase-uid'));
        expect(profile.email, isNull);
        expect(profile.displayName, isNull);
        expect(profile.syncVersion, equals(0));
        expect(profile.profileCompletionPercentage, equals(0));
        expect(profile.country, equals('India'));
        expect(profile.subscriptionPlan, equals('Free'));
        expect(profile.authProvider, equals('email'));
      });
    });
  });
}
