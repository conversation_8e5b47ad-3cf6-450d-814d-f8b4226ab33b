# All About Insurance (AAI)

A comprehensive Flutter application for insurance comparison and management with automatic Firebase Auth to Supabase profile synchronization.

## Features

### Core Functionality
- **Insurance Comparison**: Compare policies across multiple insurance providers
- **User Authentication**: Multi-provider authentication (Google, Apple, Email/Password, Phone/OTP)
- **Profile Management**: Comprehensive user profile management with automatic sync
- **Policy Management**: Track and manage insurance policies
- **PDF Reports**: Generate and share comparison reports

### Firebase Auth to Supabase Sync
- **Automatic Profile Creation**: New Firebase Auth users are automatically synced to Supabase profiles
- **Multi-Provider Support**: Supports Google, Apple, Email/Password, and Phone/OTP authentication
- **Graceful Degradation**: Handles service outages with offline queue processing
- **Error Recovery**: Comprehensive retry mechanisms with exponential backoff
- **Health Monitoring**: Real-time sync health monitoring and alerting
- **Manual Controls**: Manual sync retry and queue processing capabilities

## Architecture

### Authentication Flow
1. User signs in via Firebase Auth (Google/Apple/Email/Phone)
2. Profile automatically synced to Supabase `profiles` table
3. If sync fails, user added to offline queue for retry
4. Background processing handles failed syncs with exponential backoff

### Database Schema (Supabase)
```sql
-- Profiles table with sync tracking
CREATE TABLE profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  firebase_uid TEXT UNIQUE NOT NULL,
  email TEXT,
  display_name TEXT,
  phone_number TEXT,
  photo_url TEXT,
  is_email_verified BOOLEAN DEFAULT false,
  auth_provider TEXT NOT NULL,
  date_of_birth DATE,
  company_name TEXT,
  last_sign_in_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Sync tracking fields
  sync_status sync_status_enum DEFAULT 'pending',
  last_sync_at TIMESTAMPTZ,
  sync_error_message TEXT,
  sync_retry_count INTEGER DEFAULT 0,
  sync_version INTEGER DEFAULT 1
);

-- Sync status enumeration
CREATE TYPE sync_status_enum AS ENUM ('pending', 'synced', 'failed', 'retrying');
```

### Key Services

#### FirebaseSupabaseSyncService
- Core sync functionality between Firebase Auth and Supabase
- Handles profile creation and updates
- Implements graceful degradation and retry logic
- Provides sync statistics and health monitoring

#### SyncQueueService
- Manages offline sync queue for failed operations
- Handles background processing with connectivity monitoring
- Implements retry limits and failure tracking
- Provides queue status and management

#### SyncMonitoringService
- Real-time health monitoring and alerting
- Tracks sync performance metrics
- Generates health reports and statistics
- Manages alert conditions and notifications

#### ManualSyncService
- Manual sync operations and retry functionality
- Batch queue processing
- User-initiated sync operations
- Admin functions for sync management

#### ErrorLoggingService
- Comprehensive error logging and reporting
- Local storage with Crashlytics integration
- Error statistics and analytics
- Export functionality for debugging

## Getting Started

### Prerequisites
- Flutter SDK (>=3.0.0)
- Firebase project with Authentication enabled
- Supabase project with database setup
- Android/iOS development environment

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aai
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**
   - Create a Firebase project
   - Enable Authentication with desired providers
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place configuration files in appropriate directories

4. **Supabase Setup**
   - Create a Supabase project
   - Run the database migration scripts from `supabase/migrations/`
   - Update Supabase configuration in `lib/core/config/supabase_config.dart`

5. **Environment Configuration**
   - Copy `.env.example` to `.env`
   - Update environment variables with your project credentials

### Configuration

#### Firebase Configuration
```dart
// lib/core/config/firebase_config.dart
class FirebaseConfig {
  static const String projectId = 'your-project-id';
  // Add other Firebase configuration
}
```

#### Supabase Configuration
```dart
// lib/core/config/supabase_config.dart
class SupabaseConfig {
  static const String url = 'your-supabase-url';
  static const String anonKey = 'your-anon-key';
}
```

### Running the App

```bash
# Development
flutter run

# Release
flutter run --release
```

## Sync Functionality

### Automatic Sync Process

1. **User Authentication**: User signs in via any supported provider
2. **Profile Check**: System checks if profile exists in Supabase
3. **Profile Creation/Update**: Creates new profile or updates existing one
4. **Error Handling**: Failed syncs are queued for retry with exponential backoff
5. **Health Monitoring**: Continuous monitoring of sync health and performance

### Sync Data Mapping

| Firebase Field | Supabase Column | Notes |
|----------------|-----------------|-------|
| `uid` | `firebase_uid` | Unique identifier |
| `email` | `email` | May be null for phone login |
| `emailVerified` | `is_email_verified` | Boolean |
| `displayName` | `display_name` | May be null |
| `phoneNumber` | `phone_number` | May be null |
| `photoURL` | `photo_url` | Profile image URL |
| `providerId` | `auth_provider` | Mapped to app-specific values |
| `lastSignInTime` | `last_sign_in_at` | Timestamp |

### Error Handling

The sync system implements comprehensive error handling:

- **Network Errors**: Automatic retry with exponential backoff
- **Database Errors**: Specific error handling for Supabase issues
- **Authentication Errors**: Proper handling of auth state changes
- **Validation Errors**: Input validation and sanitization
- **Service Degradation**: Graceful fallback to offline queue

### Monitoring and Alerting

#### Health Status Levels
- **Healthy**: All systems operating normally
- **Degraded**: Some issues detected, reduced performance
- **Critical**: Major issues requiring immediate attention

#### Alert Types
- High queue size (>50 items)
- High error rate (>20%)
- Many failed syncs (>10 items)
- Service degraded/critical status

### Manual Operations

#### Sync Health Dashboard
Access via: `SyncHealthDashboard` widget

Features:
- Real-time health status
- Sync statistics and metrics
- Active alerts and notifications
- Quick action buttons for manual operations

#### Manual Sync Operations
- Sync current user
- Process entire queue
- Retry specific failed syncs
- Export error logs for debugging

## Testing

### Running Tests

```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Specific test suites
flutter test test/services/firebase_supabase_sync_service_test.dart
flutter test test/integration/firebase_supabase_sync_integration_test.dart
flutter test test/error_scenarios/sync_error_scenarios_test.dart
```

### Test Coverage

The project includes comprehensive test coverage:

- **Unit Tests**: Service layer testing with mocked dependencies
- **Integration Tests**: End-to-end sync functionality testing
- **Error Scenario Tests**: Edge cases and error condition testing
- **Performance Tests**: Concurrent operations and load testing

## Deployment

### Build for Production

```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

### Environment-Specific Builds

```bash
# Staging
flutter build apk --release --dart-define=ENVIRONMENT=staging

# Production
flutter build apk --release --dart-define=ENVIRONMENT=production
```

## Monitoring and Analytics

### Firebase Analytics Events

The app tracks comprehensive analytics events:

- `user_synced_to_supabase`: Successful sync operations
- `profile_sync_failed`: Failed sync attempts
- `sync_retry_attempted`: Retry operations
- `sync_health_check`: Health monitoring events
- `manual_sync_attempted`: User-initiated sync operations

### Error Tracking

- **Firebase Crashlytics**: Automatic crash reporting
- **Local Error Logs**: Detailed error logging with export capability
- **Sync Error Tracking**: Specific sync operation error tracking

## Troubleshooting

### Common Issues

#### Sync Failures
1. Check network connectivity
2. Verify Supabase configuration
3. Check Firebase Auth status
4. Review error logs in dashboard

#### Authentication Issues
1. Verify Firebase project configuration
2. Check provider-specific setup (Google/Apple)
3. Ensure proper app signing for release builds

#### Database Issues
1. Verify Supabase RLS policies
2. Check database schema migrations
3. Ensure proper API key permissions

### Debug Mode

Enable debug logging:
```dart
// lib/main.dart
void main() {
  // Enable debug logging
  debugPrint('Debug mode enabled');
  runApp(MyApp());
}
```

### Health Check

Use the Sync Health Dashboard to:
- Monitor real-time sync status
- View error statistics
- Process failed syncs manually
- Export logs for analysis

## Contributing

### Development Guidelines

1. Follow Flutter/Dart style guidelines
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Use conventional commit messages

### Code Structure

```
lib/
├── core/           # Core configuration and utilities
├── models/         # Data models and DTOs
├── services/       # Business logic and external integrations
├── providers/      # State management (Riverpod)
├── widgets/        # Reusable UI components
├── screens/        # Application screens
└── utils/          # Helper utilities
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the sync health dashboard for system status
