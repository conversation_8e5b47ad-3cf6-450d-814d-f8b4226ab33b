import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../lib/models/user_profile.dart';
import '../../lib/providers/profile_provider.dart';

void main() {
  group('ProfileProvider Caching Tests', () {
    setUp(() {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('should cache profile data when loaded', () async {
      // Create a test profile
      final testProfile = UserProfile(
        id: 'test-profile-id',
        firebaseUid: 'test-firebase-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
        syncVersion: 5,
      );

      // Test caching functionality
      final prefs = await SharedPreferences.getInstance();
      
      // Simulate caching the profile
      final profileJson = testProfile.toJson();
      await prefs.setString('cached_user_profile', profileJson.toString());
      await prefs.setInt('cached_user_profile_timestamp', DateTime.now().millisecondsSinceEpoch);
      await prefs.setString('cached_user_profile_user_id', 'test-firebase-uid');

      // Verify cache was set
      expect(prefs.getString('cached_user_profile'), isNotNull);
      expect(prefs.getInt('cached_user_profile_timestamp'), isNotNull);
      expect(prefs.getString('cached_user_profile_user_id'), equals('test-firebase-uid'));
    });

    test('should clear cache when user logs out', () async {
      final prefs = await SharedPreferences.getInstance();
      
      // Set some cached data
      await prefs.setString('cached_user_profile', '{"test": "data"}');
      await prefs.setInt('cached_user_profile_timestamp', DateTime.now().millisecondsSinceEpoch);
      await prefs.setString('cached_user_profile_user_id', 'test-user');

      // Verify data is set
      expect(prefs.getString('cached_user_profile'), isNotNull);

      // Clear cache (simulate logout)
      await prefs.remove('cached_user_profile');
      await prefs.remove('cached_user_profile_timestamp');
      await prefs.remove('cached_user_profile_user_id');

      // Verify cache is cleared
      expect(prefs.getString('cached_user_profile'), isNull);
      expect(prefs.getInt('cached_user_profile_timestamp'), isNull);
      expect(prefs.getString('cached_user_profile_user_id'), isNull);
    });

    test('should handle cache expiration correctly', () async {
      final prefs = await SharedPreferences.getInstance();
      
      // Set expired cache (6 minutes ago, cache valid for 5 minutes)
      final expiredTime = DateTime.now().subtract(const Duration(minutes: 6));
      await prefs.setString('cached_user_profile', '{"test": "data"}');
      await prefs.setInt('cached_user_profile_timestamp', expiredTime.millisecondsSinceEpoch);
      await prefs.setString('cached_user_profile_user_id', 'test-user');

      // Check if cache is expired
      final timestampMs = prefs.getInt('cached_user_profile_timestamp');
      expect(timestampMs, isNotNull);
      
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestampMs!);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);
      
      // Cache should be expired (older than 5 minutes)
      expect(cacheAge.inMinutes, greaterThan(5));
    });

    test('should handle different user cache correctly', () async {
      final prefs = await SharedPreferences.getInstance();
      
      // Set cache for user A
      await prefs.setString('cached_user_profile', '{"user": "A"}');
      await prefs.setInt('cached_user_profile_timestamp', DateTime.now().millisecondsSinceEpoch);
      await prefs.setString('cached_user_profile_user_id', 'user-a');

      // Check cache for user B (should not match)
      final cachedUserId = prefs.getString('cached_user_profile_user_id');
      expect(cachedUserId, equals('user-a'));
      expect(cachedUserId, isNot(equals('user-b')));
    });
  });
}
