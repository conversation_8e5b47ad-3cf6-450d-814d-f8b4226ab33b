import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart';
import '../../providers/profile_provider.dart';
import '../../widgets/profile_sidebar.dart';
import '../products/product_details_screen.dart';

class AllCompaniesScreen extends ConsumerStatefulWidget {
  const AllCompaniesScreen({super.key});

  @override
  ConsumerState<AllCompaniesScreen> createState() => _AllCompaniesScreenState();
}

class _AllCompaniesScreenState extends ConsumerState<AllCompaniesScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  String? _expandedCompanyId;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // All insurance companies data
  final List<Map<String, dynamic>> _allCompanies = [
    {
      'id': 'securehealth',
      'name': 'SecureHealth',
      'description': 'Best in health insurance.',
      'icon': Icons.security,
      'popularity': 9,
      'products': [
        {'name': 'Health Insurance', 'description': 'Comprehensive health coverage'},
        {'name': 'Family Plans', 'description': 'Complete family protection'},
        {'name': 'Senior Citizen', 'description': 'Elderly care plans'},
        {'name': 'Critical Illness', 'description': 'Serious disease coverage'},
        {'name': 'Maternity Care', 'description': 'Pregnancy & childbirth'},
        {'name': 'Dental Care', 'description': 'Oral health coverage'},
      ],
    },
    {
      'id': 'lifeguard',
      'name': 'LifeGuard',
      'description': 'Leading life insurance provider.',
      'icon': Icons.shield,
      'popularity': 8,
      'products': [
        {'name': 'Term Life', 'description': 'Pure life protection'},
        {'name': 'Whole Life', 'description': 'Lifetime coverage'},
        {'name': 'ULIP Plans', 'description': 'Investment + insurance'},
      ],
    },
    {
      'id': 'travelsafe',
      'name': 'TravelSafe',
      'description': 'Trusted travel insurance partner.',
      'icon': Icons.flight_takeoff,
      'popularity': 7,
      'products': [
        {'name': 'Domestic Travel', 'description': 'India travel coverage'},
        {'name': 'International', 'description': 'Worldwide protection'},
        {'name': 'Student Travel', 'description': 'Study abroad plans'},
        {'name': 'Business Travel', 'description': 'Corporate coverage'},
        {'name': 'Adventure Sports', 'description': 'High-risk activities'},
      ],
    },
    {
      'id': 'autoprotect',
      'name': 'AutoProtect',
      'description': 'Complete motor insurance solutions.',
      'icon': Icons.directions_car,
      'popularity': 8,
      'products': [
        {'name': 'Car Insurance', 'description': 'Comprehensive car coverage'},
        {'name': 'Bike Insurance', 'description': 'Two-wheeler protection'},
        {'name': 'Commercial Vehicle', 'description': 'Fleet insurance'},
      ],
    },
    {
      'id': 'homeguard',
      'name': 'HomeGuard',
      'description': 'Comprehensive home protection.',
      'icon': Icons.home,
      'popularity': 6,
      'products': [
        {'name': 'Home Insurance', 'description': 'Property protection'},
        {'name': 'Contents Cover', 'description': 'Belongings safety'},
        {'name': 'Tenant Insurance', 'description': 'Rental protection'},
      ],
    },
    {
      'id': 'familycare',
      'name': 'FamilyCare',
      'description': 'Family-focused insurance plans.',
      'icon': Icons.family_restroom,
      'popularity': 7,
      'products': [
        {'name': 'Family Health', 'description': 'Complete family coverage'},
        {'name': 'Child Plans', 'description': 'Kids future security'},
        {'name': 'Women Health', 'description': 'Specialized women care'},
      ],
    },
    {
      'id': 'businessshield',
      'name': 'BusinessShield',
      'description': 'Commercial insurance expertise.',
      'icon': Icons.business,
      'popularity': 5,
      'products': [
        {'name': 'Business Insurance', 'description': 'Commercial protection'},
        {'name': 'Professional Indemnity', 'description': 'Professional liability'},
        {'name': 'Cyber Security', 'description': 'Digital protection'},
      ],
    },
    {
      'id': 'wealthprotect',
      'name': 'WealthProtect',
      'description': 'Investment and wealth protection.',
      'icon': Icons.account_balance,
      'popularity': 6,
      'products': [
        {'name': 'Investment Plans', 'description': 'Wealth building'},
        {'name': 'Pension Plans', 'description': 'Retirement security'},
        {'name': 'Tax Saver', 'description': 'Tax efficient plans'},
      ],
    },
    {
      'id': 'reliancegeneral',
      'name': 'Reliance General',
      'description': 'Trusted insurance solutions.',
      'icon': Icons.verified_user,
      'popularity': 9,
      'products': [
        {'name': 'Health Insurance', 'description': 'Medical coverage'},
        {'name': 'Motor Insurance', 'description': 'Vehicle protection'},
        {'name': 'Travel Insurance', 'description': 'Journey safety'},
        {'name': 'Home Insurance', 'description': 'Property security'},
      ],
    },
    {
      'id': 'bajajallianz',
      'name': 'Bajaj Allianz',
      'description': 'Comprehensive insurance coverage.',
      'icon': Icons.security,
      'popularity': 8,
      'products': [
        {'name': 'Health Plans', 'description': 'Medical insurance'},
        {'name': 'Motor Plans', 'description': 'Vehicle insurance'},
        {'name': 'Life Insurance', 'description': 'Life protection'},
      ],
    },
  ];

  List<Map<String, dynamic>> get _filteredCompanies {
    return _allCompanies.where((company) {
      return company['name'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
             company['description'].toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final filteredCompanies = _filteredCompanies;

    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xFFf1f1f1),
      drawer: Drawer(
        child: Consumer(
          builder: (context, ref, child) {
            final currentUser = ref.watch(currentUserProvider);
            final profileState = ref.watch(profileProvider);
            final profile = profileState.profile;

            // Trigger profile load if not loaded and not currently loading
            if (profile == null && !profileState.isLoading && currentUser != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                ref.read(profileProvider.notifier).loadProfile();
              });
            }

            // Show loading state if profile is not loaded yet
            if (profile == null && currentUser != null) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFe92933)),
                  ),
                ),
              );
            }

            // Use profile data if available, fallback to Firebase Auth data only if no user
            final userName = profile?.displayName ?? currentUser?.displayName ?? 'Name';
            final userEmail = profile?.email ?? currentUser?.email ?? '<EMAIL>';
            final userPhone = profile?.phoneNumber ?? currentUser?.phoneNumber ?? '+91 - - - - - - - - - -';
            final profileImageUrl = profile?.photoUrl ?? currentUser?.photoURL;

            return ProfileSidebar(
              userName: userName,
              userEmail: userEmail,
              userPhone: userPhone,
              profileImageUrl: profileImageUrl,
              onLogout: () {
                Navigator.pop(context); // Close drawer
                ref.read(authProvider.notifier).signOut();
                NavigationService.instance.navigateToAlternateSignIn();
              },
              onEditProfile: () {
                Navigator.pop(context); // Close drawer
                NavigationService.instance.navigateToProfile();
              },
            );
          },
        ),
      ),
      body: Column(
        children: [
          // Custom header with shadow
          Container(
            color: const Color(0xFFf1f1f1),
            child: SafeArea(
              bottom: false,
              child: Container(
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  color: Color(0xFFf1f1f1),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0F000000),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                      onPressed: () => Navigator.pop(context),
                    ),
                    const Expanded(
                      child: Text(
                        'All Insurance Companies',
                        style: TextStyle(
                          color: Color(0xFF111418),
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // Home icon
                    IconButton(
                      onPressed: () {
                        NavigationService.instance.navigateToAlternateHome();
                      },
                      icon: const Icon(
                        Icons.home,
                        color: Color(0xFFe92933),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Body content with search and companies list
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Search Section (non-sticky)
                  Container(
                    color: const Color(0xFFf1f1f1),
                    padding: const EdgeInsets.all(16),
                    child: TextField(
                      controller: _searchController,
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                      decoration: InputDecoration(
                        labelText: 'Search Companies',
                        labelStyle: const TextStyle(color: Color(0xFF637488)),
                        floatingLabelStyle: const TextStyle(color: Color(0xFFe92933)),
                        prefixIcon: const Icon(Icons.search, color: Color(0xFF637488)),
                        suffixIcon: _searchQuery.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear, color: Color(0xFF637488)),
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchQuery = '';
                                  });
                                },
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Color(0xFFe0e0e0)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Color(0xFFe92933)),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                    ),
                  ),
                  // Companies List
                  filteredCompanies.isEmpty
                      ? _buildNoResultsFound()
                      : Column(
                          children: filteredCompanies.map((company) {
                            return _buildExpandableCompanyCard(
                              company['name'],
                              company['description'],
                              company['icon'],
                              company['id'],
                              List<Map<String, String>>.from(company['products']),
                            );
                          }).toList(),
                        ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: const Color(0xFF637488).withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No companies found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF637488).withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'Try searching with different keywords'
                : 'No companies match your current filters',
            style: TextStyle(
              fontSize: 14,
              color: const Color(0xFF637488).withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Clear Search'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildExpandableCompanyCard(
    String name,
    String description,
    IconData icon,
    String companyId,
    List<Map<String, String>> products,
  ) {
    final isExpanded = _expandedCompanyId == companyId;

    return Container(
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Main company card
          InkWell(
            onTap: () {
              // Unfocus search field when company card is tapped
              FocusScope.of(context).unfocus();
              setState(() {
                if (isExpanded) {
                  _expandedCompanyId = null;
                } else {
                  _expandedCompanyId = companyId;
                }
              });
            },
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 8), // Match home page internal padding
              child: Row(
                children: [
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE0E7FF),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: const Color(0xFF197FE5),
                      size: 32,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF111418),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          description,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF637488),
                          ),
                        ),
                      ],
                    ),
                  ),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: isExpanded ? const Color(0xFFe92933) : const Color(0xFF637488),
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Expandable products section
          if (isExpanded) ...[
            SizedBox(
              height: 70, // Same height as home screen
              width: double.infinity,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(left: 16, right: 16), // Match company card left padding
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return Container(
                    width: 90, // Same width as home screen
                    margin: EdgeInsets.only(
                      left: index == 0 ? 0 : 8, // No left margin for first card, 8px for others
                    ),
                    child: _buildProductCard(
                      product['name']!,
                      product['description']!,
                      name, // company name
                      companyId, // company id
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 12), // Gap between sub-cards and bottom of company card
          ],
        ],
      ),
    );
  }

  Widget _buildProductCard(String name, String description, String companyName, String companyId) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFf8f9fa),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ProductDetailsScreen(
                productName: name,
                productDescription: description,
                companyName: companyName,
                companyId: companyId,
              ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(6),
        child: Padding(
          padding: const EdgeInsets.all(6), // Very compact padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 10, // Very small font for compact layout
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF111418),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2), // Minimal spacing
              Expanded(
                child: Text(
                  description,
                  style: const TextStyle(
                    fontSize: 8, // Very small font for compact layout
                    color: Color(0xFF637488),
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

}
