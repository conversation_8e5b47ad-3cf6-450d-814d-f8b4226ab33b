## Relevant Files

- `lib/services/profile_completion_service.dart` - Contains the problematic profile validation logic that needs to be updated to check Supabase data instead of Firebase Auth data.
- `lib/screens/products/product_details_screen.dart` - PDF generation workflow that calls profile completion logic.
- `lib/screens/compare/compare_screen.dart` - PDF generation workflow that calls profile completion logic.
- `lib/widgets/profile_completion_dialog.dart` - Dialog component that shows missing fields.
- `lib/providers/enhanced_auth_provider.dart` - Contains currentUserProvider that currently returns Firebase user instead of Supabase profile.
- `lib/services/supabase_profile_service.dart` - Service for querying Supabase profile data.
- `lib/models/user_model.dart` - AppUser model with Supabase profile fields.

### Notes

- The root issue is that `currentUserProvider` returns Firebase Auth user data instead of Supabase profile data
- Profile completion logic checks Firebase Auth fields instead of actual Supabase profile table fields
- Need to create a new provider that returns Supabase profile data for profile completion checks
- Unit tests should verify that profile completion only shows for actually missing Supabase fields

## Tasks

- [ ] 1.0 Fix Current User Provider to Return Supabase Profile Data
  - [ ] 1.1 Create new `currentSupabaseUserProvider` that returns AppUser from Supabase profiles table
  - [ ] 1.2 Update product details screen to use Supabase profile data for PDF generation
  - [ ] 1.3 Update compare screen to use Supabase profile data for PDF generation
  - [ ] 1.4 Ensure proper error handling when Supabase profile is not found

- [ ] 2.0 Fix Profile Completion Service Logic
  - [ ] 2.1 Update `getMissingProfileFields()` to check actual Supabase profile fields instead of Firebase Auth fields
  - [ ] 2.2 Remove authentication-method-based assumptions from field validation
  - [ ] 2.3 Implement direct Supabase profile table field checking for: profile_picture, mobile_number, name, email
  - [ ] 2.4 Update `shouldShowProfileCompletion()` to use Supabase-based field validation

- [ ] 3.0 Update Profile Completion Dialog Integration
  - [ ] 3.1 Modify product details screen to pass Supabase profile data to profile completion dialog
  - [ ] 3.2 Modify compare screen to pass Supabase profile data to profile completion dialog
  - [ ] 3.3 Update profile completion dialog to work with Supabase profile data
  - [ ] 3.4 Ensure profile updates are saved to Supabase profiles table

- [ ] 4.0 Test and Validate Fix
  - [ ] 4.1 Test Google Auth users with complete Supabase profiles (should not show completion dialog)
  - [ ] 4.2 Test Mobile Auth users with complete Supabase profiles (should not show completion dialog)
  - [ ] 4.3 Test users with partially complete Supabase profiles (should only show missing fields)
  - [ ] 4.4 Test profile completion flow saves data correctly to Supabase
  - [ ] 4.5 Test PDF generation works with both complete and incomplete profiles
