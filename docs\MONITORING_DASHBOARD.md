# Sync Health Monitoring Dashboard

This document provides comprehensive information about the Sync Health Monitoring Dashboard, including its features, usage, and configuration.

## Overview

The Sync Health Monitoring Dashboard provides real-time visibility into the Firebase Auth to Supabase sync system's performance, health, and error status. It serves as the primary interface for monitoring sync operations, diagnosing issues, and performing manual interventions.

## Dashboard Components

### 1. Overall Health Status

**Location**: Top of dashboard
**Purpose**: Provides at-a-glance system health status

#### Health Status Levels

- **🟢 Healthy**: All systems operating normally
  - Success rate > 80%
  - Queue size < 50 items
  - Error rate < 20%
  - No critical alerts

- **🟡 Degraded**: Some issues detected, reduced performance
  - Success rate 50-80%
  - Queue size 50-100 items
  - Error rate 20-50%
  - Warning alerts present

- **🔴 Critical**: Major issues requiring immediate attention
  - Success rate < 50%
  - Queue size > 100 items
  - Error rate > 50%
  - Critical alerts present

#### Displayed Metrics

- **Success Rate**: Percentage of successful sync operations in last 24 hours
- **Error Rate**: Percentage of failed sync operations in last 24 hours
- **Last Check**: Timestamp of most recent health check

### 2. Sync Status Card

**Purpose**: Current sync queue and processing status

#### Key Metrics

- **Queue Size**: Number of pending sync operations
  - Green: < 25 items
  - Orange: 25-50 items
  - Red: > 50 items

- **Failed Syncs**: Number of permanently failed sync operations
  - Green: < 5 items
  - Orange: 5-10 items
  - Red: > 10 items

- **Need Retry**: Number of profiles requiring retry
  - Green: < 10 items
  - Orange: 10-20 items
  - Red: > 20 items

#### Processing Indicator

- Shows spinning indicator when queue is actively processing
- Displays "Queue is currently processing..." message

### 3. Error Statistics Card

**Purpose**: Historical error analysis and trends

#### Time-Based Metrics

- **Last 24 Hours**: Recent error count for immediate attention
- **Last Week**: Weekly error trends for pattern analysis
- **Total**: Cumulative error count since system deployment

#### Error Categories

- **Sync Errors**: Firebase-Supabase sync operation failures
- **Application Errors**: General application-level errors
- **Network Errors**: Connectivity and request failures
- **Database Errors**: Supabase operation failures

### 4. Active Alerts Card

**Purpose**: Real-time alert monitoring and management

#### Alert Display

- Shows up to 3 most recent alerts
- "View all X alerts" button for complete alert list
- "No Active Alerts" message when system is healthy

#### Alert Types

- **🔵 Info**: Informational messages
  - Queue processing started
  - Service recovery notifications
  - Routine maintenance alerts

- **🟡 Warning**: Issues requiring attention
  - High queue size (>50 items)
  - Elevated error rate (>20%)
  - Service degradation detected

- **🔴 Critical**: Urgent issues requiring immediate action
  - Very high queue size (>100 items)
  - High error rate (>50%)
  - Service failure detected
  - Multiple consecutive failures

#### Alert Information

Each alert displays:
- Alert message and description
- Severity level (Info/Warning/Critical)
- Timestamp (relative time: "2h ago", "Just now")
- Alert type and category

### 5. Quick Actions Card

**Purpose**: Manual intervention and administrative functions

#### Available Actions

- **🔄 Sync Current User**: Manually sync the currently authenticated user
- **▶️ Process Queue**: Manually trigger queue processing
- **🐛 View Error Logs**: Navigate to detailed error log viewer
- **📥 Export Logs**: Download error logs for analysis

#### Action Results

- Success notifications with green background
- Error notifications with red background
- Progress indicators during operation execution

## Usage Guide

### Accessing the Dashboard

```dart
// Navigate to dashboard from settings or admin screen
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => SyncHealthDashboard(),
  ),
);
```

### Refresh Functionality

- **Pull-to-Refresh**: Swipe down to refresh all dashboard data
- **Manual Refresh**: Tap refresh icon in app bar
- **Auto-Refresh**: Dashboard automatically refreshes when actions are performed

### Interpreting Health Status

#### Healthy System Indicators
- Green overall health status
- Low queue size (< 25 items)
- High success rate (> 90%)
- No active alerts or only info-level alerts

#### Warning Signs
- Orange/yellow health status
- Growing queue size (25-50 items)
- Declining success rate (70-90%)
- Warning-level alerts present

#### Critical Issues
- Red health status
- Large queue size (> 50 items)
- Low success rate (< 70%)
- Critical alerts present
- Multiple failed syncs

### Manual Interventions

#### When to Sync Current User
- User reports profile data inconsistencies
- Recent authentication but profile not updated
- Testing sync functionality
- After resolving sync service issues

#### When to Process Queue
- Large queue size with no automatic processing
- After resolving network/service issues
- Manual intervention required for stuck queue
- Testing queue processing functionality

#### When to View Error Logs
- Investigating specific sync failures
- Analyzing error patterns and trends
- Debugging user-reported issues
- System troubleshooting and analysis

#### When to Export Logs
- Detailed analysis required
- Sharing logs with development team
- Creating support tickets
- Historical analysis and reporting

## Configuration

### Alert Thresholds

Default alert thresholds can be configured in `SyncMonitoringService`:

```dart
// Queue size thresholds
static const int queueSizeWarningThreshold = 50;
static const int queueSizeCriticalThreshold = 100;

// Error rate thresholds (as percentage)
static const double errorRateWarningThreshold = 20.0;
static const double errorRateCriticalThreshold = 50.0;

// Failed sync thresholds
static const int failedSyncsWarningThreshold = 10;
static const int failedSyncsCriticalThreshold = 25;
```

### Refresh Intervals

```dart
// Dashboard auto-refresh interval
static const Duration dashboardRefreshInterval = Duration(minutes: 5);

// Health check interval
static const Duration healthCheckInterval = Duration(minutes: 2);

// Queue processing check interval
static const Duration queueCheckInterval = Duration(minutes: 1);
```

### Display Preferences

```dart
// Maximum alerts to show in dashboard
static const int maxAlertsInDashboard = 3;

// Maximum error log entries to display
static const int maxErrorLogEntries = 50;

// Time format for timestamps
static const String timestampFormat = 'relative'; // or 'absolute'
```

## Troubleshooting

### Dashboard Not Loading

1. **Check Network Connectivity**
   - Verify internet connection
   - Test Supabase connectivity
   - Check firewall/proxy settings

2. **Verify Service Configuration**
   - Confirm Supabase URL and API key
   - Check service initialization
   - Review error logs for configuration issues

3. **Clear Local Data**
   - Clear app cache and data
   - Reset SharedPreferences
   - Restart application

### Inaccurate Health Status

1. **Refresh Dashboard Data**
   - Use pull-to-refresh gesture
   - Tap refresh button in app bar
   - Wait for automatic refresh cycle

2. **Check Service Synchronization**
   - Verify all services are initialized
   - Check for service communication issues
   - Review health check logic

3. **Validate Metrics Calculation**
   - Check error rate calculation logic
   - Verify queue size reporting
   - Review alert threshold configuration

### Actions Not Working

1. **Verify User Authentication**
   - Ensure user is properly authenticated
   - Check Firebase Auth status
   - Verify Supabase session

2. **Check Service Availability**
   - Test Supabase connectivity
   - Verify service health status
   - Check for service degradation

3. **Review Permissions**
   - Confirm user has necessary permissions
   - Check RLS policies in Supabase
   - Verify API key permissions

## Best Practices

### Monitoring

1. **Regular Health Checks**
   - Monitor dashboard daily during business hours
   - Set up automated alerts for critical issues
   - Review weekly error trends and patterns

2. **Proactive Maintenance**
   - Process queue when size exceeds 25 items
   - Investigate warning-level alerts promptly
   - Clear old error logs regularly

3. **Performance Optimization**
   - Monitor success rates and response times
   - Identify and address recurring error patterns
   - Optimize queue processing frequency

### Alert Management

1. **Alert Prioritization**
   - Address critical alerts immediately
   - Schedule warning alert resolution
   - Monitor info alerts for trends

2. **Alert Resolution**
   - Document resolution steps for common alerts
   - Track alert resolution times
   - Review alert threshold effectiveness

3. **Communication**
   - Notify stakeholders of critical issues
   - Provide regular status updates
   - Document system maintenance windows

### Data Management

1. **Log Retention**
   - Export logs before automatic cleanup
   - Archive important error data
   - Maintain historical performance data

2. **Capacity Planning**
   - Monitor queue growth trends
   - Plan for peak usage periods
   - Scale resources based on metrics

3. **Security**
   - Protect sensitive error information
   - Limit dashboard access to authorized users
   - Audit dashboard usage and actions

## Integration with External Systems

### Analytics Integration

The dashboard integrates with Firebase Analytics to track:
- Dashboard usage patterns
- Manual action frequency
- Error resolution effectiveness
- User engagement with monitoring features

### Alerting Systems

Future integrations may include:
- Email notifications for critical alerts
- Slack/Teams integration for team notifications
- SMS alerts for urgent issues
- Integration with incident management systems

### Reporting

The dashboard supports:
- Automated daily/weekly health reports
- Error trend analysis and reporting
- Performance metrics export
- Custom dashboard views for different user roles

## API Reference

### Dashboard Data Models

```dart
// Health report structure
class SyncHealthReport {
  final SyncHealthStatus overallHealth;
  final double syncSuccessRate;
  final double errorRate;
  final List<SyncHealthAlert> alerts;
  final DateTime timestamp;
}

// Status summary structure
class SyncStatusSummary {
  final int queueSize;
  final int failedSyncsCount;
  final int profilesNeedingRetryCount;
  final bool isQueueProcessing;
}

// Error statistics structure
class ErrorStatistics {
  final int totalSyncErrors;
  final int totalApplicationErrors;
  final int syncErrors24h;
  final int applicationErrors24h;
  final Map<String, int> errorsByType;
}
```

### Service Methods

```dart
// Get current health status
Future<SyncHealthReport> getCurrentHealth();

// Get sync status summary
Future<SyncStatusSummary> getSyncStatusSummary();

// Get error statistics
Future<ErrorStatistics> getErrorStatistics();

// Perform manual actions
Future<SyncResult> syncCurrentUser();
Future<ManualSyncBatchResult> processQueueManually();
Future<String> exportErrorLogs();
```
