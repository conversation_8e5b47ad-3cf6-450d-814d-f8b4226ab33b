-- =============================================================================
-- CLEANUP SCRIPT: Remove all existing profile-related objects
-- =============================================================================
-- Run this FIRST to ensure a completely clean slate before running complete_profiles_setup.sql
-- This script safely removes all existing profile-related database objects
-- =============================================================================

-- Backup existing profiles table (if you want to preserve data)
-- Uncomment the next line if you want to backup existing data
-- CREATE TABLE profiles_backup AS SELECT * FROM public.profiles;

-- =============================================================================
-- 1. DROP ALL EXISTING FUNCTIONS (ALL VARIATIONS)
-- =============================================================================

-- Drop all possible variations of create_user_profile
DROP FUNCTION IF EXISTS public.create_user_profile() CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT) CASCADE;

-- Drop all possible variations of sync_firebase_user_data
DROP FUNCTION IF EXISTS public.sync_firebase_user_data() CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT, TEXT) CASCADE;

-- Drop all possible variations of handle_user_signup
DROP FUNCTION IF EXISTS public.handle_user_signup() CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT) CASCADE;

-- Drop utility functions
DROP FUNCTION IF EXISTS public.clean_phone_number(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS public.calculate_profile_completion(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.update_last_active(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.test_profiles_setup() CASCADE;

-- =============================================================================
-- 2. DROP ALL TRIGGERS
-- =============================================================================

DROP TRIGGER IF EXISTS trigger_profiles_updated_at ON public.profiles CASCADE;

-- =============================================================================
-- 3. DROP ALL INDEXES (if they exist independently)
-- =============================================================================

DROP INDEX IF EXISTS public.idx_profiles_firebase_uid CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_email CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_phone CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_role CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_subscription CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_sync_status CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_active CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_created_at CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_last_sign_in CASCADE;
DROP INDEX IF EXISTS public.idx_profiles_last_active CASCADE;

-- =============================================================================
-- 4. DROP ALL RLS POLICIES
-- =============================================================================

DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles CASCADE;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles CASCADE;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles CASCADE;
DROP POLICY IF EXISTS "Admins can delete profiles" ON public.profiles CASCADE;

-- =============================================================================
-- 5. DROP TABLES
-- =============================================================================

DROP TABLE IF EXISTS public.profiles CASCADE;

-- =============================================================================
-- 6. DROP CUSTOM TYPES
-- =============================================================================

DROP TYPE IF EXISTS public.sync_status CASCADE;

-- =============================================================================
-- 7. VERIFICATION
-- =============================================================================

-- Check that everything is cleaned up
DO $$
DECLARE
    function_count INTEGER;
    table_count INTEGER;
    type_count INTEGER;
BEGIN
    -- Count remaining profile-related functions
    SELECT COUNT(*) INTO function_count
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public' 
    AND p.proname LIKE '%profile%';
    
    -- Count remaining profile tables
    SELECT COUNT(*) INTO table_count
    FROM pg_tables
    WHERE schemaname = 'public' 
    AND tablename = 'profiles';
    
    -- Count remaining custom types
    SELECT COUNT(*) INTO type_count
    FROM pg_type t
    JOIN pg_namespace n ON t.typnamespace = n.oid
    WHERE n.nspname = 'public' 
    AND t.typname = 'sync_status';
    
    -- Report cleanup status
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'CLEANUP COMPLETED';
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'Profile-related functions remaining: %', function_count;
    RAISE NOTICE 'Profile tables remaining: %', table_count;
    RAISE NOTICE 'Custom types remaining: %', type_count;
    RAISE NOTICE '';
    
    IF function_count = 0 AND table_count = 0 AND type_count = 0 THEN
        RAISE NOTICE '✅ CLEANUP SUCCESSFUL - Database is clean and ready for fresh setup';
        RAISE NOTICE '';
        RAISE NOTICE 'NEXT STEP: Run complete_profiles_setup.sql to create the new system';
    ELSE
        RAISE NOTICE '⚠️  Some objects may still exist. Check manually if needed.';
        RAISE NOTICE '';
        RAISE NOTICE 'You can still proceed with complete_profiles_setup.sql';
    END IF;
    
    RAISE NOTICE '=============================================================================';
END $$;

-- =============================================================================
-- 8. ADDITIONAL CLEANUP (if needed)
-- =============================================================================

-- If you have any custom profile-related objects, add them here
-- Example:
-- DROP FUNCTION IF EXISTS your_custom_function(args) CASCADE;
-- DROP TABLE IF EXISTS your_custom_table CASCADE;

-- =============================================================================
-- INSTRUCTIONS
-- =============================================================================

/*
USAGE INSTRUCTIONS:

1. BACKUP FIRST (if you have important data):
   CREATE TABLE profiles_backup AS SELECT * FROM public.profiles;

2. RUN THIS CLEANUP SCRIPT:
   - Copy and paste this entire file into Supabase SQL Editor
   - Click "Run"
   - Wait for completion
   - Check the output for "CLEANUP SUCCESSFUL" message

3. THEN RUN THE MAIN SETUP:
   - Run complete_profiles_setup.sql
   - This will create the fresh, clean profiles system

4. VERIFY EVERYTHING WORKS:
   - Run: SELECT * FROM public.test_profiles_setup();
   - Should show all "PASS ✅" results

If you encounter any errors during cleanup, they are usually safe to ignore
as they typically mean the object didn't exist in the first place.
*/
