-- Test script to verify profile_completion_dont_ask_again parameter works
-- Run this AFTER executing the quick_fix_profile_completion_preference.sql

-- Test 1: Check if the function accepts the new parameter
SELECT 'Testing function with profile_completion_dont_ask_again parameter...' as test_status;

-- Test 2: Try to update a user's preference (replace 'your-firebase-uid' with actual UID)
-- This is just a test - replace with your actual Firebase UID
SELECT public.update_user_profile(
    p_firebase_uid := 'tunHYHs4WBY0hEzX9aGHW0LKBYw2',  -- Replace with your actual UID
    p_profile_completion_dont_ask_again := true
) as test_result;

-- Test 3: Verify the update worked by checking the profiles table
SELECT 
    firebase_uid,
    display_name,
    profile_completion_dont_ask_again,
    sync_version,
    updated_at
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2'  -- Replace with your actual UID
LIMIT 1;

-- Test 4: Check function parameters to ensure the new parameter is included
SELECT 
    parameter_name,
    data_type,
    parameter_mode,
    ordinal_position
FROM information_schema.parameters 
WHERE routine_schema = 'public' 
AND routine_name = 'update_user_profile'
AND parameter_name = 'p_profile_completion_dont_ask_again';

SELECT 'Test completed. Check results above.' as final_status;
