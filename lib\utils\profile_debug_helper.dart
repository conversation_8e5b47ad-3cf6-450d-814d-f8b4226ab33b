import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Quick debug helper to test profile update functionality
class ProfileDebugHelper {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Quick test to verify database functions exist
  static Future<void> testDatabaseFunctions() async {
    print('\n' + '=' * 60);
    print('🔍 TESTING DATABASE FUNCTIONS');
    print('=' * 60);

    final user = _auth.currentUser;
    if (user == null) {
      print('❌ No authenticated user found');
      return;
    }

    print('✅ User authenticated: ${user.uid}');

    // Test 1: Check if get_user_profile function exists
    try {
      print('\n📋 Testing get_user_profile function...');
      final response = await _supabase.rpc('get_user_profile', params: {
        'p_firebase_uid': user.uid
      });
      print('✅ get_user_profile function exists');
      print('📥 Response: $response');
    } catch (e) {
      print('❌ get_user_profile function error: $e');
      if (e.toString().contains('function') && e.toString().contains('does not exist')) {
        print('💡 SOLUTION: Run complete_profiles_setup.sql in Supabase Dashboard');
      }
    }

    // Test 2: Check if update_user_profile function exists
    try {
      print('\n📋 Testing update_user_profile function...');
      final response = await _supabase.rpc('update_user_profile', params: {
        'p_firebase_uid': user.uid,
        'p_sync_version': 0, // Use current version
      });
      print('✅ update_user_profile function exists');
      print('📥 Response: $response');
    } catch (e) {
      print('❌ update_user_profile function error: $e');
      if (e.toString().contains('function') && e.toString().contains('does not exist')) {
        print('💡 SOLUTION: Run complete_profiles_setup.sql in Supabase Dashboard');
      }
    }

    // Test 3: Check profiles table access
    try {
      print('\n📋 Testing profiles table access...');
      final response = await _supabase
          .from('profiles')
          .select('id, firebase_uid, email, display_name, sync_version')
          .eq('firebase_uid', user.uid)
          .maybeSingle();
      print('✅ profiles table accessible');
      print('📥 Current profile: $response');
    } catch (e) {
      print('❌ profiles table access error: $e');
      if (e.toString().contains('permission denied')) {
        print('💡 SOLUTION: Check RLS policies in Supabase Dashboard');
      }
    }

    print('\n' + '=' * 60);
    print('🎯 NEXT STEPS:');
    print('1. If functions don\'t exist: Run complete_profiles_setup.sql');
    print('2. If permission denied: Check RLS policies');
    print('3. If profile is null: User needs to be synced first');
    print('=' * 60 + '\n');
  }

  /// Test a simple profile update
  static Future<void> testProfileUpdate() async {
    print('\n' + '=' * 60);
    print('🔄 TESTING PROFILE UPDATE');
    print('=' * 60);

    final user = _auth.currentUser;
    if (user == null) {
      print('❌ No authenticated user found');
      return;
    }

    try {
      // Get current profile first
      print('📋 Getting current profile...');
      final currentProfile = await _supabase
          .from('profiles')
          .select('sync_version, display_name')
          .eq('firebase_uid', user.uid)
          .maybeSingle();

      if (currentProfile == null) {
        print('❌ No profile found for user');
        return;
      }

      print('✅ Current profile: $currentProfile');

      // Test update with minimal data
      final testName = 'Debug Test ${DateTime.now().millisecondsSinceEpoch}';
      print('🔄 Testing update with display_name: $testName');

      final updateParams = {
        'p_firebase_uid': user.uid,
        'p_display_name': testName,
        'p_sync_version': currentProfile['sync_version'] ?? 0,
      };

      print('📋 Update parameters: $updateParams');

      final response = await _supabase.rpc('update_user_profile', params: updateParams);
      print('📥 Update response: $response');

      if (response != null && response['success'] == true) {
        print('✅ Profile update successful!');
        print('📈 New sync version: ${response['sync_version']}');
      } else {
        print('❌ Profile update failed');
        print('📥 Error response: $response');
      }

    } catch (e) {
      print('❌ Profile update test error: $e');
      if (e is PostgrestException) {
        print('   Code: ${e.code}');
        print('   Message: ${e.message}');
        print('   Details: ${e.details}');
        print('   Hint: ${e.hint}');
      }
    }

    print('=' * 60 + '\n');
  }

  /// Run all debug tests
  static Future<void> runAllTests() async {
    await testDatabaseFunctions();
    await testProfileUpdate();
  }
}

/// Extension to easily add debug button to any widget
extension DebugButtonExtension on Widget {
  Widget withDebugButton() {
    return Column(
      children: [
        this,
        const SizedBox(height: 16),
        ElevatedButton.icon(
          onPressed: () async {
            await ProfileDebugHelper.runAllTests();
          },
          icon: const Icon(Icons.bug_report),
          label: const Text('Run Profile Debug Tests'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
