import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';


import 'firebase_supabase_sync_service.dart';
import 'sync_queue_service.dart';
import 'analytics_service.dart';

/// Service for monitoring sync operations and providing alerts
class SyncMonitoringService {
  static final SyncMonitoringService _instance = SyncMonitoringService._internal();
  factory SyncMonitoringService() => _instance;
  SyncMonitoringService._internal();

  static const String _metricsKey = 'sync_metrics';
  static const String _alertsKey = 'sync_alerts';
  static const Duration _monitoringInterval = Duration(minutes: 10);
  static const int _maxMetricsHistory = 100;

  final FirebaseSupabaseSyncService _syncService = FirebaseSupabaseSyncService.instance;
  final SyncQueueService _queueService = SyncQueueService();
  final AnalyticsService _analytics = AnalyticsService.instance;

  Timer? _monitoringTimer;
  final List<SyncHealthAlert> _activeAlerts = [];

  /// Initialize monitoring service
  Future<void> initialize() async {
    await _loadActiveAlerts();
    _startMonitoring();
  }

  /// Start continuous monitoring
  void _startMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(_monitoringInterval, (_) {
      _performHealthCheck();
    });
  }

  /// Perform comprehensive health check
  Future<SyncHealthReport> _performHealthCheck() async {
    try {
      final timestamp = DateTime.now();
      
      // Get sync statistics
      final syncStats = await _syncService.getSyncStatistics();
      
      // Get queue status
      final queueStatus = await _queueService.getQueueStatus();
      
      // Get profiles needing retry
      final profilesNeedingRetry = await _syncService.getProfilesNeedingRetry();
      
      // Calculate health metrics
      final healthMetrics = _calculateHealthMetrics(
        syncStats: syncStats,
        queueStatus: queueStatus,
        profilesNeedingRetry: profilesNeedingRetry,
      );

      final report = SyncHealthReport(
        timestamp: timestamp,
        overallHealth: healthMetrics.overallHealth,
        syncSuccessRate: healthMetrics.syncSuccessRate,
        queueSize: queueStatus['queueSize'] ?? 0,
        failedSyncs: queueStatus['failedSyncs'] ?? 0,
        profilesNeedingRetry: profilesNeedingRetry.length,
        averageResponseTime: healthMetrics.averageResponseTime,
        errorRate: healthMetrics.errorRate,
        alerts: List.from(_activeAlerts),
      );

      // Store metrics
      await _storeHealthMetrics(report);
      
      // Check for alert conditions
      await _checkAlertConditions(report);
      
      // Track monitoring metrics
      await _analytics.trackSyncHealthCheck(
        overallHealth: report.overallHealth.toString(),
        queueSize: report.queueSize,
        failedSyncs: report.failedSyncs,
        errorRate: report.errorRate,
      );

      return report;
    } catch (e) {
      print('Error performing health check: $e');
      return SyncHealthReport.error(e.toString());
    }
  }

  /// Get current sync health status
  Future<SyncHealthReport> getCurrentHealth() async {
    return await _performHealthCheck();
  }

  /// Get health history
  Future<List<SyncHealthReport>> getHealthHistory({int limit = 24}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_metricsKey) ?? '[]';
      final metricsList = List<Map<String, dynamic>>.from(json.decode(metricsJson));

      return metricsList
          .take(limit)
          .map((data) => SyncHealthReport.fromJson(data))
          .toList();
    } catch (e) {
      print('Error getting health history: $e');
      return [];
    }
  }

  /// Get active alerts
  List<SyncHealthAlert> getActiveAlerts() {
    return List.from(_activeAlerts);
  }

  /// Dismiss an alert
  Future<void> dismissAlert(String alertId) async {
    _activeAlerts.removeWhere((alert) => alert.id == alertId);
    await _saveActiveAlerts();
  }

  /// Calculate health metrics
  _HealthMetrics _calculateHealthMetrics({
    required Map<String, dynamic> syncStats,
    required Map<String, dynamic> queueStatus,
    required List<Map<String, dynamic>> profilesNeedingRetry,
  }) {
    // Calculate overall health based on various factors
    SyncHealthStatus overallHealth = SyncHealthStatus.healthy;
    double syncSuccessRate = 1.0;
    double averageResponseTime = 0.0;
    double errorRate = 0.0;

    // Analyze sync statistics
    if (syncStats.containsKey('error')) {
      overallHealth = SyncHealthStatus.critical;
      errorRate = 1.0;
    } else {
      final totalProfiles = syncStats['totalProfiles'] ?? 0;
      final recentSyncs = syncStats['recentSyncs'] ?? 0;
      
      if (totalProfiles > 0) {
        syncSuccessRate = recentSyncs / totalProfiles;
      }
    }

    // Analyze queue status
    final queueSize = queueStatus['queueSize'] ?? 0;
    final failedSyncs = queueStatus['failedSyncs'] ?? 0;
    
    if (queueSize > 50) {
      overallHealth = SyncHealthStatus.degraded;
    }
    
    if (failedSyncs > 10) {
      overallHealth = SyncHealthStatus.critical;
    }

    // Analyze retry requirements
    if (profilesNeedingRetry.length > 20) {
      overallHealth = SyncHealthStatus.degraded;
    }

    // Calculate error rate
    if (queueSize > 0) {
      errorRate = failedSyncs / (queueSize + failedSyncs);
    }

    return _HealthMetrics(
      overallHealth: overallHealth,
      syncSuccessRate: syncSuccessRate,
      averageResponseTime: averageResponseTime,
      errorRate: errorRate,
    );
  }

  /// Check for alert conditions
  Future<void> _checkAlertConditions(SyncHealthReport report) async {
    final newAlerts = <SyncHealthAlert>[];

    // High queue size alert
    if (report.queueSize > 50) {
      newAlerts.add(SyncHealthAlert(
        id: 'high_queue_size',
        type: SyncAlertType.highQueueSize,
        severity: report.queueSize > 100 ? SyncAlertSeverity.critical : SyncAlertSeverity.warning,
        message: 'Sync queue size is high: ${report.queueSize} items',
        timestamp: DateTime.now(),
        data: {'queueSize': report.queueSize},
      ));
    }

    // High failure rate alert
    if (report.errorRate > 0.2) {
      newAlerts.add(SyncHealthAlert(
        id: 'high_error_rate',
        type: SyncAlertType.highErrorRate,
        severity: report.errorRate > 0.5 ? SyncAlertSeverity.critical : SyncAlertSeverity.warning,
        message: 'High sync error rate: ${(report.errorRate * 100).toStringAsFixed(1)}%',
        timestamp: DateTime.now(),
        data: {'errorRate': report.errorRate},
      ));
    }

    // Many failed syncs alert
    if (report.failedSyncs > 10) {
      newAlerts.add(SyncHealthAlert(
        id: 'many_failed_syncs',
        type: SyncAlertType.manyFailedSyncs,
        severity: report.failedSyncs > 25 ? SyncAlertSeverity.critical : SyncAlertSeverity.warning,
        message: 'Many failed syncs: ${report.failedSyncs} items',
        timestamp: DateTime.now(),
        data: {'failedSyncs': report.failedSyncs},
      ));
    }

    // Service degraded alert
    if (report.overallHealth == SyncHealthStatus.degraded) {
      newAlerts.add(SyncHealthAlert(
        id: 'service_degraded',
        type: SyncAlertType.serviceDegraded,
        severity: SyncAlertSeverity.warning,
        message: 'Sync service performance is degraded',
        timestamp: DateTime.now(),
        data: {'healthStatus': report.overallHealth.toString()},
      ));
    }

    // Service critical alert
    if (report.overallHealth == SyncHealthStatus.critical) {
      newAlerts.add(SyncHealthAlert(
        id: 'service_critical',
        type: SyncAlertType.serviceCritical,
        severity: SyncAlertSeverity.critical,
        message: 'Sync service is in critical state',
        timestamp: DateTime.now(),
        data: {'healthStatus': report.overallHealth.toString()},
      ));
    }

    // Add new alerts and remove duplicates
    for (final newAlert in newAlerts) {
      _activeAlerts.removeWhere((alert) => alert.id == newAlert.id);
      _activeAlerts.add(newAlert);
    }

    // Remove resolved alerts
    _activeAlerts.removeWhere((alert) => !_isAlertStillValid(alert, report));

    await _saveActiveAlerts();
  }

  /// Check if an alert is still valid
  bool _isAlertStillValid(SyncHealthAlert alert, SyncHealthReport report) {
    switch (alert.type) {
      case SyncAlertType.highQueueSize:
        return report.queueSize > 50;
      case SyncAlertType.highErrorRate:
        return report.errorRate > 0.2;
      case SyncAlertType.manyFailedSyncs:
        return report.failedSyncs > 10;
      case SyncAlertType.serviceDegraded:
        return report.overallHealth == SyncHealthStatus.degraded;
      case SyncAlertType.serviceCritical:
        return report.overallHealth == SyncHealthStatus.critical;
    }
  }

  /// Store health metrics
  Future<void> _storeHealthMetrics(SyncHealthReport report) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_metricsKey) ?? '[]';
      final metricsList = List<Map<String, dynamic>>.from(json.decode(metricsJson));

      metricsList.insert(0, report.toJson());

      // Limit history size
      if (metricsList.length > _maxMetricsHistory) {
        metricsList.removeRange(_maxMetricsHistory, metricsList.length);
      }

      await prefs.setString(_metricsKey, json.encode(metricsList));
    } catch (e) {
      print('Error storing health metrics: $e');
    }
  }

  /// Load active alerts
  Future<void> _loadActiveAlerts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final alertsJson = prefs.getString(_alertsKey) ?? '[]';
      final alertsList = List<Map<String, dynamic>>.from(json.decode(alertsJson));

      _activeAlerts.clear();
      _activeAlerts.addAll(
        alertsList.map((data) => SyncHealthAlert.fromJson(data)),
      );
    } catch (e) {
      print('Error loading active alerts: $e');
    }
  }

  /// Save active alerts
  Future<void> _saveActiveAlerts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final alertsData = _activeAlerts.map((alert) => alert.toJson()).toList();
      await prefs.setString(_alertsKey, json.encode(alertsData));
    } catch (e) {
      print('Error saving active alerts: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _monitoringTimer?.cancel();
  }
}

/// Health metrics calculation helper
class _HealthMetrics {
  final SyncHealthStatus overallHealth;
  final double syncSuccessRate;
  final double averageResponseTime;
  final double errorRate;

  _HealthMetrics({
    required this.overallHealth,
    required this.syncSuccessRate,
    required this.averageResponseTime,
    required this.errorRate,
  });
}

/// Sync health status enumeration
enum SyncHealthStatus { healthy, degraded, critical }

/// Sync alert type enumeration
enum SyncAlertType {
  highQueueSize,
  highErrorRate,
  manyFailedSyncs,
  serviceDegraded,
  serviceCritical,
}

/// Sync alert severity enumeration
enum SyncAlertSeverity { info, warning, critical }

/// Sync health report model
class SyncHealthReport {
  final DateTime timestamp;
  final SyncHealthStatus overallHealth;
  final double syncSuccessRate;
  final int queueSize;
  final int failedSyncs;
  final int profilesNeedingRetry;
  final double averageResponseTime;
  final double errorRate;
  final List<SyncHealthAlert> alerts;
  final String? error;

  SyncHealthReport({
    required this.timestamp,
    required this.overallHealth,
    required this.syncSuccessRate,
    required this.queueSize,
    required this.failedSyncs,
    required this.profilesNeedingRetry,
    required this.averageResponseTime,
    required this.errorRate,
    required this.alerts,
    this.error,
  });

  factory SyncHealthReport.error(String error) {
    return SyncHealthReport(
      timestamp: DateTime.now(),
      overallHealth: SyncHealthStatus.critical,
      syncSuccessRate: 0.0,
      queueSize: 0,
      failedSyncs: 0,
      profilesNeedingRetry: 0,
      averageResponseTime: 0.0,
      errorRate: 1.0,
      alerts: [],
      error: error,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'overallHealth': overallHealth.toString(),
      'syncSuccessRate': syncSuccessRate,
      'queueSize': queueSize,
      'failedSyncs': failedSyncs,
      'profilesNeedingRetry': profilesNeedingRetry,
      'averageResponseTime': averageResponseTime,
      'errorRate': errorRate,
      'alerts': alerts.map((a) => a.toJson()).toList(),
      'error': error,
    };
  }

  factory SyncHealthReport.fromJson(Map<String, dynamic> json) {
    return SyncHealthReport(
      timestamp: DateTime.parse(json['timestamp']),
      overallHealth: SyncHealthStatus.values.firstWhere(
        (e) => e.toString() == json['overallHealth'],
        orElse: () => SyncHealthStatus.critical,
      ),
      syncSuccessRate: json['syncSuccessRate']?.toDouble() ?? 0.0,
      queueSize: json['queueSize'] ?? 0,
      failedSyncs: json['failedSyncs'] ?? 0,
      profilesNeedingRetry: json['profilesNeedingRetry'] ?? 0,
      averageResponseTime: json['averageResponseTime']?.toDouble() ?? 0.0,
      errorRate: json['errorRate']?.toDouble() ?? 0.0,
      alerts: (json['alerts'] as List?)
          ?.map((a) => SyncHealthAlert.fromJson(a))
          .toList() ?? [],
      error: json['error'],
    );
  }
}

/// Sync health alert model
class SyncHealthAlert {
  final String id;
  final SyncAlertType type;
  final SyncAlertSeverity severity;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  SyncHealthAlert({
    required this.id,
    required this.type,
    required this.severity,
    required this.message,
    required this.timestamp,
    required this.data,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'severity': severity.toString(),
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }

  factory SyncHealthAlert.fromJson(Map<String, dynamic> json) {
    return SyncHealthAlert(
      id: json['id'],
      type: SyncAlertType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => SyncAlertType.serviceCritical,
      ),
      severity: SyncAlertSeverity.values.firstWhere(
        (e) => e.toString() == json['severity'],
        orElse: () => SyncAlertSeverity.critical,
      ),
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      data: Map<String, dynamic>.from(json['data'] ?? {}),
    );
  }
}
