import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user_profile.dart';
import '../services/profile_update_service.dart';
import '../services/profile_completion_service.dart';
import '../utils/result.dart';

/// Profile state that includes loading states for different operations
class ProfileState {
  final UserProfile? profile;
  final bool isLoading;
  final bool isUpdating;
  final String? error;
  final String? successMessage;
  final Map<String, bool> fieldUpdating; // Track which fields are being updated

  const ProfileState({
    this.profile,
    this.isLoading = false,
    this.isUpdating = false,
    this.error,
    this.successMessage,
    this.fieldUpdating = const {},
  });

  ProfileState copyWith({
    UserProfile? profile,
    bool? isLoading,
    bool? isUpdating,
    String? error,
    String? successMessage,
    Map<String, bool>? fieldUpdating,
  }) {
    return ProfileState(
      profile: profile ?? this.profile,
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      error: error,
      successMessage: successMessage,
      fieldUpdating: fieldUpdating ?? this.fieldUpdating,
    );
  }

  /// Clear error and success messages
  ProfileState clearMessages() {
    return copyWith(error: null, successMessage: null);
  }

  /// Set field updating state
  ProfileState setFieldUpdating(String fieldName, bool updating) {
    final newFieldUpdating = Map<String, bool>.from(fieldUpdating);
    if (updating) {
      newFieldUpdating[fieldName] = true;
    } else {
      newFieldUpdating.remove(fieldName);
    }
    return copyWith(fieldUpdating: newFieldUpdating);
  }

  /// Check if a specific field is being updated
  bool isFieldUpdating(String fieldName) {
    return fieldUpdating[fieldName] ?? false;
  }
}

/// Profile provider that manages user profile state and operations
class ProfileNotifier extends StateNotifier<ProfileState> {
  ProfileNotifier(this._profileService, this._auth) : super(const ProfileState()) {
    // Initialize by loading any available cached profile immediately
    _initializeProfile();

    // Listen to auth state changes
    _auth.authStateChanges().listen((user) {
      print('🔄 ProfileProvider: Auth state changed');

      if (user == null) {
        print('❌ ProfileProvider: User logged out, clearing profile');
        // User logged out, clear profile and cache
        _clearProfileCache();
        state = const ProfileState();
      } else {
        print('✅ ProfileProvider: User logged in - UID: ${user.uid}');
        // Check if we already have cached data for this user
        _handleUserLogin(user.uid);
      }
    });
  }

  final ProfileUpdateService _profileService;
  final FirebaseAuth _auth;

  // Cache keys
  static const String _profileCacheKey = 'cached_user_profile';
  static const String _profileCacheTimestampKey = 'cached_user_profile_timestamp';
  static const String _profileCacheUserIdKey = 'cached_user_profile_user_id';

  // Cache duration (5 minutes)
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  /// Load user profile from database with caching support
  Future<void> loadProfile({bool showLoading = true, bool forceRefresh = false}) async {
    print('🔄 ProfileProvider: Starting loadProfile...');

    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      print('❌ ProfileProvider: No authenticated user');
      return;
    }

    // Check cache first if not forcing refresh
    if (!forceRefresh) {
      final cachedProfile = await _getCachedProfile(currentUser.uid);
      if (cachedProfile != null) {
        print('💾 ProfileProvider: Using cached profile');
        state = state.copyWith(
          profile: cachedProfile,
          isLoading: false,
          error: null,
        );
        return;
      }
    }

    if (showLoading) {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      print('🔗 ProfileProvider: Calling ProfileUpdateService.getProfile()...');
      final result = await _profileService.getProfile();

      print('📥 ProfileProvider: getProfile result received');

      result.when(
        success: (profile) {
          print('✅ ProfileProvider: Profile loaded successfully');
          print('📋 ProfileProvider: Profile data - ID: ${profile.id}, UID: ${profile.firebaseUid}, Email: ${profile.email}');
          print('📋 ProfileProvider: Sync version: ${profile.syncVersion}');

          // Cache the profile
          _cacheProfile(profile, currentUser.uid);

          // Cache the profile completion preference for cross-device sync
          _cacheProfileCompletionPreference(profile);

          state = state.copyWith(
            profile: profile,
            isLoading: false,
            error: null,
          );
          // Logger.info('Profile loaded successfully');
        },
        failure: (message, errorCode) {
          print('❌ ProfileProvider: Failed to load profile - $errorCode: $message');

          state = state.copyWith(
            isLoading: false,
            error: message,
          );
          // Logger.error('Failed to load profile: $message');
        },
      );
    } catch (e) {
      print('❌ ProfileProvider: Exception during loadProfile: $e');
      print('❌ ProfileProvider: Exception type: ${e.runtimeType}');

      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred while loading profile',
      );
      // Logger.error('Unexpected error loading profile: $e');
    }
  }

  /// Load cached profile if available and valid
  Future<UserProfile?> _getCachedProfile(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if cache is for the current user
      final cachedUserId = prefs.getString(_profileCacheUserIdKey);
      if (cachedUserId != userId) {
        print('💾 ProfileProvider: Cache is for different user, clearing...');
        await _clearProfileCache();
        return null;
      }

      // Check cache timestamp
      final timestampMs = prefs.getInt(_profileCacheTimestampKey);
      if (timestampMs == null) {
        print('💾 ProfileProvider: No cache timestamp found');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestampMs);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      if (cacheAge > _cacheValidDuration) {
        print('💾 ProfileProvider: Cache expired (${cacheAge.inMinutes} minutes old)');
        await _clearProfileCache();
        return null;
      }

      // Get cached profile data
      final profileJson = prefs.getString(_profileCacheKey);
      if (profileJson == null) {
        print('💾 ProfileProvider: No cached profile data found');
        return null;
      }

      final profileData = jsonDecode(profileJson) as Map<String, dynamic>;
      final profile = UserProfile.fromJson(profileData);

      print('💾 ProfileProvider: Found valid cached profile (${cacheAge.inMinutes} minutes old)');
      return profile;
    } catch (e) {
      print('❌ ProfileProvider: Error loading cached profile: $e');
      await _clearProfileCache();
      return null;
    }
  }

  /// Cache profile data to SharedPreferences
  Future<void> _cacheProfile(UserProfile profile, String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert profile to JSON
      final profileJson = jsonEncode(profile.toJson());

      // Save profile data, timestamp, and user ID
      await prefs.setString(_profileCacheKey, profileJson);
      await prefs.setInt(_profileCacheTimestampKey, DateTime.now().millisecondsSinceEpoch);
      await prefs.setString(_profileCacheUserIdKey, userId);

      print('💾 ProfileProvider: Profile cached successfully');
    } catch (e) {
      print('❌ ProfileProvider: Error caching profile: $e');
    }
  }

  /// Cache profile completion preference for cross-device synchronization
  Future<void> _cacheProfileCompletionPreference(UserProfile profile) async {
    try {
      await ProfileCompletionService.instance.cachePreferenceFromProfile(profile);
    } catch (e) {
      print('❌ ProfileProvider: Error caching profile completion preference: $e');
    }
  }

  /// Initialize profile by loading from cache if available
  Future<void> _initializeProfile() async {
    try {
      // Try to load from cache for any previously logged in user
      final prefs = await SharedPreferences.getInstance();
      final cachedProfileJson = prefs.getString(_profileCacheKey);
      final cachedTimestamp = prefs.getInt(_profileCacheTimestampKey);
      final cachedUserId = prefs.getString(_profileCacheUserIdKey);

      if (cachedProfileJson != null && cachedTimestamp != null && cachedUserId != null) {
        // Check if cache is still valid (within 5 minutes)
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
        final now = DateTime.now();
        final cacheAge = now.difference(cacheTime);

        if (cacheAge.inMinutes < 5) {
          final profileData = jsonDecode(cachedProfileJson) as Map<String, dynamic>;
          final cachedProfile = UserProfile.fromJson(profileData);

          print('💾 ProfileProvider: Loaded cached profile during initialization');
          print('💾 ProfileProvider: Cache age: ${cacheAge.inSeconds} seconds');

          state = state.copyWith(
            profile: cachedProfile,
            isLoading: false,
            error: null,
          );
        } else {
          print('💾 ProfileProvider: Cache expired during initialization');
        }
      } else {
        print('💾 ProfileProvider: No valid cache found during initialization');
      }
    } catch (e) {
      print('❌ ProfileProvider: Error loading cached profile during initialization: $e');
    }
  }

  /// Handle user login and profile loading
  Future<void> _handleUserLogin(String userId) async {
    try {
      // Check if we already have cached data for this specific user
      final cachedProfile = await _getCachedProfile(userId);

      if (cachedProfile != null) {
        print('💾 ProfileProvider: Using cached profile for logged in user');
        // We already have valid cached data, no need to make network call
        if (state.profile?.firebaseUid != userId) {
          // Update state only if it's for a different user or no profile loaded
          state = state.copyWith(
            profile: cachedProfile,
            isLoading: false,
            error: null,
          );
        }
      } else {
        print('🔄 ProfileProvider: No valid cache, loading from network');
        // No valid cache, load from network
        loadProfile();
      }
    } catch (e) {
      print('❌ ProfileProvider: Error handling user login: $e');
      // Fallback to network load
      loadProfile();
    }
  }

  /// Clear profile cache
  Future<void> _clearProfileCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_profileCacheKey);
      await prefs.remove(_profileCacheTimestampKey);
      await prefs.remove(_profileCacheUserIdKey);

      // Also clear profile completion preference cache
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        await ProfileCompletionService.instance.clearCachedPreference(currentUser.uid);
      }

      print('💾 ProfileProvider: Profile cache cleared');
    } catch (e) {
      print('❌ ProfileProvider: Error clearing profile cache: $e');
    }
  }

  /// Update profile with optimistic UI updates
  Future<bool> updateProfile(
    ProfileUpdateRequest request, {
    String? fieldName,
    bool optimisticUpdate = true,
  }) async {
    print('\n🔄 ProfileProvider: Starting updateProfile...');
    print('📋 ProfileProvider: Field name: $fieldName');
    print('📋 ProfileProvider: Optimistic update: $optimisticUpdate');

    // Set field-specific loading state
    if (fieldName != null) {
      state = state.setFieldUpdating(fieldName, true);
    } else {
      state = state.copyWith(isUpdating: true);
    }

    // Clear previous messages
    state = state.clearMessages();

    // Optimistic update - apply changes immediately to UI
    UserProfile? optimisticProfile;
    if (optimisticUpdate && state.profile != null) {
      print('🔄 ProfileProvider: Applying optimistic update...');
      print('📋 ProfileProvider: Current sync version: ${state.profile!.syncVersion}');
      print('📋 ProfileProvider: Request sync version: ${request.syncVersion}');

      // Validate sync version consistency
      if (request.syncVersion != null && request.syncVersion != state.profile!.syncVersion) {
        print('⚠️ ProfileProvider: Sync version mismatch detected!');
        print('⚠️ ProfileProvider: Expected: ${state.profile!.syncVersion}, Got: ${request.syncVersion}');
        // This could indicate a race condition or stale state
      }

      optimisticProfile = _applyOptimisticUpdate(state.profile!, request);
      print('📋 ProfileProvider: Optimistic sync version: ${optimisticProfile.syncVersion}');
      state = state.copyWith(profile: optimisticProfile);
    }

    try {
      print('🔗 ProfileProvider: Calling ProfileUpdateService...');
      final result = await _profileService.updateProfile(request);
      print('📥 ProfileProvider: Service result received');
      
      return result.when(
        success: (updateResult) {
          print('✅ ProfileProvider: Service returned success result');
          return updateResult.when(
            success: (profile, message, newSyncVersion, completionPercentage) {
              print('✅ ProfileProvider: Update successful!');
              print('📈 ProfileProvider: New sync version: $newSyncVersion');
              print('📊 ProfileProvider: Completion percentage: $completionPercentage');
              print('💬 ProfileProvider: Success message: $message');

              // Update cache with new profile data
              final currentUser = _auth.currentUser;
              if (currentUser != null) {
                _cacheProfile(profile, currentUser.uid);
                // Also cache the profile completion preference
                _cacheProfileCompletionPreference(profile);
              }

              state = state.copyWith(
                profile: profile,
                isUpdating: false,
                successMessage: message,
                fieldUpdating: {},
              );
              return true;
            },
            error: (error, message, errorCode, currentVersion, providedVersion) {
              print('❌ ProfileProvider: Update failed from service');
              print('❌ ProfileProvider: Error: $error');
              print('❌ ProfileProvider: Message: $message');
              print('❌ ProfileProvider: Error code: $errorCode');

              // Revert optimistic update on error
              if (optimisticUpdate && state.profile != null) {
                _revertOptimisticUpdate();
              }

              state = state.copyWith(
                isUpdating: false,
                error: message,
                fieldUpdating: {},
              );
              return false;
            },
          );
        },
        failure: (message, errorCode) {
          print('❌ ProfileProvider: Service returned failure');
          print('❌ ProfileProvider: Message: $message');
          print('❌ ProfileProvider: Error code: $errorCode');

          // Revert optimistic update on error
          if (optimisticUpdate && state.profile != null) {
            _revertOptimisticUpdate();
          }

          state = state.copyWith(
            isUpdating: false,
            error: message,
            fieldUpdating: {},
          );
          return false;
        },
      );
    } catch (e) {
      // Revert optimistic update on error
      if (optimisticUpdate && state.profile != null) {
        _revertOptimisticUpdate();
      }
      
      state = state.copyWith(
        isUpdating: false,
        error: 'An unexpected error occurred while updating profile',
        fieldUpdating: {},
      );
      // Logger.error('Unexpected error updating profile: $e');
      return false;
    }
  }

  /// Update a single field
  Future<bool> updateField(String fieldName, dynamic value) async {
    if (state.profile == null) return false;

    final request = _createSingleFieldRequest(fieldName, value);
    if (request == null) return false;

    return updateProfile(request, fieldName: fieldName);
  }

  /// Refresh profile data from server
  Future<void> refreshProfile() async {
    await loadProfile(showLoading: false, forceRefresh: true);
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear success message
  void clearSuccessMessage() {
    state = state.copyWith(successMessage: null);
  }

  /// Update last active timestamp
  Future<void> updateLastActive() async {
    await _profileService.updateLastActive();
  }

  /// Apply optimistic update to profile
  UserProfile _applyOptimisticUpdate(UserProfile profile, ProfileUpdateRequest request) {
    return profile.copyWith(
      displayName: request.displayName ?? profile.displayName,
      firstName: request.firstName ?? profile.firstName,
      lastName: request.lastName ?? profile.lastName,
      email: request.email ?? profile.email,
      phoneNumber: request.phoneNumber ?? profile.phoneNumber,
      dateOfBirth: request.dateOfBirth ?? profile.dateOfBirth,
      gender: request.gender ?? profile.gender,
      nativeLanguage: request.nativeLanguage ?? profile.nativeLanguage,
      addressLine1: request.addressLine1 ?? profile.addressLine1,
      addressLine2: request.addressLine2 ?? profile.addressLine2,
      city: request.city ?? profile.city,
      state: request.state ?? profile.state,
      postalCode: request.postalCode ?? profile.postalCode,
      country: request.country ?? profile.country,
      occupation: request.occupation ?? profile.occupation,
      companyName: request.companyName ?? profile.companyName,
      annualIncome: request.annualIncome ?? profile.annualIncome,
      // CRITICAL FIX: Update sync version optimistically to prevent stale version conflicts
      // The server will increment the sync version, so we need to match that in our optimistic update
      syncVersion: profile.syncVersion + 1,
    );
  }

  /// Revert optimistic update by reloading from server
  Future<void> _revertOptimisticUpdate() async {
    await loadProfile(showLoading: false);
  }

  /// Create a single field update request
  ProfileUpdateRequest? _createSingleFieldRequest(String fieldName, dynamic value) {
    if (state.profile == null) return null;

    switch (fieldName) {
      case 'displayName':
        return ProfileUpdateRequest(
          displayName: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'firstName':
        return ProfileUpdateRequest(
          firstName: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'lastName':
        return ProfileUpdateRequest(
          lastName: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'email':
        return ProfileUpdateRequest(
          email: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'phoneNumber':
        return ProfileUpdateRequest(
          phoneNumber: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'dateOfBirth':
        return ProfileUpdateRequest(
          dateOfBirth: value as DateTime?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'gender':
        return ProfileUpdateRequest(
          gender: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'nativeLanguage':
        return ProfileUpdateRequest(
          nativeLanguage: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'city':
        return ProfileUpdateRequest(
          city: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'state':
        return ProfileUpdateRequest(
          state: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      case 'occupation':
        return ProfileUpdateRequest(
          occupation: value as String?,
          syncVersion: state.profile!.syncVersion,
        );
      default:
        return null;
    }
  }
}

/// Providers
final profileUpdateServiceProvider = Provider<ProfileUpdateService>((ref) {
  return ProfileUpdateService();
});

final profileProvider = StateNotifierProvider<ProfileNotifier, ProfileState>((ref) {
  final profileService = ref.watch(profileUpdateServiceProvider);
  return ProfileNotifier(profileService, FirebaseAuth.instance);
});

/// Convenience providers for specific profile data
final currentProfileProvider = Provider<UserProfile?>((ref) {
  return ref.watch(profileProvider).profile;
});

final profileCompletionProvider = Provider<int>((ref) {
  return ref.watch(profileProvider).profile?.profileCompletionPercentage ?? 0;
});

final isProfileLoadingProvider = Provider<bool>((ref) {
  return ref.watch(profileProvider).isLoading;
});

final isProfileUpdatingProvider = Provider<bool>((ref) {
  return ref.watch(profileProvider).isUpdating;
});

final profileErrorProvider = Provider<String?>((ref) {
  return ref.watch(profileProvider).error;
});

final profileSuccessMessageProvider = Provider<String?>((ref) {
  return ref.watch(profileProvider).successMessage;
});
