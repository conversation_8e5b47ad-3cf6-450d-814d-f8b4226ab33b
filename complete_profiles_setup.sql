-- =============================================================================
-- COMPLETE PROFILES SYSTEM SETUP - FRESH START
-- =============================================================================
-- This file creates the entire profiles system from scratch with all fixes
-- Run this after deleting existing tables for a clean setup
-- Created: 2024-08-03
-- =============================================================================

-- Drop existing objects if they exist (for clean setup)
DROP TABLE IF EXISTS public.profiles CASCADE;

-- Drop all variations of create_user_profile function
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.create_user_profile(TEXT) CASCADE;

-- Drop all variations of sync_firebase_user_data function
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.sync_firebase_user_data(TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT, TEXT) CASCADE;

-- Drop all variations of handle_user_signup function
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS public.handle_user_signup(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, BOOLEAN, TEXT) CASCADE;

-- Drop utility functions
DROP FUNCTION IF EXISTS public.clean_phone_number(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS public.calculate_profile_completion(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.update_last_active(TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.test_profiles_setup() CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS public.sync_status CASCADE;

-- =============================================================================
-- 1. CREATE CUSTOM TYPES
-- =============================================================================

-- Enum for sync status tracking
CREATE TYPE public.sync_status AS ENUM (
    'pending',
    'synced',
    'failed',
    'retry'
);

-- =============================================================================
-- 2. CREATE PROFILES TABLE
-- =============================================================================

CREATE TABLE public.profiles (
    -- Primary key and Firebase integration
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    firebase_uid TEXT UNIQUE NOT NULL,
    
    -- Basic user information from Firebase Auth
    email TEXT,
    display_name TEXT,
    phone_number TEXT,
    photo_url TEXT,
    is_email_verified BOOLEAN DEFAULT FALSE,
    auth_provider TEXT DEFAULT 'email',
    
    -- User role (text field - no separate roles table needed)
    role TEXT DEFAULT NULL, -- Can be 'admin', 'user', 'agent', etc. - assigned manually
    
    -- Personal information (to be filled by user)
    first_name TEXT,
    last_name TEXT,
    date_of_birth DATE,
    gender TEXT,
    native_language TEXT DEFAULT NULL, -- Nullable, no default
    
    -- Address information
    address_line_1 TEXT,
    address_line_2 TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    country TEXT DEFAULT 'India',
    
    -- Professional information
    occupation TEXT,
    company_name TEXT,
    annual_income DECIMAL(15,2),
    
    -- App preferences and settings
    push_notifications_enabled BOOLEAN DEFAULT TRUE,
    email_notifications_enabled BOOLEAN DEFAULT FALSE,
    sms_notifications_enabled BOOLEAN DEFAULT TRUE,
    location_services_enabled BOOLEAN DEFAULT TRUE,
    analytics_enabled BOOLEAN DEFAULT FALSE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    biometric_enabled BOOLEAN DEFAULT TRUE,
    auto_download_enabled BOOLEAN DEFAULT FALSE,
    profile_completion_dont_ask_again BOOLEAN DEFAULT FALSE,
    
    -- Subscription management
    subscription_plan TEXT DEFAULT 'Free' CHECK (subscription_plan IN ('Free', 'Premium', 'Enterprise')),
    subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'inactive', 'cancelled', 'expired')),
    subscription_start_date TIMESTAMPTZ DEFAULT NOW(),
    subscription_end_date TIMESTAMPTZ,
    
    -- Profile completion tracking
    profile_completion_percentage INTEGER DEFAULT 0 CHECK (profile_completion_percentage >= 0 AND profile_completion_percentage <= 100),
    onboarding_completed BOOLEAN DEFAULT FALSE,
    terms_accepted BOOLEAN DEFAULT FALSE,
    privacy_policy_accepted BOOLEAN DEFAULT FALSE,
    
    -- Sync tracking fields
    sync_status public.sync_status DEFAULT 'synced',
    last_sync_at TIMESTAMPTZ DEFAULT NOW(),
    sync_error_message TEXT,
    sync_retry_count INTEGER DEFAULT 0,
    sync_version INTEGER DEFAULT 1,
    
    -- Timestamp fields
    last_sign_in_at TIMESTAMPTZ DEFAULT NOW(), -- This field was causing the issue
    last_active_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ DEFAULT NULL,
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_phone CHECK (phone_number IS NULL OR length(phone_number) >= 10),
    CONSTRAINT valid_gender CHECK (gender IS NULL OR gender IN ('male', 'female', 'other', 'prefer_not_to_say')),
    CONSTRAINT valid_auth_provider CHECK (auth_provider IN ('email', 'google', 'apple', 'phone')),
    CONSTRAINT valid_role CHECK (role IS NULL OR role IN ('admin', 'user', 'agent', 'moderator', 'support'))
);

-- =============================================================================
-- 3. CREATE INDEXES FOR PERFORMANCE
-- =============================================================================

-- Primary lookup indexes
CREATE UNIQUE INDEX idx_profiles_firebase_uid ON public.profiles(firebase_uid);
CREATE INDEX idx_profiles_email ON public.profiles(email) WHERE email IS NOT NULL;
CREATE INDEX idx_profiles_phone ON public.profiles(phone_number) WHERE phone_number IS NOT NULL;

-- Query optimization indexes
CREATE INDEX idx_profiles_role ON public.profiles(role) WHERE role IS NOT NULL;
CREATE INDEX idx_profiles_subscription ON public.profiles(subscription_plan, subscription_status);
CREATE INDEX idx_profiles_sync_status ON public.profiles(sync_status);
CREATE INDEX idx_profiles_active ON public.profiles(deleted_at) WHERE deleted_at IS NULL;

-- Timestamp indexes
CREATE INDEX idx_profiles_created_at ON public.profiles(created_at);
CREATE INDEX idx_profiles_last_sign_in ON public.profiles(last_sign_in_at);
CREATE INDEX idx_profiles_last_active ON public.profiles(last_active_at);

-- =============================================================================
-- 4. CREATE UTILITY FUNCTIONS
-- =============================================================================

-- Function to clean and validate phone numbers
CREATE OR REPLACE FUNCTION public.clean_phone_number(phone_input TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Remove all non-digit characters except +
    phone_input := regexp_replace(phone_input, '[^\d+]', '', 'g');
    
    -- Handle Indian numbers
    IF phone_input ~ '^[6-9]\d{9}$' THEN
        -- Indian mobile number without country code
        RETURN '+91' || phone_input;
    ELSIF phone_input ~ '^\+91[6-9]\d{9}$' THEN
        -- Indian mobile number with country code
        RETURN phone_input;
    ELSIF phone_input ~ '^\+\d{10,15}$' THEN
        -- International number with country code
        RETURN phone_input;
    ELSE
        -- Return as-is if doesn't match expected patterns
        RETURN phone_input;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic updated_at
CREATE TRIGGER trigger_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =============================================================================
-- 5. CREATE PROFILE MANAGEMENT FUNCTIONS
-- =============================================================================

-- Function to create user profile with all latest fixes
CREATE OR REPLACE FUNCTION public.create_user_profile(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_auth_provider TEXT DEFAULT 'email',
    p_is_email_verified BOOLEAN DEFAULT FALSE,
    p_last_sign_in_at TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_profile_id UUID;
    cleaned_phone TEXT;
    sign_in_timestamp TIMESTAMPTZ;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RAISE EXCEPTION 'Firebase UID is required and cannot be empty';
    END IF;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Parse last_sign_in_at or use current time
    IF p_last_sign_in_at IS NOT NULL AND p_last_sign_in_at != '' THEN
        sign_in_timestamp := p_last_sign_in_at::TIMESTAMPTZ;
    ELSE
        sign_in_timestamp := NOW();
    END IF;

    -- Insert new profile with all fields including last_sign_in_at
    INSERT INTO public.profiles (
        firebase_uid,
        email,
        display_name,
        phone_number,
        photo_url,
        auth_provider,
        is_email_verified,
        last_sign_in_at,
        -- role intentionally omitted - will remain NULL for manual assignment
        -- App preferences with sensible defaults
        push_notifications_enabled,
        email_notifications_enabled,
        sms_notifications_enabled,
        location_services_enabled,
        analytics_enabled,
        dark_mode_enabled,
        biometric_enabled,
        auto_download_enabled,
        profile_completion_dont_ask_again,
        -- Subscription defaults
        subscription_plan,
        subscription_status,
        subscription_start_date,
        -- Sync tracking
        sync_status,
        last_sync_at,
        sync_version
    ) VALUES (
        p_firebase_uid,
        p_email,
        p_display_name,
        cleaned_phone,
        p_photo_url,
        p_auth_provider,
        p_is_email_verified,
        sign_in_timestamp,
        -- role omitted - remains NULL
        -- Default app preferences
        TRUE,  -- push_notifications_enabled
        FALSE, -- email_notifications_enabled
        TRUE,  -- sms_notifications_enabled
        TRUE,  -- location_services_enabled
        FALSE, -- analytics_enabled
        FALSE, -- dark_mode_enabled
        TRUE,  -- biometric_enabled
        FALSE, -- auto_download_enabled
        FALSE, -- profile_completion_dont_ask_again
        -- Default subscription
        'Free',
        'active',
        NOW(),
        -- Sync tracking
        'synced',
        NOW(),
        1
    ) RETURNING id INTO new_profile_id;

    RETURN new_profile_id;

EXCEPTION
    WHEN unique_violation THEN
        -- Profile already exists, update last_sign_in_at and return existing profile ID
        UPDATE public.profiles SET
            last_sign_in_at = sign_in_timestamp,
            last_sync_at = NOW(),
            updated_at = NOW()
        WHERE firebase_uid = p_firebase_uid;
        
        SELECT id INTO new_profile_id
        FROM public.profiles
        WHERE firebase_uid = p_firebase_uid;
        RETURN new_profile_id;
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE EXCEPTION 'Failed to create profile for user %: %', p_firebase_uid, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to sync Firebase Auth user data to profile with proper last_sign_in_at handling
CREATE OR REPLACE FUNCTION public.sync_firebase_user_data(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_is_email_verified BOOLEAN DEFAULT NULL,
    p_auth_provider TEXT DEFAULT NULL,
    p_last_sign_in_at TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    profile_exists BOOLEAN;
    cleaned_phone TEXT;
    sign_in_timestamp TIMESTAMPTZ;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RAISE EXCEPTION 'Firebase UID is required and cannot be empty';
    END IF;

    -- Check if profile exists
    SELECT EXISTS(
        SELECT 1 FROM public.profiles
        WHERE firebase_uid = p_firebase_uid
        AND deleted_at IS NULL
    ) INTO profile_exists;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Parse timestamp or use current time
    IF p_last_sign_in_at IS NOT NULL AND p_last_sign_in_at != '' THEN
        sign_in_timestamp := p_last_sign_in_at::TIMESTAMPTZ;
    ELSE
        sign_in_timestamp := NOW();
    END IF;

    IF profile_exists THEN
        -- Update existing profile with Firebase data
        UPDATE public.profiles SET
            email = COALESCE(p_email, email),
            display_name = COALESCE(p_display_name, display_name),
            phone_number = COALESCE(cleaned_phone, phone_number),
            photo_url = COALESCE(p_photo_url, photo_url),
            is_email_verified = COALESCE(p_is_email_verified, is_email_verified),
            auth_provider = COALESCE(p_auth_provider, auth_provider),
            last_sign_in_at = sign_in_timestamp,  -- Always update this
            last_active_at = NOW(),
            last_sync_at = NOW(),
            sync_status = 'synced',
            updated_at = NOW()
        WHERE firebase_uid = p_firebase_uid;
    ELSE
        -- Create new profile using updated function (now includes last_sign_in_at)
        PERFORM public.create_user_profile(
            p_firebase_uid,
            p_email,
            p_display_name,
            cleaned_phone,
            p_photo_url,
            COALESCE(p_auth_provider, 'email'),
            COALESCE(p_is_email_verified, FALSE),
            p_last_sign_in_at  -- Pass the timestamp parameter
        );
    END IF;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        -- Update sync status to failed
        UPDATE public.profiles SET
            sync_status = 'failed',
            sync_error_message = SQLERRM,
            sync_retry_count = sync_retry_count + 1,
            last_sync_at = NOW()
        WHERE firebase_uid = p_firebase_uid;

        RAISE EXCEPTION 'Failed to sync user data for %: %', p_firebase_uid, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle user signup with comprehensive error handling
CREATE OR REPLACE FUNCTION public.handle_user_signup(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_auth_provider TEXT DEFAULT 'email',
    p_is_email_verified BOOLEAN DEFAULT FALSE,
    p_last_sign_in_at TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    profile_id UUID;
    result JSON;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Firebase UID is required',
            'message', 'Invalid user data provided'
        );
    END IF;

    -- Create or update profile using the updated function
    SELECT public.create_user_profile(
        p_firebase_uid,
        p_email,
        p_display_name,
        p_phone_number,
        p_photo_url,
        p_auth_provider,
        p_is_email_verified,
        p_last_sign_in_at  -- Pass the timestamp
    ) INTO profile_id;

    -- Build success response
    result := json_build_object(
        'success', true,
        'profile_id', profile_id,
        'firebase_uid', p_firebase_uid,
        'email', p_email,
        'display_name', p_display_name,
        'message', 'Profile created successfully'
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Return error response
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to create user profile'
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user profile with comprehensive field support
CREATE OR REPLACE FUNCTION public.update_user_profile(
    p_firebase_uid TEXT,
    p_display_name TEXT DEFAULT NULL,
    p_first_name TEXT DEFAULT NULL,
    p_last_name TEXT DEFAULT NULL,
    p_email TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_date_of_birth DATE DEFAULT NULL,
    p_gender TEXT DEFAULT NULL,
    p_native_language TEXT DEFAULT NULL,
    p_address_line_1 TEXT DEFAULT NULL,
    p_address_line_2 TEXT DEFAULT NULL,
    p_city TEXT DEFAULT NULL,
    p_state TEXT DEFAULT NULL,
    p_postal_code TEXT DEFAULT NULL,
    p_country TEXT DEFAULT NULL,
    p_occupation TEXT DEFAULT NULL,
    p_company_name TEXT DEFAULT NULL,
    p_annual_income DECIMAL(15,2) DEFAULT NULL,
    p_sync_version INTEGER DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    profile_record RECORD;
    cleaned_phone TEXT;
    new_completion_percentage INTEGER;
    rows_affected INTEGER;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'INVALID_UID',
            'message', 'Firebase UID is required'
        );
    END IF;

    -- Check if profile exists and get current version
    SELECT * INTO profile_record
    FROM public.profiles
    WHERE firebase_uid = p_firebase_uid AND deleted_at IS NULL;

    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'PROFILE_NOT_FOUND',
            'message', 'Profile not found for the specified user'
        );
    END IF;

    -- Check for version conflict (optimistic locking)
    IF p_sync_version IS NOT NULL AND profile_record.sync_version != p_sync_version THEN
        RETURN json_build_object(
            'success', false,
            'error', 'VERSION_CONFLICT',
            'message', 'Profile has been updated by another session. Please refresh and try again.',
            'current_version', profile_record.sync_version,
            'provided_version', p_sync_version
        );
    END IF;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Update profile with provided fields (only update non-null values)
    UPDATE public.profiles SET
        display_name = COALESCE(p_display_name, display_name),
        first_name = COALESCE(p_first_name, first_name),
        last_name = COALESCE(p_last_name, last_name),
        email = COALESCE(p_email, email),
        phone_number = COALESCE(cleaned_phone, phone_number),
        date_of_birth = COALESCE(p_date_of_birth, date_of_birth),
        gender = COALESCE(p_gender, gender),
        native_language = COALESCE(p_native_language, native_language),
        address_line_1 = COALESCE(p_address_line_1, address_line_1),
        address_line_2 = COALESCE(p_address_line_2, address_line_2),
        city = COALESCE(p_city, city),
        state = COALESCE(p_state, state),
        postal_code = COALESCE(p_postal_code, postal_code),
        country = COALESCE(p_country, country),
        occupation = COALESCE(p_occupation, occupation),
        company_name = COALESCE(p_company_name, company_name),
        annual_income = COALESCE(p_annual_income, annual_income),
        sync_version = sync_version + 1,
        last_sync_at = NOW(),
        sync_status = 'synced',
        updated_at = NOW()
    WHERE firebase_uid = p_firebase_uid AND deleted_at IS NULL;

    GET DIAGNOSTICS rows_affected = ROW_COUNT;

    IF rows_affected = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', 'UPDATE_FAILED',
            'message', 'No rows were updated. Profile may have been deleted.'
        );
    END IF;

    -- Calculate new profile completion percentage
    SELECT public.calculate_profile_completion(profile_record.id) INTO new_completion_percentage;

    -- Get updated profile data
    SELECT * INTO profile_record
    FROM public.profiles
    WHERE firebase_uid = p_firebase_uid AND deleted_at IS NULL;

    -- Return success response with updated data
    RETURN json_build_object(
        'success', true,
        'message', 'Profile updated successfully',
        'profile_id', profile_record.id,
        'sync_version', profile_record.sync_version,
        'completion_percentage', new_completion_percentage,
        'updated_at', profile_record.updated_at,
        'profile_data', json_build_object(
            'firebase_uid', profile_record.firebase_uid,
            'display_name', profile_record.display_name,
            'first_name', profile_record.first_name,
            'last_name', profile_record.last_name,
            'email', profile_record.email,
            'phone_number', profile_record.phone_number,
            'date_of_birth', profile_record.date_of_birth,
            'gender', profile_record.gender,
            'native_language', profile_record.native_language,
            'address_line_1', profile_record.address_line_1,
            'address_line_2', profile_record.address_line_2,
            'city', profile_record.city,
            'state', profile_record.state,
            'postal_code', profile_record.postal_code,
            'country', profile_record.country,
            'occupation', profile_record.occupation,
            'company_name', profile_record.company_name,
            'annual_income', profile_record.annual_income
        )
    );

EXCEPTION
    WHEN check_violation THEN
        RETURN json_build_object(
            'success', false,
            'error', 'VALIDATION_ERROR',
            'message', 'Invalid data provided: ' || SQLERRM
        );
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'DATABASE_ERROR',
            'message', 'Failed to update profile: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get complete user profile data
CREATE OR REPLACE FUNCTION public.get_user_profile(p_firebase_uid TEXT)
RETURNS JSON AS $$
DECLARE
    profile_record RECORD;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'INVALID_UID',
            'message', 'Firebase UID is required'
        );
    END IF;

    -- Get profile data
    SELECT * INTO profile_record
    FROM public.profiles
    WHERE firebase_uid = p_firebase_uid AND deleted_at IS NULL;

    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'PROFILE_NOT_FOUND',
            'message', 'Profile not found for the specified user'
        );
    END IF;

    -- Return complete profile data
    RETURN json_build_object(
        'success', true,
        'profile_data', json_build_object(
            'id', profile_record.id,
            'firebase_uid', profile_record.firebase_uid,
            'email', profile_record.email,
            'display_name', profile_record.display_name,
            'first_name', profile_record.first_name,
            'last_name', profile_record.last_name,
            'phone_number', profile_record.phone_number,
            'photo_url', profile_record.photo_url,
            'is_email_verified', profile_record.is_email_verified,
            'auth_provider', profile_record.auth_provider,
            'role', profile_record.role,
            'date_of_birth', profile_record.date_of_birth,
            'gender', profile_record.gender,
            'native_language', profile_record.native_language,
            'address_line_1', profile_record.address_line_1,
            'address_line_2', profile_record.address_line_2,
            'city', profile_record.city,
            'state', profile_record.state,
            'postal_code', profile_record.postal_code,
            'country', profile_record.country,
            'occupation', profile_record.occupation,
            'company_name', profile_record.company_name,
            'annual_income', profile_record.annual_income,
            'push_notifications_enabled', profile_record.push_notifications_enabled,
            'email_notifications_enabled', profile_record.email_notifications_enabled,
            'sms_notifications_enabled', profile_record.sms_notifications_enabled,
            'location_services_enabled', profile_record.location_services_enabled,
            'analytics_enabled', profile_record.analytics_enabled,
            'dark_mode_enabled', profile_record.dark_mode_enabled,
            'biometric_enabled', profile_record.biometric_enabled,
            'auto_download_enabled', profile_record.auto_download_enabled,
            'profile_completion_dont_ask_again', profile_record.profile_completion_dont_ask_again,
            'subscription_plan', profile_record.subscription_plan,
            'subscription_status', profile_record.subscription_status,
            'subscription_start_date', profile_record.subscription_start_date,
            'subscription_end_date', profile_record.subscription_end_date,
            'profile_completion_percentage', profile_record.profile_completion_percentage,
            'onboarding_completed', profile_record.onboarding_completed,
            'terms_accepted', profile_record.terms_accepted,
            'privacy_policy_accepted', profile_record.privacy_policy_accepted,
            'sync_version', profile_record.sync_version,
            'last_sign_in_at', profile_record.last_sign_in_at,
            'last_active_at', profile_record.last_active_at,
            'created_at', profile_record.created_at,
            'updated_at', profile_record.updated_at
        )
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'DATABASE_ERROR',
            'message', 'Failed to retrieve profile: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- 6. ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (
        firebase_uid = auth.jwt() ->> 'sub'
        OR
        auth.jwt() ->> 'role' = 'admin'
    );

-- Policy: Users can insert their own profile
CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (
        firebase_uid = auth.jwt() ->> 'sub'
    );

-- Policy: Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (
        firebase_uid = auth.jwt() ->> 'sub'
        OR
        auth.jwt() ->> 'role' = 'admin'
    );

-- Policy: Only admins can delete profiles (soft delete)
CREATE POLICY "Admins can delete profiles" ON public.profiles
    FOR UPDATE USING (
        auth.jwt() ->> 'role' = 'admin'
    );

-- =============================================================================
-- 7. ADDITIONAL UTILITY FUNCTIONS
-- =============================================================================

-- Function to calculate profile completion percentage
CREATE OR REPLACE FUNCTION public.calculate_profile_completion(profile_id UUID)
RETURNS INTEGER AS $$
DECLARE
    completion_score INTEGER := 0;
    total_fields INTEGER := 20; -- Total number of optional fields
    profile_record RECORD;
BEGIN
    SELECT * INTO profile_record FROM public.profiles WHERE id = profile_id;

    IF NOT FOUND THEN
        RETURN 0;
    END IF;

    -- Basic information (5 points each)
    IF profile_record.first_name IS NOT NULL THEN completion_score := completion_score + 5; END IF;
    IF profile_record.last_name IS NOT NULL THEN completion_score := completion_score + 5; END IF;
    IF profile_record.date_of_birth IS NOT NULL THEN completion_score := completion_score + 5; END IF;
    IF profile_record.gender IS NOT NULL THEN completion_score := completion_score + 5; END IF;
    IF profile_record.phone_number IS NOT NULL THEN completion_score := completion_score + 5; END IF;

    -- Address information (4 points each)
    IF profile_record.address_line_1 IS NOT NULL THEN completion_score := completion_score + 4; END IF;
    IF profile_record.city IS NOT NULL THEN completion_score := completion_score + 4; END IF;
    IF profile_record.state IS NOT NULL THEN completion_score := completion_score + 4; END IF;
    IF profile_record.postal_code IS NOT NULL THEN completion_score := completion_score + 4; END IF;

    -- Professional information (6 points each)
    IF profile_record.occupation IS NOT NULL THEN completion_score := completion_score + 6; END IF;
    IF profile_record.company_name IS NOT NULL THEN completion_score := completion_score + 6; END IF;
    IF profile_record.annual_income IS NOT NULL THEN completion_score := completion_score + 6; END IF;

    -- Profile image and verification (5 points each)
    IF profile_record.photo_url IS NOT NULL THEN completion_score := completion_score + 5; END IF;
    IF profile_record.is_email_verified = TRUE THEN completion_score := completion_score + 5; END IF;

    -- Terms and onboarding (10 points each)
    IF profile_record.terms_accepted = TRUE THEN completion_score := completion_score + 10; END IF;
    IF profile_record.privacy_policy_accepted = TRUE THEN completion_score := completion_score + 10; END IF;
    IF profile_record.onboarding_completed = TRUE THEN completion_score := completion_score + 10; END IF;

    -- Language preference (3 points)
    IF profile_record.native_language IS NOT NULL THEN completion_score := completion_score + 3; END IF;

    -- Country (2 points)
    IF profile_record.country IS NOT NULL THEN completion_score := completion_score + 2; END IF;

    -- Display name (3 points)
    IF profile_record.display_name IS NOT NULL THEN completion_score := completion_score + 3; END IF;

    -- Ensure score doesn't exceed 100
    IF completion_score > 100 THEN
        completion_score := 100;
    END IF;

    -- Update the profile with calculated completion percentage
    UPDATE public.profiles
    SET profile_completion_percentage = completion_score
    WHERE id = profile_id;

    RETURN completion_score;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update last active timestamp
CREATE OR REPLACE FUNCTION public.update_last_active(p_firebase_uid TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.profiles
    SET
        last_active_at = NOW(),
        updated_at = NOW()
    WHERE firebase_uid = p_firebase_uid AND deleted_at IS NULL;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- 8. VERIFICATION AND TESTING
-- =============================================================================

-- Function to test the complete setup
CREATE OR REPLACE FUNCTION public.test_profiles_setup()
RETURNS TABLE(test_name TEXT, status TEXT, details TEXT) AS $$
BEGIN
    -- Test 1: Create a test profile
    RETURN QUERY
    SELECT
        'Profile Creation Test'::TEXT,
        CASE
            WHEN public.sync_firebase_user_data(
                'test-user-setup-' || extract(epoch from now())::text,
                '<EMAIL>',
                'Test Setup User',
                '+************',
                'https://example.com/photo.jpg',
                TRUE,
                'google',
                NOW()::TEXT
            ) THEN 'PASS ✅'
            ELSE 'FAIL ❌'
        END,
        'Testing profile creation with sync function'::TEXT;

    -- Test 2: Check if last_sign_in_at is set
    RETURN QUERY
    SELECT
        'Last Sign In Test'::TEXT,
        CASE
            WHEN EXISTS(
                SELECT 1 FROM public.profiles
                WHERE firebase_uid LIKE 'test-user-setup-%'
                AND last_sign_in_at IS NOT NULL
            ) THEN 'PASS ✅'
            ELSE 'FAIL ❌'
        END,
        'Checking if last_sign_in_at is properly set'::TEXT;

    -- Test 3: Check default values
    RETURN QUERY
    SELECT
        'Default Values Test'::TEXT,
        CASE
            WHEN EXISTS(
                SELECT 1 FROM public.profiles
                WHERE firebase_uid LIKE 'test-user-setup-%'
                AND role IS NULL
                AND subscription_plan = 'Free'
                AND native_language IS NULL
            ) THEN 'PASS ✅'
            ELSE 'FAIL ❌'
        END,
        'Checking role=NULL, subscription=Free, native_language=NULL'::TEXT;

    -- Test 4: Check RLS policies
    RETURN QUERY
    SELECT
        'RLS Policies Test'::TEXT,
        CASE
            WHEN EXISTS(
                SELECT 1 FROM pg_policies
                WHERE tablename = 'profiles'
                AND schemaname = 'public'
            ) THEN 'PASS ✅'
            ELSE 'FAIL ❌'
        END,
        'Checking if RLS policies are created'::TEXT;

    -- Clean up test data
    DELETE FROM public.profiles WHERE firebase_uid LIKE 'test-user-setup-%';

    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- 9. SETUP COMPLETION MESSAGE
-- =============================================================================

-- Display setup completion message
DO $$
BEGIN
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'PROFILES SYSTEM SETUP COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Profiles table created with all fields and constraints';
    RAISE NOTICE '✅ All database functions created with latest fixes';
    RAISE NOTICE '✅ Row Level Security (RLS) policies applied';
    RAISE NOTICE '✅ Indexes created for optimal performance';
    RAISE NOTICE '✅ Utility functions for profile management';
    RAISE NOTICE '✅ last_sign_in_at tracking fix included';
    RAISE NOTICE '';
    RAISE NOTICE 'KEY FEATURES:';
    RAISE NOTICE '• role field is nullable (no automatic assignment)';
    RAISE NOTICE '• subscription_plan defaults to "Free"';
    RAISE NOTICE '• native_language is nullable with no default';
    RAISE NOTICE '• last_sign_in_at properly tracked on every login';
    RAISE NOTICE '• Complete sync tracking with error handling';
    RAISE NOTICE '• Profile completion percentage calculation';
    RAISE NOTICE '';
    RAISE NOTICE 'NEXT STEPS:';
    RAISE NOTICE '1. Test the setup by running: SELECT * FROM public.test_profiles_setup();';
    RAISE NOTICE '2. Update your Flutter app to use the new database functions';
    RAISE NOTICE '3. Test Firebase Auth integration with profile creation';
    RAISE NOTICE '';
    RAISE NOTICE 'Your profiles system is now ready for production use! 🚀';
    RAISE NOTICE '=============================================================================';
END $$;
