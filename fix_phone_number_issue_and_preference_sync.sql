-- CRITICAL FIX: Phone Number Issue and Profile Completion Preference Sync
-- This script fixes the phone number cleaning bug and adds preference sync support

-- 1. First, drop the existing function and recreate it to prevent double +91 prefix
DROP FUNCTION IF EXISTS public.clean_phone_number(TEXT);

CREATE OR REPLACE FUNCTION public.clean_phone_number(phone TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Return NULL if input is NULL or empty
    IF phone IS NULL OR phone = '' THEN
        RETURN NULL;
    END IF;

    -- Remove all non-digit characters except +
    phone := regexp_replace(phone, '[^\d+]', '', 'g');

    -- Handle different phone number formats
    IF phone ~ '^\+91[6-9]\d{9}$' THEN
        -- Already has correct +91 prefix with 10-digit Indian mobile number
        RETURN phone;
    ELSIF phone ~ '^91[6-9]\d{9}$' THEN
        -- Has 91 prefix but missing +
        RETURN '+' || phone;
    ELSIF phone ~ '^[6-9]\d{9}$' THEN
        -- Indian mobile number without country code (exactly 10 digits starting with 6-9)
        RETURN '+91' || phone;
    ELSIF phone ~ '^\+\d{10,15}$' THEN
        -- International number with country code (10-15 digits total)
        RETURN phone;
    ELSE
        -- Return as-is for other formats (don't modify)
        RETURN phone;
    END IF;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 2. Fix your phone number back to the correct format
UPDATE public.profiles 
SET 
    phone_number = '+************',
    updated_at = NOW()
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2'
AND phone_number = '+91************';

-- 3. Drop existing update_user_profile function and recreate with preference support
DROP FUNCTION IF EXISTS public.update_user_profile;

CREATE OR REPLACE FUNCTION public.update_user_profile(
    p_firebase_uid TEXT,
    p_display_name TEXT DEFAULT NULL,
    p_first_name TEXT DEFAULT NULL,
    p_last_name TEXT DEFAULT NULL,
    p_email TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_date_of_birth DATE DEFAULT NULL,
    p_gender TEXT DEFAULT NULL,
    p_native_language TEXT DEFAULT NULL,
    p_address_line_1 TEXT DEFAULT NULL,
    p_address_line_2 TEXT DEFAULT NULL,
    p_city TEXT DEFAULT NULL,
    p_state TEXT DEFAULT NULL,
    p_postal_code TEXT DEFAULT NULL,
    p_country TEXT DEFAULT NULL,
    p_occupation TEXT DEFAULT NULL,
    p_company_name TEXT DEFAULT NULL,
    p_annual_income DECIMAL(15,2) DEFAULT NULL,
    p_profile_completion_dont_ask_again BOOLEAN DEFAULT NULL,
    p_sync_version INTEGER DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    current_profile RECORD;
    cleaned_phone TEXT;
    update_count INTEGER;
    new_sync_version INTEGER;
    completion_percentage DECIMAL(5,2);
    result JSON;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Firebase UID is required',
            'message', 'Invalid user data provided',
            'error_code', 'INVALID_INPUT'
        );
    END IF;

    -- Get current profile with row-level locking
    SELECT * INTO current_profile
    FROM public.profiles
    WHERE firebase_uid = p_firebase_uid
    AND deleted_at IS NULL
    FOR UPDATE;

    -- Check if profile exists
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Profile not found',
            'message', 'No profile found for the specified user',
            'error_code', 'PROFILE_NOT_FOUND'
        );
    END IF;

    -- Validate sync version for optimistic locking (if provided)
    IF p_sync_version IS NOT NULL AND current_profile.sync_version != p_sync_version THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Sync conflict detected',
            'message', 'Profile has been modified by another process',
            'error_code', 'SYNC_CONFLICT',
            'current_version', current_profile.sync_version,
            'provided_version', p_sync_version
        );
    END IF;

    -- IMPORTANT: Only clean phone number if it's explicitly being updated
    -- This prevents accidental phone number modifications during other updates
    IF p_phone_number IS NOT NULL THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Calculate new sync version
    new_sync_version := COALESCE(current_profile.sync_version, 0) + 1;

    -- Update profile with provided fields (only non-null values)
    UPDATE public.profiles SET
        display_name = COALESCE(p_display_name, display_name),
        first_name = COALESCE(p_first_name, first_name),
        last_name = COALESCE(p_last_name, last_name),
        email = COALESCE(p_email, email),
        phone_number = COALESCE(cleaned_phone, phone_number),
        date_of_birth = COALESCE(p_date_of_birth, date_of_birth),
        gender = COALESCE(p_gender, gender),
        native_language = COALESCE(p_native_language, native_language),
        address_line_1 = COALESCE(p_address_line_1, address_line_1),
        address_line_2 = COALESCE(p_address_line_2, address_line_2),
        city = COALESCE(p_city, city),
        state = COALESCE(p_state, state),
        postal_code = COALESCE(p_postal_code, postal_code),
        country = COALESCE(p_country, country),
        occupation = COALESCE(p_occupation, occupation),
        company_name = COALESCE(p_company_name, company_name),
        annual_income = COALESCE(p_annual_income, annual_income),
        profile_completion_dont_ask_again = COALESCE(p_profile_completion_dont_ask_again, profile_completion_dont_ask_again),
        sync_version = new_sync_version,
        updated_at = NOW()
    WHERE firebase_uid = p_firebase_uid
    AND deleted_at IS NULL;

    GET DIAGNOSTICS update_count = ROW_COUNT;

    -- Check if update was successful
    IF update_count = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Update failed',
            'message', 'No rows were updated',
            'error_code', 'UPDATE_FAILED'
        );
    END IF;

    -- Calculate profile completion percentage (with error handling)
    BEGIN
        SELECT public.calculate_profile_completion(current_profile.id) INTO completion_percentage;
    EXCEPTION
        WHEN OTHERS THEN
            completion_percentage := 0.0;
    END;

    -- Get updated profile data for response
    SELECT * INTO current_profile
    FROM public.profiles
    WHERE firebase_uid = p_firebase_uid
    AND deleted_at IS NULL;

    -- Build success response
    result := json_build_object(
        'success', true,
        'message', 'Profile updated successfully',
        'sync_version', new_sync_version,
        'completion_percentage', completion_percentage,
        'profile', json_build_object(
            'id', current_profile.id,
            'firebase_uid', current_profile.firebase_uid,
            'email', current_profile.email,
            'display_name', current_profile.display_name,
            'phone_number', current_profile.phone_number,
            'profile_completion_dont_ask_again', current_profile.profile_completion_dont_ask_again,
            'sync_version', current_profile.sync_version,
            'updated_at', current_profile.updated_at
        )
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return failure response
        RETURN json_build_object(
            'success', false,
            'error', 'Database error occurred',
            'message', 'An unexpected error occurred while updating the profile',
            'error_code', 'DATABASE_ERROR',
            'details', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.update_user_profile TO authenticated;

-- 4. Verify the fixes
SELECT 'Phone number cleaning function updated successfully' as status;

-- Check if your phone number was fixed
SELECT 
    firebase_uid,
    phone_number,
    profile_completion_dont_ask_again,
    updated_at
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

-- Test the phone cleaning function with various inputs
SELECT 
    'Test Results:' as test_type,
    public.clean_phone_number('+************') as correct_format,
    public.clean_phone_number('+91************') as double_prefix_fixed,
    public.clean_phone_number('9582263561') as without_country_code;

SELECT 'All fixes applied successfully!' as final_status;
