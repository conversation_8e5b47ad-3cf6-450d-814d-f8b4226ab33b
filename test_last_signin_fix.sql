-- Test script to verify last_sign_in_at fix
-- This script tests the updated sync_firebase_user_data function

-- Test 1: Create a new profile and verify last_sign_in_at is set
SELECT public.sync_firebase_user_data(
    'test-uid-123',
    '<EMAIL>',
    'Test User',
    NULL,
    NULL,
    TRUE,
    'google',
    '2024-08-03T10:30:00Z'
);

-- Verify the profile was created with correct last_sign_in_at
SELECT firebase_uid, email, display_name, auth_provider, last_sign_in_at
FROM profiles 
WHERE firebase_uid = 'test-uid-123';

-- Test 2: Update existing profile and verify last_sign_in_at is updated
SELECT public.sync_firebase_user_data(
    'test-uid-123',
    '<EMAIL>',
    'Test User Updated',
    NULL,
    NULL,
    TRUE,
    'google',
    '2024-08-03T11:45:00Z'
);

-- Verify the profile was updated with new last_sign_in_at
SELECT firebase_uid, email, display_name, auth_provider, last_sign_in_at
FROM profiles 
WHERE firebase_uid = 'test-uid-123';

-- Clean up test data
DELETE FROM profiles WHERE firebase_uid = 'test-uid-123';
