# Developer Guide - Firebase Auth to Supabase Sync

This guide provides detailed technical information for developers working with the Firebase Auth to Supabase profile synchronization system.

## Architecture Overview

### System Components

```mermaid
graph TB
    A[Firebase Auth] --> B[Auth Provider]
    B --> C[Sync Service]
    C --> D[Supabase Database]
    C --> E[Queue Service]
    C --> F[Monitoring Service]
    E --> G[Error Logging]
    F --> H[Health Dashboard]
    G --> I[Analytics]
```

### Core Services

#### 1. FirebaseSupabaseSyncService
**Location**: `lib/services/firebase_supabase_sync_service.dart`

**Purpose**: Core synchronization logic between Firebase Auth and Supabase profiles.

**Key Methods**:
- `syncUserToSupabase(User)`: Main sync entry point
- `checkProfileExists(String)`: Verify profile existence
- `updateExistingProfile(User)`: Update existing profile data
- `_createNewProfile(User)`: Create new profile record
- `retrySyncWithBackoff(User)`: Retry failed sync with exponential backoff

**Error Handling**:
- Network failures → Queue for retry
- Database errors → Specific error types with retry logic
- Authentication errors → Graceful degradation
- Validation errors → Immediate failure with logging

#### 2. SyncQueueService
**Location**: `lib/services/sync_queue_service.dart`

**Purpose**: Manages offline sync queue and failed sync operations.

**Key Features**:
- Persistent queue storage using SharedPreferences
- Automatic background processing every 5 minutes
- Connectivity monitoring for queue processing
- Maximum retry attempts (5) before moving to failed syncs
- Queue size limits (100 items) to prevent memory issues

**Queue Item Structure**:
```dart
{
  'firebaseUid': String,
  'email': String?,
  'displayName': String?,
  'phoneNumber': String?,
  'photoURL': String?,
  'emailVerified': bool,
  'authProvider': String,
  'creationTime': String?,
  'addedAt': String,
  'retryCount': int,
  'lastError': String?,
  'lastAttempt': String?,
}
```

#### 3. SyncMonitoringService
**Location**: `lib/services/sync_monitoring_service.dart`

**Purpose**: Real-time health monitoring and alerting system.

**Monitoring Metrics**:
- Overall health status (Healthy/Degraded/Critical)
- Sync success rate percentage
- Queue size and failed sync counts
- Error rate calculations
- Response time measurements

**Alert Conditions**:
- Queue size > 50 items (Warning) / > 100 items (Critical)
- Error rate > 20% (Warning) / > 50% (Critical)
- Failed syncs > 10 items (Warning) / > 25 items (Critical)
- Service degraded/critical status

#### 4. ManualSyncService
**Location**: `lib/services/manual_sync_service.dart`

**Purpose**: Manual sync operations and administrative functions.

**Capabilities**:
- Manual sync for current authenticated user
- Retry specific failed sync operations
- Batch process entire sync queue
- Administrative functions (clear failed syncs, force sync)

#### 5. ErrorLoggingService
**Location**: `lib/services/error_logging_service.dart`

**Purpose**: Comprehensive error logging and reporting.

**Features**:
- Local error storage with size limits
- Firebase Crashlytics integration
- Error statistics and analytics
- Export functionality for debugging
- Automatic cleanup of old logs

## Database Schema

### Profiles Table

```sql
CREATE TABLE profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  firebase_uid TEXT UNIQUE NOT NULL,
  email TEXT,
  display_name TEXT,
  phone_number TEXT,
  photo_url TEXT,
  is_email_verified BOOLEAN DEFAULT false,
  auth_provider TEXT NOT NULL,
  date_of_birth DATE,
  company_name TEXT,
  last_sign_in_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Sync tracking fields
  sync_status sync_status_enum DEFAULT 'pending',
  last_sync_at TIMESTAMPTZ,
  sync_error_message TEXT,
  sync_retry_count INTEGER DEFAULT 0,
  sync_version INTEGER DEFAULT 1
);
```

### Sync Status Enumeration

```sql
CREATE TYPE sync_status_enum AS ENUM (
  'pending',    -- Initial state, sync not yet attempted
  'synced',     -- Successfully synced
  'failed',     -- Sync failed, will retry
  'retrying'    -- Currently retrying sync
);
```

### RLS Policies

```sql
-- Users can only access their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (firebase_uid = auth.jwt() ->> 'sub');

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (firebase_uid = auth.jwt() ->> 'sub');

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (firebase_uid = auth.jwt() ->> 'sub');
```

## Implementation Patterns

### Result Pattern

All sync operations return a `SyncResult` object:

```dart
class SyncResult {
  final bool isSuccess;
  final SyncOperationType operationType;
  final SyncErrorType? errorType;
  final String? errorMessage;
  final String? message;
  final String? firebaseUid;
  final String? authProvider;
  final Map<String, dynamic>? data;
  final Map<String, dynamic>? additionalData;

  // Factory constructors for different result types
  factory SyncResult.success({...});
  factory SyncResult.failure({...});
  factory SyncResult.authFailure({...});
  factory SyncResult.networkFailure({...});
  factory SyncResult.databaseFailure({...});
  factory SyncResult.validationFailure({...});
}
```

### Error Types

```dart
enum SyncErrorType {
  networkError,      // Network connectivity issues
  databaseError,     // Supabase database errors
  authError,         // Firebase Auth errors
  validationError,   // Data validation failures
  timeoutError,      // Operation timeout
  unknownError,      // Unexpected errors
}
```

### Operation Types

```dart
enum SyncOperationType {
  profileCreation,   // Creating new profile
  profileUpdate,     // Updating existing profile
  dataSync,          // General sync operation
  healthCheck,       // Health monitoring
  queueProcessing,   // Queue operations
}
```

## Integration Guide

### Adding Sync to Authentication Flow

```dart
// In your auth provider
class AuthProvider extends StateNotifier<AuthState> {
  final FirebaseSupabaseSyncService _syncService = 
      FirebaseSupabaseSyncService.instance;

  Future<void> signInWithGoogle() async {
    try {
      // Perform Firebase Auth sign-in
      final credential = await GoogleSignIn().signIn();
      final authCredential = GoogleAuthProvider.credential(
        accessToken: credential?.authentication.accessToken,
        idToken: credential?.authentication.idToken,
      );
      
      final userCredential = await FirebaseAuth.instance
          .signInWithCredential(authCredential);
      
      if (userCredential.user != null) {
        // Sync to Supabase (non-blocking)
        final syncResult = await _syncService
            .syncUserToSupabase(userCredential.user!);
        
        if (!syncResult.isSuccess) {
          // Log error but don't block authentication
          print('Sync failed: ${syncResult.errorMessage}');
        }
        
        // Update auth state
        state = AuthState.authenticated(userCredential.user!);
      }
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }
}
```

### Monitoring Integration

```dart
// Initialize monitoring in main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp();
  
  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.url,
    anonKey: SupabaseConfig.anonKey,
  );
  
  // Initialize sync services
  await SyncQueueService().initialize();
  await SyncMonitoringService().initialize();
  
  runApp(MyApp());
}
```

### Health Dashboard Integration

```dart
// Add to your admin/settings screen
class SettingsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Settings')),
      body: Column(
        children: [
          // Other settings...
          
          ListTile(
            title: Text('Sync Health Dashboard'),
            subtitle: Text('Monitor sync performance'),
            trailing: Icon(Icons.health_and_safety),
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => SyncHealthDashboard(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
```

## Testing Strategy

### Unit Tests

```dart
// Example unit test
void main() {
  group('FirebaseSupabaseSyncService', () {
    late FirebaseSupabaseSyncService syncService;
    late MockSupabaseClient mockSupabase;
    late MockFirebaseAuth mockAuth;

    setUp(() {
      mockSupabase = MockSupabaseClient();
      mockAuth = MockFirebaseAuth();
      syncService = FirebaseSupabaseSyncService.instance;
    });

    test('should create new profile for new user', () async {
      // Arrange
      final mockUser = MockUser();
      when(mockUser.uid).thenReturn('test-uid');
      when(mockUser.email).thenReturn('<EMAIL>');
      
      // Act
      final result = await syncService.syncUserToSupabase(mockUser);
      
      // Assert
      expect(result.isSuccess, true);
      expect(result.operationType, SyncOperationType.profileCreation);
    });
  });
}
```

### Integration Tests

```dart
// Example integration test
void main() {
  group('Sync Integration Tests', () {
    testWidgets('should sync user after authentication', (tester) async {
      // Setup test environment
      await tester.pumpWidget(MyApp());
      
      // Simulate authentication
      await tester.tap(find.byKey(Key('google-sign-in-button')));
      await tester.pumpAndSettle();
      
      // Verify sync occurred
      // Check database state, UI updates, etc.
    });
  });
}
```

## Performance Considerations

### Optimization Strategies

1. **Batch Operations**: Process multiple sync operations together
2. **Connection Pooling**: Reuse database connections
3. **Caching**: Cache profile existence checks
4. **Background Processing**: Use isolates for heavy operations
5. **Rate Limiting**: Prevent excessive API calls

### Memory Management

- Limit queue size to prevent memory issues
- Clean up old error logs automatically
- Use weak references where appropriate
- Dispose of services properly

### Network Optimization

- Implement request deduplication
- Use compression for large payloads
- Implement proper timeout handling
- Cache network responses when appropriate

## Security Considerations

### Data Protection

- Never log sensitive user data
- Encrypt local storage when possible
- Use secure communication channels
- Implement proper input validation

### Authentication Security

- Validate Firebase tokens properly
- Use RLS policies in Supabase
- Implement proper session management
- Handle token refresh gracefully

### Error Handling Security

- Don't expose internal system details in error messages
- Log security events appropriately
- Implement rate limiting for retry attempts
- Use secure error reporting channels

## Debugging and Troubleshooting

### Debug Logging

Enable comprehensive logging:

```dart
// In debug mode
if (kDebugMode) {
  print('Sync operation started for user: ${user.uid}');
  print('Profile exists: $profileExists');
  print('Sync result: ${result.toJson()}');
}
```

### Common Issues

1. **Sync Failures**
   - Check network connectivity
   - Verify Supabase configuration
   - Review RLS policies
   - Check Firebase Auth status

2. **Performance Issues**
   - Monitor queue size
   - Check retry frequency
   - Review error rates
   - Analyze response times

3. **Data Inconsistencies**
   - Verify sync status in database
   - Check for concurrent operations
   - Review error logs
   - Validate data mapping

### Monitoring Tools

- Use the Sync Health Dashboard for real-time monitoring
- Export error logs for detailed analysis
- Monitor Firebase Analytics events
- Use Crashlytics for crash reporting

## Best Practices

### Code Organization

- Keep services focused and single-purpose
- Use dependency injection for testability
- Implement proper error boundaries
- Follow Flutter/Dart style guidelines

### Error Handling

- Always handle errors gracefully
- Provide meaningful error messages
- Log errors with sufficient context
- Implement proper retry logic

### Testing

- Write tests for all critical paths
- Use mocks for external dependencies
- Test error scenarios thoroughly
- Implement integration tests for end-to-end flows

### Documentation

- Keep documentation up to date
- Document all public APIs
- Provide usage examples
- Include troubleshooting guides
