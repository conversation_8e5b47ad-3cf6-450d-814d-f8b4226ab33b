import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_profile.dart';
import '../providers/profile_provider.dart';
import '../services/profile_debug_service.dart';

/// Quick test widget to verify profile update functionality
class ProfileUpdateTestWidget extends ConsumerStatefulWidget {
  const ProfileUpdateTestWidget({super.key});

  @override
  ConsumerState<ProfileUpdateTestWidget> createState() => _ProfileUpdateTestWidgetState();
}

class _ProfileUpdateTestWidgetState extends ConsumerState<ProfileUpdateTestWidget> {
  final ProfileDebugService _debugService = ProfileDebugService();
  String _testResult = '';
  bool _isTesting = false;

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(profileProvider);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🧪 Profile Update Test',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Current Profile Status
            _buildProfileStatus(profileState),
            
            const SizedBox(height: 16),
            
            // Test Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTesting ? null : _testQuickUpdate,
                    child: _isTesting
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Test Quick Update'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTesting ? null : _runFullDebug,
                    child: const Text('Full Debug'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Test Results
            if (_testResult.isNotEmpty) ...[
              const Text(
                'Test Results:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _testResult,
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProfileStatus(ProfileState profileState) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Current Profile Status:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text('Loading: ${profileState.isLoading}'),
          Text('Updating: ${profileState.isUpdating}'),
          Text('Has Profile: ${profileState.profile != null}'),
          if (profileState.profile != null) ...[
            Text('Display Name: ${profileState.profile!.displayName ?? "null"}'),
            Text('Email: ${profileState.profile!.email ?? "null"}'),
            Text('Sync Version: ${profileState.profile!.syncVersion}'),
          ],
          if (profileState.error != null)
            Text(
              'Error: ${profileState.error}',
              style: const TextStyle(color: Colors.red),
            ),
          if (profileState.successMessage != null)
            Text(
              'Success: ${profileState.successMessage}',
              style: const TextStyle(color: Colors.green),
            ),
        ],
      ),
    );
  }

  Future<void> _testQuickUpdate() async {
    setState(() {
      _isTesting = true;
      _testResult = 'Running quick update test...';
    });

    try {
      final profileState = ref.read(profileProvider);
      
      if (profileState.profile == null) {
        setState(() {
          _testResult = '❌ No profile loaded. Load profile first.';
        });
        return;
      }

      // Create a test update request
      final testName = 'Test Update ${DateTime.now().millisecondsSinceEpoch}';
      final request = ProfileUpdateRequest(
        displayName: testName,
        syncVersion: profileState.profile!.syncVersion,
      );

      // Attempt the update
      final success = await ref.read(profileProvider.notifier).updateProfile(request);

      setState(() {
        _testResult = success
            ? '✅ Quick update test PASSED\nUpdated display name to: $testName'
            : '❌ Quick update test FAILED\nCheck error messages above';
      });

    } catch (e) {
      setState(() {
        _testResult = '❌ Quick update test ERROR: $e';
      });
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }

  Future<void> _runFullDebug() async {
    setState(() {
      _isTesting = true;
      _testResult = 'Running full debug tests...';
    });

    try {
      final results = await _debugService.runDebugTests();
      
      // Create a summary
      final summary = StringBuffer();
      summary.writeln('🔍 FULL DEBUG RESULTS:');
      summary.writeln('');
      
      results.forEach((testName, result) {
        if (result is Map<String, dynamic>) {
          final status = result['status'] ?? 'UNKNOWN';
          final icon = status == 'SUCCESS' ? '✅' : 
                      status == 'FAILED' || status == 'ERROR' ? '❌' : 
                      status == 'SKIPPED' ? '⏭️' : '❓';
          
          summary.writeln('$icon $testName: $status');
          
          if (status != 'SUCCESS' && result['error'] != null) {
            summary.writeln('   Error: ${result['error']}');
          }
        }
      });
      
      setState(() {
        _testResult = summary.toString();
      });

    } catch (e) {
      setState(() {
        _testResult = '❌ Full debug ERROR: $e';
      });
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }
}

/// Extension to easily add this widget to any screen for testing
extension ProfileUpdateTestExtension on Widget {
  Widget withProfileUpdateTest() {
    return Column(
      children: [
        this,
        const ProfileUpdateTestWidget(),
      ],
    );
  }
}
