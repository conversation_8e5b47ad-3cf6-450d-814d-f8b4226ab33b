// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCaauQKzhpuI30NAGvdJJ14UgEUdviQxoI',
    appId: '1:1068028541160:web:523cbf28997bf99e9ab15d',
    messagingSenderId: '1068028541160',
    projectId: 'allaboutinsurance-c7964',
    authDomain: 'allaboutinsurance-c7964.firebaseapp.com',
    storageBucket: 'allaboutinsurance-c7964.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCaauQKzhpuI30NAGvdJJ14UgEUdviQxoI',
    appId: '1:1068028541160:android:523cbf28997bf99e9ab15d',
    messagingSenderId: '1068028541160',
    projectId: 'allaboutinsurance-c7964',
    storageBucket: 'allaboutinsurance-c7964.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCaauQKzhpuI30NAGvdJJ14UgEUdviQxoI',
    appId: '1:1068028541160:ios:523cbf28997bf99e9ab15d',
    messagingSenderId: '1068028541160',
    projectId: 'allaboutinsurance-c7964',
    storageBucket: 'allaboutinsurance-c7964.firebasestorage.app',
    iosBundleId: 'com.zstartech.aai',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCaauQKzhpuI30NAGvdJJ14UgEUdviQxoI',
    appId: '1:1068028541160:macos:523cbf28997bf99e9ab15d',
    messagingSenderId: '1068028541160',
    projectId: 'allaboutinsurance-c7964',
    storageBucket: 'allaboutinsurance-c7964.firebasestorage.app',
    iosBundleId: 'com.zstartech.aai',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCaauQKzhpuI30NAGvdJJ14UgEUdviQxoI',
    appId: '1:1068028541160:windows:523cbf28997bf99e9ab15d',
    messagingSenderId: '1068028541160',
    projectId: 'allaboutinsurance-c7964',
    storageBucket: 'allaboutinsurance-c7964.firebasestorage.app',
  );
}
