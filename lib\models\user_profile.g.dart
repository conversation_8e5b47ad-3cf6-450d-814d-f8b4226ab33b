// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserProfileImpl _$$UserProfileImplFromJson(Map<String, dynamic> json) =>
    _$UserProfileImpl(
      id: json['id'] as String,
      firebaseUid: json['firebaseUid'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      photoUrl: json['photoUrl'] as String?,
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      authProvider: json['authProvider'] as String? ?? 'email',
      role: json['role'] as String?,
      dateOfBirth: json['dateOfBirth'] == null
          ? null
          : DateTime.parse(json['dateOfBirth'] as String),
      gender: json['gender'] as String?,
      nativeLanguage: json['nativeLanguage'] as String?,
      addressLine1: json['addressLine1'] as String?,
      addressLine2: json['addressLine2'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      postalCode: json['postalCode'] as String?,
      country: json['country'] as String? ?? 'India',
      occupation: json['occupation'] as String?,
      companyName: json['companyName'] as String?,
      annualIncome: (json['annualIncome'] as num?)?.toDouble(),
      pushNotificationsEnabled:
          json['pushNotificationsEnabled'] as bool? ?? true,
      emailNotificationsEnabled:
          json['emailNotificationsEnabled'] as bool? ?? false,
      smsNotificationsEnabled: json['smsNotificationsEnabled'] as bool? ?? true,
      locationServicesEnabled: json['locationServicesEnabled'] as bool? ?? true,
      analyticsEnabled: json['analyticsEnabled'] as bool? ?? false,
      darkModeEnabled: json['darkModeEnabled'] as bool? ?? false,
      biometricEnabled: json['biometricEnabled'] as bool? ?? true,
      autoDownloadEnabled: json['autoDownloadEnabled'] as bool? ?? false,
      profileCompletionDontAskAgain:
          json['profileCompletionDontAskAgain'] as bool? ?? false,
      subscriptionPlan: json['subscriptionPlan'] as String? ?? 'Free',
      subscriptionStatus: json['subscriptionStatus'] as String? ?? 'active',
      subscriptionStartDate: json['subscriptionStartDate'] == null
          ? null
          : DateTime.parse(json['subscriptionStartDate'] as String),
      subscriptionEndDate: json['subscriptionEndDate'] == null
          ? null
          : DateTime.parse(json['subscriptionEndDate'] as String),
      profileCompletionPercentage:
          (json['profileCompletionPercentage'] as num?)?.toInt() ?? 0,
      onboardingCompleted: json['onboardingCompleted'] as bool? ?? false,
      termsAccepted: json['termsAccepted'] as bool? ?? false,
      privacyPolicyAccepted: json['privacyPolicyAccepted'] as bool? ?? false,
      syncVersion: (json['syncVersion'] as num?)?.toInt() ?? 1,
      lastSignInAt: json['lastSignInAt'] == null
          ? null
          : DateTime.parse(json['lastSignInAt'] as String),
      lastActiveAt: json['lastActiveAt'] == null
          ? null
          : DateTime.parse(json['lastActiveAt'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$$UserProfileImplToJson(
  _$UserProfileImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'firebaseUid': instance.firebaseUid,
  'email': instance.email,
  'displayName': instance.displayName,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'phoneNumber': instance.phoneNumber,
  'photoUrl': instance.photoUrl,
  'isEmailVerified': instance.isEmailVerified,
  'authProvider': instance.authProvider,
  'role': instance.role,
  'dateOfBirth': instance.dateOfBirth?.toIso8601String(),
  'gender': instance.gender,
  'nativeLanguage': instance.nativeLanguage,
  'addressLine1': instance.addressLine1,
  'addressLine2': instance.addressLine2,
  'city': instance.city,
  'state': instance.state,
  'postalCode': instance.postalCode,
  'country': instance.country,
  'occupation': instance.occupation,
  'companyName': instance.companyName,
  'annualIncome': instance.annualIncome,
  'pushNotificationsEnabled': instance.pushNotificationsEnabled,
  'emailNotificationsEnabled': instance.emailNotificationsEnabled,
  'smsNotificationsEnabled': instance.smsNotificationsEnabled,
  'locationServicesEnabled': instance.locationServicesEnabled,
  'analyticsEnabled': instance.analyticsEnabled,
  'darkModeEnabled': instance.darkModeEnabled,
  'biometricEnabled': instance.biometricEnabled,
  'autoDownloadEnabled': instance.autoDownloadEnabled,
  'profileCompletionDontAskAgain': instance.profileCompletionDontAskAgain,
  'subscriptionPlan': instance.subscriptionPlan,
  'subscriptionStatus': instance.subscriptionStatus,
  'subscriptionStartDate': instance.subscriptionStartDate?.toIso8601String(),
  'subscriptionEndDate': instance.subscriptionEndDate?.toIso8601String(),
  'profileCompletionPercentage': instance.profileCompletionPercentage,
  'onboardingCompleted': instance.onboardingCompleted,
  'termsAccepted': instance.termsAccepted,
  'privacyPolicyAccepted': instance.privacyPolicyAccepted,
  'syncVersion': instance.syncVersion,
  'lastSignInAt': instance.lastSignInAt?.toIso8601String(),
  'lastActiveAt': instance.lastActiveAt?.toIso8601String(),
  'createdAt': instance.createdAt?.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

_$ProfileUpdateRequestImpl _$$ProfileUpdateRequestImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileUpdateRequestImpl(
  displayName: json['displayName'] as String?,
  firstName: json['firstName'] as String?,
  lastName: json['lastName'] as String?,
  email: json['email'] as String?,
  phoneNumber: json['phoneNumber'] as String?,
  dateOfBirth: json['dateOfBirth'] == null
      ? null
      : DateTime.parse(json['dateOfBirth'] as String),
  gender: json['gender'] as String?,
  nativeLanguage: json['nativeLanguage'] as String?,
  addressLine1: json['addressLine1'] as String?,
  addressLine2: json['addressLine2'] as String?,
  city: json['city'] as String?,
  state: json['state'] as String?,
  postalCode: json['postalCode'] as String?,
  country: json['country'] as String?,
  occupation: json['occupation'] as String?,
  companyName: json['companyName'] as String?,
  annualIncome: (json['annualIncome'] as num?)?.toDouble(),
  profileCompletionDontAskAgain: json['profileCompletionDontAskAgain'] as bool?,
  syncVersion: (json['syncVersion'] as num?)?.toInt(),
);

Map<String, dynamic> _$$ProfileUpdateRequestImplToJson(
  _$ProfileUpdateRequestImpl instance,
) => <String, dynamic>{
  'displayName': instance.displayName,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'email': instance.email,
  'phoneNumber': instance.phoneNumber,
  'dateOfBirth': instance.dateOfBirth?.toIso8601String(),
  'gender': instance.gender,
  'nativeLanguage': instance.nativeLanguage,
  'addressLine1': instance.addressLine1,
  'addressLine2': instance.addressLine2,
  'city': instance.city,
  'state': instance.state,
  'postalCode': instance.postalCode,
  'country': instance.country,
  'occupation': instance.occupation,
  'companyName': instance.companyName,
  'annualIncome': instance.annualIncome,
  'profileCompletionDontAskAgain': instance.profileCompletionDontAskAgain,
  'syncVersion': instance.syncVersion,
};

_$ProfileUpdateSuccessImpl _$$ProfileUpdateSuccessImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileUpdateSuccessImpl(
  profile: UserProfile.fromJson(json['profile'] as Map<String, dynamic>),
  message: json['message'] as String,
  newSyncVersion: (json['newSyncVersion'] as num).toInt(),
  completionPercentage: (json['completionPercentage'] as num).toInt(),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ProfileUpdateSuccessImplToJson(
  _$ProfileUpdateSuccessImpl instance,
) => <String, dynamic>{
  'profile': instance.profile,
  'message': instance.message,
  'newSyncVersion': instance.newSyncVersion,
  'completionPercentage': instance.completionPercentage,
  'runtimeType': instance.$type,
};

_$ProfileUpdateErrorImpl _$$ProfileUpdateErrorImplFromJson(
  Map<String, dynamic> json,
) => _$ProfileUpdateErrorImpl(
  error: json['error'] as String,
  message: json['message'] as String,
  errorCode: json['errorCode'] as String?,
  currentVersion: (json['currentVersion'] as num?)?.toInt(),
  providedVersion: (json['providedVersion'] as num?)?.toInt(),
  $type: json['runtimeType'] as String?,
);

Map<String, dynamic> _$$ProfileUpdateErrorImplToJson(
  _$ProfileUpdateErrorImpl instance,
) => <String, dynamic>{
  'error': instance.error,
  'message': instance.message,
  'errorCode': instance.errorCode,
  'currentVersion': instance.currentVersion,
  'providedVersion': instance.providedVersion,
  'runtimeType': instance.$type,
};
