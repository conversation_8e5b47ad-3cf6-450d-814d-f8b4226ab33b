-- Migration: Fix last_sign_in_at tracking issue
-- Created: 2024-08-03
-- Description: Ensures last_sign_in_at is properly set during profile creation and updates

-- =============================================================================
-- ISSUE: last_sign_in_at column remains NULL even after login/logout cycles
-- ROOT CAUSE: Functions don't properly handle last_sign_in_at parameter
-- SOLUTION: Update functions to always set last_sign_in_at during any sign-in
-- =============================================================================

-- Fix 1: Update create_user_profile to accept and set last_sign_in_at
CREATE OR REPLACE FUNCTION public.create_user_profile(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_auth_provider TEXT DEFAULT 'email',
    p_is_email_verified BOOLEAN DEFAULT FALSE,
    p_last_sign_in_at TEXT DEFAULT NULL  -- Add this parameter
)
RETURNS UUID AS $$
DECLARE
    new_profile_id UUID;
    cleaned_phone TEXT;
    sign_in_timestamp TIMESTAMPTZ;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RAISE EXCEPTION 'Firebase UID is required and cannot be empty';
    END IF;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Parse last_sign_in_at or use current time
    IF p_last_sign_in_at IS NOT NULL AND p_last_sign_in_at != '' THEN
        sign_in_timestamp := p_last_sign_in_at::TIMESTAMPTZ;
    ELSE
        sign_in_timestamp := NOW();
    END IF;

    -- Insert new profile with all fields including last_sign_in_at
    INSERT INTO public.profiles (
        firebase_uid,
        email,
        display_name,
        phone_number,
        photo_url,
        auth_provider,
        is_email_verified,
        last_sign_in_at,  -- Include this field
        -- role_id intentionally omitted - will remain NULL for manual assignment
        -- App preferences with sensible defaults
        push_notifications_enabled,
        email_notifications_enabled,
        location_services_enabled,
        analytics_enabled,
        dark_mode_enabled,
        biometric_enabled,
        auto_download_enabled,
        profile_completion_dont_ask_again,
        -- Subscription defaults
        subscription_plan,
        subscription_status,
        subscription_start_date
    ) VALUES (
        p_firebase_uid,
        p_email,
        p_display_name,
        cleaned_phone,
        p_photo_url,
        p_auth_provider,
        p_is_email_verified,
        sign_in_timestamp,  -- Set the timestamp
        -- role_id omitted - remains NULL
        -- Default app preferences
        TRUE,  -- push_notifications_enabled
        FALSE, -- email_notifications_enabled
        TRUE,  -- location_services_enabled
        FALSE, -- analytics_enabled
        FALSE, -- dark_mode_enabled
        TRUE,  -- biometric_enabled
        FALSE, -- auto_download_enabled
        FALSE, -- profile_completion_dont_ask_again
        -- Default subscription
        'Free',
        'active',
        NOW()
    ) RETURNING id INTO new_profile_id;

    RETURN new_profile_id;

EXCEPTION
    WHEN unique_violation THEN
        -- Profile already exists, update last_sign_in_at and return existing profile ID
        UPDATE public.profiles SET
            last_sign_in_at = sign_in_timestamp,
            updated_at = NOW()
        WHERE firebase_uid = p_firebase_uid;
        
        SELECT id INTO new_profile_id
        FROM public.profiles
        WHERE firebase_uid = p_firebase_uid;
        RETURN new_profile_id;
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE EXCEPTION 'Failed to create profile for user %: %', p_firebase_uid, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix 2: Update sync_firebase_user_data to properly handle last_sign_in_at
CREATE OR REPLACE FUNCTION public.sync_firebase_user_data(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_is_email_verified BOOLEAN DEFAULT NULL,
    p_auth_provider TEXT DEFAULT NULL,
    p_last_sign_in_at TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    profile_exists BOOLEAN;
    cleaned_phone TEXT;
    sign_in_timestamp TIMESTAMPTZ;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RAISE EXCEPTION 'Firebase UID is required and cannot be empty';
    END IF;

    -- Check if profile exists
    SELECT EXISTS(
        SELECT 1 FROM public.profiles 
        WHERE firebase_uid = p_firebase_uid 
        AND deleted_at IS NULL
    ) INTO profile_exists;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Parse timestamp or use current time
    IF p_last_sign_in_at IS NOT NULL AND p_last_sign_in_at != '' THEN
        sign_in_timestamp := p_last_sign_in_at::TIMESTAMPTZ;
    ELSE
        sign_in_timestamp := NOW();
    END IF;

    IF profile_exists THEN
        -- Update existing profile with Firebase data
        UPDATE public.profiles SET
            email = COALESCE(p_email, email),
            display_name = COALESCE(p_display_name, display_name),
            phone_number = COALESCE(cleaned_phone, phone_number),
            photo_url = COALESCE(p_photo_url, photo_url),
            is_email_verified = COALESCE(p_is_email_verified, is_email_verified),
            auth_provider = COALESCE(p_auth_provider, auth_provider),
            last_sign_in_at = sign_in_timestamp,  -- Always update this
            updated_at = NOW()
        WHERE firebase_uid = p_firebase_uid;
    ELSE
        -- Create new profile using updated function (now includes last_sign_in_at)
        PERFORM public.create_user_profile(
            p_firebase_uid,
            p_email,
            p_display_name,
            cleaned_phone,
            p_photo_url,
            COALESCE(p_auth_provider, 'email'),
            COALESCE(p_is_email_verified, FALSE),
            p_last_sign_in_at  -- Pass the timestamp parameter
        );
    END IF;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to sync user data for %: %', p_firebase_uid, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix 3: Update handle_user_signup to also handle last_sign_in_at properly
CREATE OR REPLACE FUNCTION public.handle_user_signup(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_auth_provider TEXT DEFAULT 'email',
    p_is_email_verified BOOLEAN DEFAULT FALSE,
    p_last_sign_in_at TEXT DEFAULT NULL  -- Add this parameter
)
RETURNS JSON AS $$
DECLARE
    profile_id UUID;
    result JSON;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Firebase UID is required',
            'message', 'Invalid user data provided'
        );
    END IF;

    -- Create or update profile using the updated function
    SELECT public.create_user_profile(
        p_firebase_uid,
        p_email,
        p_display_name,
        p_phone_number,
        p_photo_url,
        p_auth_provider,
        p_is_email_verified,
        p_last_sign_in_at  -- Pass the timestamp
    ) INTO profile_id;

    -- Build success response
    result := json_build_object(
        'success', true,
        'profile_id', profile_id,
        'firebase_uid', p_firebase_uid,
        'email', p_email,
        'display_name', p_display_name,
        'message', 'Profile created successfully'
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Return error response
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to create user profile'
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- VERIFICATION: Test the fix
-- =============================================================================

-- Test script to verify last_sign_in_at is working
DO $$
DECLARE
    test_uid TEXT := 'test-signin-fix-' || extract(epoch from now())::text;
    test_timestamp TEXT := '2024-08-03T10:30:00Z';
    result_profile RECORD;
BEGIN
    RAISE NOTICE 'Testing last_sign_in_at fix...';
    
    -- Test 1: Create new profile with specific timestamp
    PERFORM public.sync_firebase_user_data(
        test_uid,
        '<EMAIL>',
        'Test SignIn User',
        NULL,
        NULL,
        TRUE,
        'google',
        test_timestamp
    );
    
    -- Check if last_sign_in_at was set correctly
    SELECT * INTO result_profile
    FROM profiles 
    WHERE firebase_uid = test_uid;
    
    IF result_profile.last_sign_in_at IS NOT NULL THEN
        RAISE NOTICE 'SUCCESS: last_sign_in_at set to %', result_profile.last_sign_in_at;
    ELSE
        RAISE WARNING 'FAILED: last_sign_in_at is still NULL';
    END IF;
    
    -- Test 2: Update existing profile (simulate another login)
    PERFORM public.sync_firebase_user_data(
        test_uid,
        '<EMAIL>',
        'Test SignIn User Updated',
        NULL,
        NULL,
        TRUE,
        'google',
        NOW()::TEXT
    );
    
    -- Check if last_sign_in_at was updated
    SELECT * INTO result_profile
    FROM profiles 
    WHERE firebase_uid = test_uid;
    
    IF result_profile.last_sign_in_at > test_timestamp::TIMESTAMPTZ THEN
        RAISE NOTICE 'SUCCESS: last_sign_in_at updated to %', result_profile.last_sign_in_at;
    ELSE
        RAISE WARNING 'FAILED: last_sign_in_at was not updated properly';
    END IF;
    
    -- Clean up test data
    DELETE FROM profiles WHERE firebase_uid = test_uid;
    RAISE NOTICE 'Test completed and cleaned up successfully';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Test failed with error: %', SQLERRM;
END $$;
