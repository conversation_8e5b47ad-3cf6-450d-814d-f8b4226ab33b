// Mocks generated by Mocki<PERSON> 5.4.6 from annotations
// in aai/test/providers/profile_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:aai/models/user_profile.dart' as _i7;
import 'package:aai/services/profile_update_service.dart' as _i5;
import 'package:aai/utils/result.dart' as _i2;
import 'package:firebase_auth/firebase_auth.dart' as _i4;
import 'package:firebase_core/firebase_core.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeResult_0<T> extends _i1.SmartFake implements _i2.Result<T> {
  _FakeResult_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeFirebaseApp_1 extends _i1.SmartFake implements _i3.FirebaseApp {
  _FakeFirebaseApp_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeActionCodeInfo_2 extends _i1.SmartFake
    implements _i4.ActionCodeInfo {
  _FakeActionCodeInfo_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserCredential_3 extends _i1.SmartFake
    implements _i4.UserCredential {
  _FakeUserCredential_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeConfirmationResult_4 extends _i1.SmartFake
    implements _i4.ConfirmationResult {
  _FakeConfirmationResult_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserMetadata_5 extends _i1.SmartFake implements _i4.UserMetadata {
  _FakeUserMetadata_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeMultiFactor_6 extends _i1.SmartFake implements _i4.MultiFactor {
  _FakeMultiFactor_6(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeIdTokenResult_7 extends _i1.SmartFake implements _i4.IdTokenResult {
  _FakeIdTokenResult_7(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUser_8 extends _i1.SmartFake implements _i4.User {
  _FakeUser_8(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ProfileUpdateService].
///
/// See the documentation for Mockito's code generation for more information.
class MockProfileUpdateService extends _i1.Mock
    implements _i5.ProfileUpdateService {
  MockProfileUpdateService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i2.Result<_i7.ProfileUpdateResult>> updateProfile(
    _i7.ProfileUpdateRequest? request, {
    Duration? timeout = const Duration(seconds: 30),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [request], {#timeout: timeout}),
            returnValue: _i6.Future<_i2.Result<_i7.ProfileUpdateResult>>.value(
              _FakeResult_0<_i7.ProfileUpdateResult>(
                this,
                Invocation.method(
                  #updateProfile,
                  [request],
                  {#timeout: timeout},
                ),
              ),
            ),
          )
          as _i6.Future<_i2.Result<_i7.ProfileUpdateResult>>);

  @override
  _i6.Future<_i2.Result<_i7.UserProfile>> getProfile({
    Duration? timeout = const Duration(seconds: 15),
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getProfile, [], {#timeout: timeout}),
            returnValue: _i6.Future<_i2.Result<_i7.UserProfile>>.value(
              _FakeResult_0<_i7.UserProfile>(
                this,
                Invocation.method(#getProfile, [], {#timeout: timeout}),
              ),
            ),
          )
          as _i6.Future<_i2.Result<_i7.UserProfile>>);

  @override
  _i6.Future<_i2.Result<void>> updateLastActive() =>
      (super.noSuchMethod(
            Invocation.method(#updateLastActive, []),
            returnValue: _i6.Future<_i2.Result<void>>.value(
              _FakeResult_0<void>(
                this,
                Invocation.method(#updateLastActive, []),
              ),
            ),
          )
          as _i6.Future<_i2.Result<void>>);

  @override
  bool isValidEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#isValidEmail, [email]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidPhoneNumber(String? phoneNumber) =>
      (super.noSuchMethod(
            Invocation.method(#isValidPhoneNumber, [phoneNumber]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidDateOfBirth(DateTime? dateOfBirth) =>
      (super.noSuchMethod(
            Invocation.method(#isValidDateOfBirth, [dateOfBirth]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidGender(String? gender) =>
      (super.noSuchMethod(
            Invocation.method(#isValidGender, [gender]),
            returnValue: false,
          )
          as bool);

  @override
  bool isValidAnnualIncome(double? income) =>
      (super.noSuchMethod(
            Invocation.method(#isValidAnnualIncome, [income]),
            returnValue: false,
          )
          as bool);
}

/// A class which mocks [FirebaseAuth].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseAuth extends _i1.Mock implements _i4.FirebaseAuth {
  MockFirebaseAuth() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.FirebaseApp get app =>
      (super.noSuchMethod(
            Invocation.getter(#app),
            returnValue: _FakeFirebaseApp_1(this, Invocation.getter(#app)),
          )
          as _i3.FirebaseApp);

  @override
  set app(_i3.FirebaseApp? _app) => super.noSuchMethod(
    Invocation.setter(#app, _app),
    returnValueForMissingStub: null,
  );

  @override
  set tenantId(String? tenantId) => super.noSuchMethod(
    Invocation.setter(#tenantId, tenantId),
    returnValueForMissingStub: null,
  );

  @override
  set customAuthDomain(String? customAuthDomain) => super.noSuchMethod(
    Invocation.setter(#customAuthDomain, customAuthDomain),
    returnValueForMissingStub: null,
  );

  @override
  Map<dynamic, dynamic> get pluginConstants =>
      (super.noSuchMethod(
            Invocation.getter(#pluginConstants),
            returnValue: <dynamic, dynamic>{},
          )
          as Map<dynamic, dynamic>);

  @override
  _i6.Future<void> useAuthEmulator(
    String? host,
    int? port, {
    bool? automaticHostMapping = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #useAuthEmulator,
              [host, port],
              {#automaticHostMapping: automaticHostMapping},
            ),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> applyActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#applyActionCode, [code]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.ActionCodeInfo> checkActionCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#checkActionCode, [code]),
            returnValue: _i6.Future<_i4.ActionCodeInfo>.value(
              _FakeActionCodeInfo_2(
                this,
                Invocation.method(#checkActionCode, [code]),
              ),
            ),
          )
          as _i6.Future<_i4.ActionCodeInfo>);

  @override
  _i6.Future<void> confirmPasswordReset({
    required String? code,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#confirmPasswordReset, [], {
              #code: code,
              #newPassword: newPassword,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.UserCredential> createUserWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createUserWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#createUserWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<List<String>> fetchSignInMethodsForEmail(String? email) =>
      (super.noSuchMethod(
            Invocation.method(#fetchSignInMethodsForEmail, [email]),
            returnValue: _i6.Future<List<String>>.value(<String>[]),
          )
          as _i6.Future<List<String>>);

  @override
  _i6.Future<_i4.UserCredential> getRedirectResult() =>
      (super.noSuchMethod(
            Invocation.method(#getRedirectResult, []),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#getRedirectResult, []),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  bool isSignInWithEmailLink(String? emailLink) =>
      (super.noSuchMethod(
            Invocation.method(#isSignInWithEmailLink, [emailLink]),
            returnValue: false,
          )
          as bool);

  @override
  _i6.Stream<_i4.User?> authStateChanges() =>
      (super.noSuchMethod(
            Invocation.method(#authStateChanges, []),
            returnValue: _i6.Stream<_i4.User?>.empty(),
          )
          as _i6.Stream<_i4.User?>);

  @override
  _i6.Stream<_i4.User?> idTokenChanges() =>
      (super.noSuchMethod(
            Invocation.method(#idTokenChanges, []),
            returnValue: _i6.Stream<_i4.User?>.empty(),
          )
          as _i6.Stream<_i4.User?>);

  @override
  _i6.Stream<_i4.User?> userChanges() =>
      (super.noSuchMethod(
            Invocation.method(#userChanges, []),
            returnValue: _i6.Stream<_i4.User?>.empty(),
          )
          as _i6.Stream<_i4.User?>);

  @override
  _i6.Future<void> sendPasswordResetEmail({
    required String? email,
    _i4.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendPasswordResetEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendSignInLinkToEmail({
    required String? email,
    required _i4.ActionCodeSettings? actionCodeSettings,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendSignInLinkToEmail, [], {
              #email: email,
              #actionCodeSettings: actionCodeSettings,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setLanguageCode(String? languageCode) =>
      (super.noSuchMethod(
            Invocation.method(#setLanguageCode, [languageCode]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setSettings({
    bool? appVerificationDisabledForTesting = false,
    String? userAccessGroup,
    String? phoneNumber,
    String? smsCode,
    bool? forceRecaptchaFlow,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setSettings, [], {
              #appVerificationDisabledForTesting:
                  appVerificationDisabledForTesting,
              #userAccessGroup: userAccessGroup,
              #phoneNumber: phoneNumber,
              #smsCode: smsCode,
              #forceRecaptchaFlow: forceRecaptchaFlow,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> setPersistence(_i4.Persistence? persistence) =>
      (super.noSuchMethod(
            Invocation.method(#setPersistence, [persistence]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.UserCredential> signInAnonymously() =>
      (super.noSuchMethod(
            Invocation.method(#signInAnonymously, []),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInAnonymously, []),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCredential, [credential]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithCredential, [credential]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithCustomToken(String? token) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithCustomToken, [token]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithCustomToken, [token]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithEmailAndPassword({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailAndPassword, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithEmailAndPassword, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithEmailLink({
    required String? email,
    required String? emailLink,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithEmailLink, [], {
              #email: email,
              #emailLink: emailLink,
            }),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithEmailLink, [], {
                  #email: email,
                  #emailLink: emailLink,
                }),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> signInWithProvider(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithProvider, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithProvider, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.ConfirmationResult> signInWithPhoneNumber(
    String? phoneNumber, [
    _i4.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i6.Future<_i4.ConfirmationResult>.value(
              _FakeConfirmationResult_4(
                this,
                Invocation.method(#signInWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i6.Future<_i4.ConfirmationResult>);

  @override
  _i6.Future<_i4.UserCredential> signInWithPopup(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithPopup, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#signInWithPopup, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> signInWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#signInWithRedirect, [provider]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> signOut() =>
      (super.noSuchMethod(
            Invocation.method(#signOut, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<String> verifyPasswordResetCode(String? code) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPasswordResetCode, [code]),
            returnValue: _i6.Future<String>.value(
              _i8.dummyValue<String>(
                this,
                Invocation.method(#verifyPasswordResetCode, [code]),
              ),
            ),
          )
          as _i6.Future<String>);

  @override
  _i6.Future<void> verifyPhoneNumber({
    String? phoneNumber,
    _i4.PhoneMultiFactorInfo? multiFactorInfo,
    required _i4.PhoneVerificationCompleted? verificationCompleted,
    required _i4.PhoneVerificationFailed? verificationFailed,
    required _i4.PhoneCodeSent? codeSent,
    required _i4.PhoneCodeAutoRetrievalTimeout? codeAutoRetrievalTimeout,
    String? autoRetrievedSmsCodeForTesting,
    Duration? timeout = const Duration(seconds: 30),
    int? forceResendingToken,
    _i4.MultiFactorSession? multiFactorSession,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#verifyPhoneNumber, [], {
              #phoneNumber: phoneNumber,
              #multiFactorInfo: multiFactorInfo,
              #verificationCompleted: verificationCompleted,
              #verificationFailed: verificationFailed,
              #codeSent: codeSent,
              #codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
              #autoRetrievedSmsCodeForTesting: autoRetrievedSmsCodeForTesting,
              #timeout: timeout,
              #forceResendingToken: forceResendingToken,
              #multiFactorSession: multiFactorSession,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> revokeTokenWithAuthorizationCode(
    String? authorizationCode,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#revokeTokenWithAuthorizationCode, [
              authorizationCode,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> initializeRecaptchaConfig() =>
      (super.noSuchMethod(
            Invocation.method(#initializeRecaptchaConfig, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}

/// A class which mocks [User].
///
/// See the documentation for Mockito's code generation for more information.
class MockUser extends _i1.Mock implements _i4.User {
  MockUser() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get emailVerified =>
      (super.noSuchMethod(Invocation.getter(#emailVerified), returnValue: false)
          as bool);

  @override
  bool get isAnonymous =>
      (super.noSuchMethod(Invocation.getter(#isAnonymous), returnValue: false)
          as bool);

  @override
  _i4.UserMetadata get metadata =>
      (super.noSuchMethod(
            Invocation.getter(#metadata),
            returnValue: _FakeUserMetadata_5(
              this,
              Invocation.getter(#metadata),
            ),
          )
          as _i4.UserMetadata);

  @override
  List<_i4.UserInfo> get providerData =>
      (super.noSuchMethod(
            Invocation.getter(#providerData),
            returnValue: <_i4.UserInfo>[],
          )
          as List<_i4.UserInfo>);

  @override
  String get uid =>
      (super.noSuchMethod(
            Invocation.getter(#uid),
            returnValue: _i8.dummyValue<String>(this, Invocation.getter(#uid)),
          )
          as String);

  @override
  _i4.MultiFactor get multiFactor =>
      (super.noSuchMethod(
            Invocation.getter(#multiFactor),
            returnValue: _FakeMultiFactor_6(
              this,
              Invocation.getter(#multiFactor),
            ),
          )
          as _i4.MultiFactor);

  @override
  _i6.Future<void> delete() =>
      (super.noSuchMethod(
            Invocation.method(#delete, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<String?> getIdToken([bool? forceRefresh = false]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdToken, [forceRefresh]),
            returnValue: _i6.Future<String?>.value(),
          )
          as _i6.Future<String?>);

  @override
  _i6.Future<_i4.IdTokenResult> getIdTokenResult([
    bool? forceRefresh = false,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#getIdTokenResult, [forceRefresh]),
            returnValue: _i6.Future<_i4.IdTokenResult>.value(
              _FakeIdTokenResult_7(
                this,
                Invocation.method(#getIdTokenResult, [forceRefresh]),
              ),
            ),
          )
          as _i6.Future<_i4.IdTokenResult>);

  @override
  _i6.Future<_i4.UserCredential> linkWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithCredential, [credential]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#linkWithCredential, [credential]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> linkWithProvider(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithProvider, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#linkWithProvider, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> reauthenticateWithProvider(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithProvider, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#reauthenticateWithProvider, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<_i4.UserCredential> reauthenticateWithPopup(
    _i4.AuthProvider? provider,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithPopup, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#reauthenticateWithPopup, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> reauthenticateWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithRedirect, [provider]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.UserCredential> linkWithPopup(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPopup, [provider]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#linkWithPopup, [provider]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> linkWithRedirect(_i4.AuthProvider? provider) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithRedirect, [provider]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.ConfirmationResult> linkWithPhoneNumber(
    String? phoneNumber, [
    _i4.RecaptchaVerifier? verifier,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#linkWithPhoneNumber, [phoneNumber, verifier]),
            returnValue: _i6.Future<_i4.ConfirmationResult>.value(
              _FakeConfirmationResult_4(
                this,
                Invocation.method(#linkWithPhoneNumber, [
                  phoneNumber,
                  verifier,
                ]),
              ),
            ),
          )
          as _i6.Future<_i4.ConfirmationResult>);

  @override
  _i6.Future<_i4.UserCredential> reauthenticateWithCredential(
    _i4.AuthCredential? credential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#reauthenticateWithCredential, [credential]),
            returnValue: _i6.Future<_i4.UserCredential>.value(
              _FakeUserCredential_3(
                this,
                Invocation.method(#reauthenticateWithCredential, [credential]),
              ),
            ),
          )
          as _i6.Future<_i4.UserCredential>);

  @override
  _i6.Future<void> reload() =>
      (super.noSuchMethod(
            Invocation.method(#reload, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> sendEmailVerification([
    _i4.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, [actionCodeSettings]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i4.User> unlink(String? providerId) =>
      (super.noSuchMethod(
            Invocation.method(#unlink, [providerId]),
            returnValue: _i6.Future<_i4.User>.value(
              _FakeUser_8(this, Invocation.method(#unlink, [providerId])),
            ),
          )
          as _i6.Future<_i4.User>);

  @override
  _i6.Future<void> updateEmail(String? newEmail) =>
      (super.noSuchMethod(
            Invocation.method(#updateEmail, [newEmail]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updatePassword(String? newPassword) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [newPassword]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updatePhoneNumber(
    _i4.PhoneAuthCredential? phoneCredential,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhoneNumber, [phoneCredential]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updateDisplayName(String? displayName) =>
      (super.noSuchMethod(
            Invocation.method(#updateDisplayName, [displayName]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updatePhotoURL(String? photoURL) =>
      (super.noSuchMethod(
            Invocation.method(#updatePhotoURL, [photoURL]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> updateProfile({String? displayName, String? photoURL}) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #photoURL: photoURL,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> verifyBeforeUpdateEmail(
    String? newEmail, [
    _i4.ActionCodeSettings? actionCodeSettings,
  ]) =>
      (super.noSuchMethod(
            Invocation.method(#verifyBeforeUpdateEmail, [
              newEmail,
              actionCodeSettings,
            ]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);
}
