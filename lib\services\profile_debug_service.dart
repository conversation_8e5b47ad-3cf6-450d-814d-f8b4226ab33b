import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_profile.dart';
import '../services/profile_update_service.dart';

/// Debug service to test profile update functionality
class ProfileDebugService {
  static final ProfileDebugService _instance = ProfileDebugService._internal();
  factory ProfileDebugService() => _instance;
  ProfileDebugService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final ProfileUpdateService _profileService = ProfileUpdateService();

  /// Comprehensive debug test for profile update functionality
  Future<Map<String, dynamic>> runDebugTests() async {
    final results = <String, dynamic>{};
    
    print('🔍 Starting Profile Update Debug Tests...');
    
    // Test 1: Check Authentication
    results['auth'] = await _testAuthentication();
    
    // Test 2: Check Supabase Connection
    results['supabase_connection'] = await _testSupabaseConnection();
    
    // Test 3: Test Database Functions
    results['database_functions'] = await _testDatabaseFunctions();
    
    // Test 4: Test Profile Service
    results['profile_service'] = await _testProfileService();
    
    // Test 5: Test Full Update Flow
    results['update_flow'] = await _testUpdateFlow();
    
    // Test 6: Test RLS Policies
    results['rls_policies'] = await _testRLSPolicies();
    
    print('✅ Debug Tests Completed');
    return results;
  }

  /// Test 1: Authentication Status
  Future<Map<String, dynamic>> _testAuthentication() async {
    print('🔐 Testing Authentication...');
    
    try {
      final user = _auth.currentUser;
      
      if (user == null) {
        return {
          'status': 'FAILED',
          'error': 'No authenticated user found',
          'solution': 'User must be logged in via Firebase Auth'
        };
      }
      
      return {
        'status': 'SUCCESS',
        'user_id': user.uid,
        'email': user.email,
        'display_name': user.displayName,
        'email_verified': user.emailVerified,
        'provider': user.providerData.isNotEmpty ? user.providerData.first.providerId : 'unknown'
      };
    } catch (e) {
      return {
        'status': 'ERROR',
        'error': e.toString()
      };
    }
  }

  /// Test 2: Supabase Connection
  Future<Map<String, dynamic>> _testSupabaseConnection() async {
    print('🔗 Testing Supabase Connection...');
    
    try {
      // Simple query to test connection
      final response = await _supabase
          .from('profiles')
          .select('count')
          .count(CountOption.exact);
      
      return {
        'status': 'SUCCESS',
        'connection': 'OK',
        'profiles_count': response.count
      };
    } catch (e) {
      return {
        'status': 'ERROR',
        'error': e.toString(),
        'solution': 'Check Supabase configuration and internet connection'
      };
    }
  }

  /// Test 3: Database Functions
  Future<Map<String, dynamic>> _testDatabaseFunctions() async {
    print('🗄️ Testing Database Functions...');
    
    final results = <String, dynamic>{};
    
    // Test get_user_profile function
    try {
      final user = _auth.currentUser;
      if (user != null) {
        final response = await _supabase.rpc('get_user_profile', params: {
          'p_firebase_uid': user.uid
        });
        
        results['get_user_profile'] = {
          'status': 'SUCCESS',
          'response_type': response.runtimeType.toString(),
          'has_data': response != null
        };
      } else {
        results['get_user_profile'] = {
          'status': 'SKIPPED',
          'reason': 'No authenticated user'
        };
      }
    } catch (e) {
      results['get_user_profile'] = {
        'status': 'ERROR',
        'error': e.toString()
      };
    }
    
    // Test update_user_profile function (dry run)
    try {
      final user = _auth.currentUser;
      if (user != null) {
        // Test with minimal parameters
        final response = await _supabase.rpc('update_user_profile', params: {
          'p_firebase_uid': user.uid,
          'p_sync_version': 0, // Use current version
        });
        
        results['update_user_profile'] = {
          'status': 'SUCCESS',
          'response_type': response.runtimeType.toString(),
          'has_data': response != null
        };
      } else {
        results['update_user_profile'] = {
          'status': 'SKIPPED',
          'reason': 'No authenticated user'
        };
      }
    } catch (e) {
      results['update_user_profile'] = {
        'status': 'ERROR',
        'error': e.toString()
      };
    }
    
    return results;
  }

  /// Test 4: Profile Service Methods
  Future<Map<String, dynamic>> _testProfileService() async {
    print('🔧 Testing Profile Service...');
    
    final results = <String, dynamic>{};
    
    // Test validation methods
    results['validation'] = {
      'email_valid': _profileService.isValidEmail('<EMAIL>'),
      'email_invalid': !_profileService.isValidEmail('invalid-email'),
      'phone_valid': _profileService.isValidPhoneNumber('+919876543210'),
      'phone_invalid': !_profileService.isValidPhoneNumber('123'),
      'gender_valid': _profileService.isValidGender('male'),
      'gender_invalid': !_profileService.isValidGender('invalid'),
      'income_valid': _profileService.isValidAnnualIncome(50000.0),
      'income_invalid': !_profileService.isValidAnnualIncome(-1000.0),
    };
    
    // Test profile retrieval
    try {
      final result = await _profileService.getProfile();
      results['get_profile'] = {
        'status': result.isSuccess ? 'SUCCESS' : 'FAILED',
        'has_data': result.data != null,
        'error': result.errorMessage
      };
    } catch (e) {
      results['get_profile'] = {
        'status': 'ERROR',
        'error': e.toString()
      };
    }
    
    return results;
  }

  /// Test 5: Full Update Flow
  Future<Map<String, dynamic>> _testUpdateFlow() async {
    print('🔄 Testing Full Update Flow...');
    
    try {
      // Get current profile first
      final getResult = await _profileService.getProfile();
      
      if (!getResult.isSuccess) {
        return {
          'status': 'FAILED',
          'step': 'get_profile',
          'error': getResult.errorMessage
        };
      }
      
      final currentProfile = getResult.data!;
      
      // Create a test update request
      final updateRequest = ProfileUpdateRequest(
        displayName: 'Debug Test User ${DateTime.now().millisecondsSinceEpoch}',
        syncVersion: currentProfile.syncVersion,
      );
      
      // Test the update
      final updateResult = await _profileService.updateProfile(updateRequest);
      
      return {
        'status': updateResult.isSuccess ? 'SUCCESS' : 'FAILED',
        'current_version': currentProfile.syncVersion,
        'update_data': updateRequest.toSupabaseParams(_auth.currentUser!.uid),
        'result': updateResult.isSuccess ? 'Profile updated successfully' : updateResult.errorMessage
      };
      
    } catch (e) {
      return {
        'status': 'ERROR',
        'error': e.toString()
      };
    }
  }

  /// Test 6: RLS Policies
  Future<Map<String, dynamic>> _testRLSPolicies() async {
    print('🔒 Testing RLS Policies...');
    
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {
          'status': 'SKIPPED',
          'reason': 'No authenticated user'
        };
      }
      
      // Test direct table access (should work with RLS)
      final response = await _supabase
          .from('profiles')
          .select('id, firebase_uid, email, display_name')
          .eq('firebase_uid', user.uid)
          .maybeSingle();
      
      return {
        'status': 'SUCCESS',
        'can_access_own_profile': response != null,
        'profile_exists': response != null,
        'firebase_uid_match': response?['firebase_uid'] == user.uid
      };
      
    } catch (e) {
      return {
        'status': 'ERROR',
        'error': e.toString(),
        'possible_cause': 'RLS policies may be blocking access'
      };
    }
  }

  /// Print debug results in a readable format
  void printDebugResults(Map<String, dynamic> results) {
    print('\n' + '=' * 60);
    print('🔍 PROFILE UPDATE DEBUG RESULTS');
    print('=' * 60);
    
    results.forEach((testName, result) {
      print('\n📋 ${testName.toUpperCase()}:');
      if (result is Map) {
        result.forEach((key, value) {
          print('   $key: $value');
        });
      } else {
        print('   $result');
      }
    });
    
    print('\n' + '=' * 60);
    print('🎯 NEXT STEPS:');
    print('1. Check any FAILED or ERROR statuses above');
    print('2. Ensure complete_profiles_setup.sql is deployed');
    print('3. Verify user is authenticated via Firebase');
    print('4. Check Supabase RLS policies');
    print('=' * 60 + '\n');
  }
}
