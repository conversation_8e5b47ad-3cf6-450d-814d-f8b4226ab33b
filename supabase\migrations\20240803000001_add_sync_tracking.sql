-- Migration: Add sync tracking fields to profiles table
-- Created: 2024-08-03
-- Description: Adds fields to track Firebase Auth to Supabase profile synchronization

-- Create sync status enum type
CREATE TYPE sync_status AS ENUM ('pending', 'success', 'failed', 'retry');

-- Add sync tracking columns to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS last_sync_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS sync_status sync_status DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS sync_error_message TEXT,
ADD COLUMN IF NOT EXISTS sync_retry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS sync_version INTEGER DEFAULT 1;

-- Add indexes for efficient sync status queries
CREATE INDEX IF NOT EXISTS idx_profiles_sync_status ON profiles(sync_status);
CREATE INDEX IF NOT EXISTS idx_profiles_last_sync_at ON profiles(last_sync_at);
CREATE INDEX IF NOT EXISTS idx_profiles_firebase_uid_sync ON profiles(firebase_uid, sync_status);

-- Add comments for documentation
COMMENT ON COLUMN profiles.last_sync_at IS 'Timestamp of the last successful sync from Firebase Auth';
COMMENT ON COLUMN profiles.sync_status IS 'Current sync status: pending, success, failed, retry';
COMMENT ON COLUMN profiles.sync_error_message IS 'Error message from the last failed sync attempt';
COMMENT ON COLUMN profiles.sync_retry_count IS 'Number of sync retry attempts';
COMMENT ON COLUMN profiles.sync_version IS 'Version number for sync conflict resolution';

-- Function to update sync status
CREATE OR REPLACE FUNCTION update_sync_status(
  p_firebase_uid TEXT,
  p_status sync_status,
  p_error_message TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE profiles 
  SET 
    sync_status = p_status,
    last_sync_at = CASE WHEN p_status = 'success' THEN NOW() ELSE last_sync_at END,
    sync_error_message = p_error_message,
    sync_retry_count = CASE 
      WHEN p_status = 'retry' THEN sync_retry_count + 1 
      WHEN p_status = 'success' THEN 0 
      ELSE sync_retry_count 
    END,
    updated_at = NOW()
  WHERE firebase_uid = p_firebase_uid;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Profile not found for firebase_uid: %', p_firebase_uid;
  END IF;
END;
$$;

-- Function to get profiles needing sync retry
CREATE OR REPLACE FUNCTION get_profiles_needing_retry(
  p_max_retry_count INTEGER DEFAULT 3,
  p_retry_delay_minutes INTEGER DEFAULT 5
)
RETURNS TABLE(
  firebase_uid TEXT,
  email TEXT,
  sync_retry_count INTEGER,
  last_sync_at TIMESTAMPTZ,
  sync_error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.firebase_uid,
    p.email,
    p.sync_retry_count,
    p.last_sync_at,
    p.sync_error_message
  FROM profiles p
  WHERE 
    p.sync_status IN ('failed', 'retry')
    AND p.sync_retry_count < p_max_retry_count
    AND (
      p.last_sync_at IS NULL 
      OR p.last_sync_at < NOW() - INTERVAL '1 minute' * p_retry_delay_minutes
    )
  ORDER BY p.sync_retry_count ASC, p.last_sync_at ASC;
END;
$$;

-- Function to get sync statistics
CREATE OR REPLACE FUNCTION get_sync_statistics()
RETURNS TABLE(
  total_profiles BIGINT,
  successful_syncs BIGINT,
  failed_syncs BIGINT,
  pending_syncs BIGINT,
  retry_syncs BIGINT,
  avg_retry_count NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_profiles,
    COUNT(*) FILTER (WHERE sync_status = 'success') as successful_syncs,
    COUNT(*) FILTER (WHERE sync_status = 'failed') as failed_syncs,
    COUNT(*) FILTER (WHERE sync_status = 'pending') as pending_syncs,
    COUNT(*) FILTER (WHERE sync_status = 'retry') as retry_syncs,
    AVG(sync_retry_count) as avg_retry_count
  FROM profiles
  WHERE firebase_uid IS NOT NULL;
END;
$$;

-- RLS policies for sync tracking fields
-- Allow users to read their own sync status
CREATE POLICY "Users can read own sync status" ON profiles
  FOR SELECT USING (firebase_uid = (auth.jwt() ->> 'sub'));

-- Allow service role to update sync status
CREATE POLICY "Service can update sync status" ON profiles
  FOR UPDATE USING (auth.role() = 'service_role');

-- Grant necessary permissions
GRANT USAGE ON TYPE sync_status TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION update_sync_status(TEXT, sync_status, TEXT) TO service_role;
GRANT EXECUTE ON FUNCTION get_profiles_needing_retry(INTEGER, INTEGER) TO service_role;
GRANT EXECUTE ON FUNCTION get_sync_statistics() TO service_role;

-- Update existing create_user_profile function to include sync tracking
CREATE OR REPLACE FUNCTION public.create_user_profile(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_auth_provider TEXT DEFAULT 'email',
    p_is_email_verified BOOLEAN DEFAULT FALSE
)
RETURNS UUID AS $$
DECLARE
    new_profile_id UUID;
    cleaned_phone TEXT;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RAISE EXCEPTION 'Firebase UID is required and cannot be empty';
    END IF;

    -- No automatic role assignment - roles will be assigned manually through admin interface

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Insert new profile with default values and sync tracking
    INSERT INTO public.profiles (
        firebase_uid,
        email,
        display_name,
        phone_number,
        photo_url,
        auth_provider,
        is_email_verified,
        -- role_id intentionally omitted - will remain NULL for manual assignment
        -- Sync tracking fields
        sync_status,
        last_sync_at,
        sync_retry_count,
        sync_version,
        -- App preferences with sensible defaults
        push_notifications_enabled,
        email_notifications_enabled,
        location_services_enabled,
        analytics_enabled,
        dark_mode_enabled,
        biometric_enabled,
        auto_download_enabled,
        profile_completion_dont_ask_again,
        -- Subscription defaults
        subscription_plan,
        subscription_status,
        subscription_start_date
    ) VALUES (
        p_firebase_uid,
        p_email,
        p_display_name,
        cleaned_phone,
        p_photo_url,
        p_auth_provider,
        p_is_email_verified,
        -- role_id omitted - remains NULL
        -- Sync tracking values
        'success',
        NOW(),
        0,
        1,
        -- Default app preferences
        TRUE,  -- push_notifications_enabled
        FALSE, -- email_notifications_enabled
        TRUE,  -- location_services_enabled
        FALSE, -- analytics_enabled
        FALSE, -- dark_mode_enabled
        TRUE,  -- biometric_enabled
        FALSE, -- auto_download_enabled
        FALSE, -- profile_completion_dont_ask_again
        -- Default subscription
        'Free',
        'active',
        NOW()
    ) RETURNING id INTO new_profile_id;

    RETURN new_profile_id;

EXCEPTION
    WHEN unique_violation THEN
        -- Profile already exists, update sync status and return existing profile ID
        UPDATE public.profiles
        SET
            sync_status = 'success',
            last_sync_at = NOW(),
            updated_at = NOW()
        WHERE firebase_uid = p_firebase_uid
        RETURNING id INTO new_profile_id;

        RETURN new_profile_id;
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE EXCEPTION 'Failed to create profile for user %: %', p_firebase_uid, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert initial sync status for existing profiles
UPDATE profiles
SET
  sync_status = 'success',
  last_sync_at = created_at,
  sync_retry_count = 0,
  sync_version = 1
WHERE firebase_uid IS NOT NULL AND sync_status IS NULL;

-- Add trigger to automatically set sync_version on updates
CREATE OR REPLACE FUNCTION increment_sync_version()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.sync_version = OLD.sync_version + 1;
  RETURN NEW;
END;
$$;

CREATE TRIGGER trigger_increment_sync_version
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  WHEN (OLD.sync_version IS DISTINCT FROM NEW.sync_version OR NEW.sync_version IS NULL)
  EXECUTE FUNCTION increment_sync_version();
