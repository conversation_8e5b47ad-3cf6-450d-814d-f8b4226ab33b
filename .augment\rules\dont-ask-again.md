I/VRI[MainActivity]@b6d91ed( 6117): call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@b6d91ed
I/flutter ( 6117): 🔄 EnhancedProfileScreen: Starting save profile...
I/flutter ( 6117): 📋 EnhancedProfileScreen: Getting current profile from provider...
I/flutter ( 6117): ✅ EnhancedProfileScreen: Current profile found, sync version: 3
I/flutter ( 6117): 📋 EnhancedProfileScreen: Update request created:
I/flutter ( 6117):    Display Name: <PERSON><PERSON><PERSON> Al
I/flutter ( 6117):    Email: <EMAIL>
I/flutter ( 6117):    Phone: **********
I/flutter ( 6117):    Date of Birth: null
I/flutter ( 6117):    Language: null
I/flutter ( 6117):    Company: null
I/flutter ( 6117):    Designation: null
I/flutter ( 6117):    State: null
I/flutter ( 6117):    Sync Version: 3
I/flutter ( 6117): 🔄 EnhancedProfileScreen: Calling provider updateProfile...
I/flutter ( 6117): 🔄 ProfileProvider: Starting updateProfile...
I/flutter ( 6117): 📋 ProfileProvider: Field name: null
I/flutter ( 6117): 📋 ProfileProvider: Optimistic update: true
I/flutter ( 6117): 🔄 ProfileProvider: Applying optimistic update...
I/flutter ( 6117): 📋 ProfileProvider: Current sync version: 3
I/flutter ( 6117): 📋 ProfileProvider: Request sync version: 3
I/flutter ( 6117): 📋 ProfileProvider: Optimistic sync version: 4
I/flutter ( 6117): 🔄 EnhancedProfileScreen: Profile state changed, updating UI...
I/flutter ( 6117): 🔗 ProfileProvider: Calling ProfileUpdateService...
I/flutter ( 6117): 🔄 ProfileUpdateService: Starting profile update...
I/flutter ( 6117): ✅ ProfileUpdateService: User authenticated - UID: JGAk3O4RbVR73qhin1zbXmh8NZw2  
I/flutter ( 6117): 📧 ProfileUpdateService: User email: <EMAIL>
I/flutter ( 6117): ✅ ProfileUpdateService: Request validation passed
I/flutter ( 6117): 📋 ProfileUpdateService: Update parameters: {p_firebase_uid: JGAk3O4RbVR73qhin1zbXmh8NZw2, p_display_name: Zeeshan Al, p_email: <EMAIL>, p_phone_number: **********, p_sync_version: 3}
I/flutter ( 6117): 🔗 ProfileUpdateService: Calling update_user_profile RPC...
I/flutter ( 6117): 🔄 EnhancedProfileScreen: Loading user data...
I/flutter ( 6117): ✅ EnhancedProfileScreen: Loading data from ProfileProvider...
I/flutter ( 6117): 📋 EnhancedProfileScreen: Profile data loaded successfully
I/ImeTracker( 6117): com.zstartech.aai:b69ae901: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT fromUser false
I/InputMethodManager_LC( 6117): hsifw() - flag : 0
I/InputMethodManager_LC( 6117): hsifw() - mService.hideSoftInput
I/flutter ( 6117): 📥 ProfileUpdateService: RPC response received: {success: true, message: Profile updated successfully, sync_version: 4, completion_percentage: 20.0, profile: {id: d412e478-c02c-405d-bb27-0bdecc92219f, firebase_uid: JGAk3O4RbVR73qhin1zbXmh8NZw2, email: <EMAIL>, display_name: Zeeshan Al, phone_number: +************, profile_completion_dont_ask_again: false, sync_version: 4, updated_at: 2025-08-07T11:27:10.975786+00:00}}
I/flutter ( 6117): 🔄 ProfileUpdateService: Parsing response...
I/flutter ( 6117): 📊 ProfileUpdateService: Response data: {success: true, message: Profile updated successfully, sync_version: 4, completion_percentage: 20.0, profile: {id: d412e478-c02c-405d-bb27-0bdecc92219f, firebase_uid: JGAk3O4RbVR73qhin1zbXmh8NZw2, email: <EMAIL>, display_name: Zeeshan Al, phone_number: +************, profile_completion_dont_ask_again: false, sync_version: 4, updated_at: 2025-08-07T11:27:10.975786+00:00}}
I/flutter ( 6117): ✅ ProfileUpdateService: Update successful!
I/flutter ( 6117): ❌ ProfileUpdateService: Unexpected error occurred: type 'Null' is not a subtype of type 'Map<String, dynamic>' in type cast
I/flutter ( 6117): ❌ ProfileUpdateService: Error type: _TypeError
I/flutter ( 6117): 📥 ProfileProvider: Service result received
I/flutter ( 6117): ❌ ProfileProvider: Service returned failure
I/flutter ( 6117): ❌ ProfileProvider: Message: An unexpected error occurred. Please try again.     
I/flutter ( 6117): ❌ ProfileProvider: Error code: Exception: UNKNOWN_ERROR
I/flutter ( 6117): 🔄 ProfileProvider: Starting loadProfile...
I/flutter ( 6117): 📥 EnhancedProfileScreen: Update result: false
I/flutter ( 6117): ❌ EnhancedProfileScreen: Update failed, showing error message
I/flutter ( 6117): 💾 ProfileProvider: Found valid cached profile (0 minutes old)
I/flutter ( 6117): 💾 ProfileProvider: Using cached profile
I/flutter ( 6117): 🔄 EnhancedProfileScreen: Profile state changed, updating UI...
D/InsetsController( 6117): hide(ime(), fromIme=true)
I/InsetsController( 6117): setRequestedVisibleTypes: visible=false, mask=ime, host=com.zstartech.aai/com.zstartech.aai.MainActivity, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:8063 android.view.ViewRootImpl$ViewRootHandler.handleMessage:7991 android.os.Handler.dispatchMessage:107 android.os.Looper.loopOnce:257 android.os.Looper.loop:342 android.app.ActivityThread.main:9638  
I/InsetsController( 6117): controlAnimationUncheckedInner: Added types=ime, animType=1, host=com.zstartech.aai/com.zstartech.aai.MainActivity, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159
I/flutter ( 6117): 🔄 EnhancedProfileScreen: Loading user data...
I/flutter ( 6117): ✅ EnhancedProfileScreen: Loading data from ProfileProvider...
I/flutter ( 6117): 📋 EnhancedProfileScreen: Profile data loaded successfully
I/InsetsController( 6117): onStateChanged: host=com.zstartech.aai/com.zstartech.aai.MainActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2340), mDisplayCutout=DisplayCutout{insets=Rect(0, 80 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(472, 0 - 608, 80), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 28.********** H 24.177777778 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=113, center=Point(113, 113)}, RoundedCorner{position=TopRight, radius=113, center=Point(967, 113)}, RoundedCorner{position=BottomRight, radius=113, center=Point(967, 2227)}, RoundedCorner{position=BottomLeft, radius=113, center=Point(113, 2227)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2340), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 80) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {a33d0000 mType=statusBars mFrame=[0,0][1080,80] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {a33d0005 mType=mandatorySystemGestures mFrame=[0,0][1080,114] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {a33d0006 mType=tappableElement mFrame=[0,0][1080,80] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,1326][1080,2340] mVisible=false mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,80] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {14dc0001 mType=navigationBars mFrame=[0,2298][1080,2340] mVisible=true mFlags=SUPPRESS_SCRIM mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {14dc0004 mType=systemGestures mFrame=[0,0][140,2340] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {14dc0005 mType=mandatorySystemGestures mFrame=[0,2250][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {14dc0006 mType=tappableElement mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {14dc0024 mType=systemGestures mFrame=[940,0][1080,2340] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null} }
I/VRI[MainActivity]@b6d91ed( 6117): handleResized, frames=ClientWindowFrames{frame=[0,0][1080,2340] display=[0,0][1080,2340] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
I/VRI[MainActivity]@b6d91ed( 6117): mWNT: t=0xb4000070ea805180 mBlastBufferQueue=0xb400006fb7c67800 fn= 9 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$afI4fXg3U3-nBZQEDQMiNy-B06s:0
I/InsetsSourceConsumer( 6117): applyRequestedVisibilityToControl: visible=true, type=navigationBars, host=com.zstartech.aai/com.zstartech.aai.MainActivity
I/VRI[MainActivity]@b6d91ed( 6117): mWNT: t=0xb4000070ea805480 mBlastBufferQueue=0xb400006fb7c67800 fn= 10 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$afI4fXg3U3-nBZQEDQMiNy-B06s:0
I/VRI[MainActivity]@b6d91ed( 6117): mWNT: t=0xb400006f832a9a80 mBlastBufferQueue=0xb400006fb7c67800 fn= 11 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$afI4fXg3U3-nBZQEDQMiNy-B06s:0
I/VRI[MainActivity]@b6d91ed( 6117): mWNT: t=0xb400006f832aa380 mBlastBufferQueue=0xb400006fb7c67800 fn= 12 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$afI4fXg3U3-nBZQEDQMiNy-B06s:0
I/VRI[MainActivity]@b6d91ed( 6117): mWNT: t=0xb400006f832aa500 mBlastBufferQueue=0xb400006fb7c67800 fn= 13 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$afI4fXg3U3-nBZQEDQMiNy-B06s:0
I/InsetsController( 6117): cancelAnimation: types=ime, animType=1, host=com.zstartech.aai/com.zstartech.aai.MainActivity, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationControlImpl.applyChangeInsets:307 android.view.InsetsController.lambda$new$3:932
I/ImeTracker( 6117): com.zstartech.aai:ec599d3e: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
I/InputMethodManager_LC( 6117): notifyImeHidden: IInputMethodManagerGlobalInvoker.hideSoftInput     
I/ImeTracker( 6117): com.zstartech.aai:b69ae901: onHidden
I/VRI[MainActivity]@b6d91ed( 6117): mWNT: t=0xb4000070ea805d80 mBlastBufferQueue=0xb400006fb7c67800 fn= 14 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SyncRtSurfaceTransactionApplier.applyTransaction:96 android.view.SyncRtSurfaceTransactionApplier.lambda$scheduleApply$0:69 android.view.SyncRtSurfaceTransactionApplier.$r8$lambda$afI4fXg3U3-nBZQEDQMiNy-B06s:0
I/InsetsController( 6117): onStateChanged: host=com.zstartech.aai/com.zstartech.aai.MainActivity, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2340), mDisplayCutout=DisplayCutout{insets=Rect(0, 80 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(472, 0 - 608, 80), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 28.********** H 24.177777778 V 0 H 0 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=113, center=Point(113, 113)}, RoundedCorner{position=TopRight, radius=113, center=Point(967, 113)}, RoundedCorner{position=BottomRight, radius=113, center=Point(967, 2227)}, RoundedCorner{position=BottomLeft, radius=113, center=Point(113, 2227)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2340), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(956, 0 - 1080, 80) rotation=0}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=0 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {a33d0000 mType=statusBars mFrame=[0,0][1080,80] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {a33d0005 mType=mandatorySystemGestures mFrame=[0,0][1080,114] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {a33d0006 mType=tappableElement mFrame=[0,0][1080,80] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {27 mType=displayCutout mFrame=[0,0][1080,80] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {14dc0001 mType=navigationBars mFrame=[0,2298][1080,2340] mVisible=true mFlags=SUPPRESS_SCRIM mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {14dc0004 mType=systemGestures mFrame=[0,0][140,2340] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {14dc0005 mType=mandatorySystemGestures mFrame=[0,2250][1080,2340] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {14dc0006 mType=tappableElement mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {14dc0024 mType=systemGestures mFrame=[940,0][1080,2340] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null} }
I/VRI[MainActivity]@b6d91ed( 6117): handleResized, frames=ClientWindowFrames{frame=[0,0][1080,2340] display=[0,0][1080,2340] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
I/VRI[MainActivity]@b6d91ed( 6117): call setFrameRateCategory for touch hint category=no preference, reason=boost timeout, vri=VRI[MainActivity]@b6d91ed




push notification, email notification, location services, analytics, biometrics, auto doenload, sms notification, 