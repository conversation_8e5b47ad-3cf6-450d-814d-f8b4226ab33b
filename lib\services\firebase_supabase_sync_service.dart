import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/sync_result.dart';
import '../utils/auth_provider_mapper.dart';
import 'analytics_service.dart';
import 'sync_queue_service.dart';

/// Service responsible for synchronizing Firebase Auth users to Supabase profiles.
///
/// This service handles the automatic creation and updating of user profiles
/// in Supabase when users authenticate through Firebase Auth. It provides
/// comprehensive error handling, retry mechanisms, and graceful degradation
/// when services are unavailable.
///
/// Key Features:
/// - Automatic profile creation for new Firebase Auth users
/// - Profile updates for existing users with changed data
/// - Exponential backoff retry mechanism for failed operations
/// - Offline queue processing when Supabase is unavailable
/// - Comprehensive error logging and analytics tracking
/// - Service health monitoring and graceful degradation
///
/// Usage:
/// ```dart
/// final syncService = FirebaseSupabaseSyncService.instance;
/// final result = await syncService.syncUserToSupabase(firebaseUser);
/// if (result.isSuccess) {
///   print('User synced successfully');
/// } else {
///   print('Sync failed: ${result.errorMessage}');
/// }
/// ```
///
/// Error Handling:
/// The service implements multiple layers of error handling:
/// 1. Network connectivity checks before operations
/// 2. Service availability monitoring with degraded mode
/// 3. Exponential backoff retry for transient failures
/// 4. Automatic queue fallback for persistent failures
/// 5. Comprehensive error logging and analytics tracking
///
/// Performance Considerations:
/// - Uses singleton pattern for efficient resource usage
/// - Implements connection pooling and request deduplication
/// - Provides timeout handling to prevent hanging operations
/// - Monitors service health to prevent cascade failures
class FirebaseSupabaseSyncService {
  /// Singleton instance of the sync service
  static FirebaseSupabaseSyncService? _instance;

  /// Gets the singleton instance, creating it if necessary
  static FirebaseSupabaseSyncService get instance => _instance ??= FirebaseSupabaseSyncService._();

  /// Private constructor to enforce singleton pattern
  FirebaseSupabaseSyncService._();

  /// Supabase client for database operations
  final SupabaseClient _supabase = Supabase.instance.client;

  /// Firebase Auth instance for user authentication
  final firebase_auth.FirebaseAuth _firebaseAuth = firebase_auth.FirebaseAuth.instance;

  /// Mapper for converting Firebase provider IDs to app-specific names
  final AuthProviderMapper _providerMapper = AuthProviderMapper.instance;

  /// Analytics service for tracking sync operations and errors
  final AnalyticsService _analytics = AnalyticsService.instance;

  /// Connectivity checker for network status monitoring
  final Connectivity _connectivity = Connectivity();

  // Graceful degradation settings

  /// Maximum time to wait for service operations before timing out
  static const Duration _serviceTimeoutDuration = Duration(seconds: 10);

  /// Maximum consecutive failures before entering degraded mode
  static const int _maxConsecutiveFailures = 3;

  /// Counter for consecutive service failures
  int _consecutiveFailures = 0;

  /// Timestamp of the last service failure
  DateTime? _lastFailureTime;

  /// Flag indicating if service is in degraded mode
  bool _isServiceDegraded = false;

  /// Check if service is available and healthy
  Future<bool> isServiceAvailable() async {
    try {
      // Check network connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        return false;
      }

      // Check if service is in degraded mode
      if (_isServiceDegraded) {
        final now = DateTime.now();
        if (_lastFailureTime != null &&
            now.difference(_lastFailureTime!).inMinutes < 5) {
          return false;
        } else {
          // Reset degraded mode after 5 minutes
          _isServiceDegraded = false;
          _consecutiveFailures = 0;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Handle service failure and update degradation state
  void _handleServiceFailure() {
    _consecutiveFailures++;
    _lastFailureTime = DateTime.now();

    if (_consecutiveFailures >= _maxConsecutiveFailures) {
      _isServiceDegraded = true;
      print('Sync service entering degraded mode after $_consecutiveFailures consecutive failures');
    }
  }

  /// Handle service success and reset degradation state
  void _handleServiceSuccess() {
    if (_consecutiveFailures > 0 || _isServiceDegraded) {
      print('Sync service recovering from degraded mode');
    }
    _consecutiveFailures = 0;
    _isServiceDegraded = false;
    _lastFailureTime = null;
  }

  /// Execute operation with timeout and graceful degradation
  Future<T> _executeWithGracefulDegradation<T>(
    Future<T> Function() operation,
    T Function() fallback,
  ) async {
    try {
      // Check if service is available
      if (!await isServiceAvailable()) {
        return fallback();
      }

      // Execute with timeout
      final result = await operation().timeout(_serviceTimeoutDuration);
      _handleServiceSuccess();
      return result;
    } catch (e) {
      _handleServiceFailure();
      return fallback();
    }
  }

  /// Check if a profile exists for the given Firebase UID
  Future<SyncResult> checkProfileExists(String firebaseUid) async {
    try {
      if (firebaseUid.isEmpty) {
        return SyncResult.validationFailure(
          operationType: SyncOperationType.dataSync,
          errorMessage: 'Firebase UID cannot be empty',
          firebaseUid: firebaseUid,
        );
      }

      final response = await _supabase
          .from('profiles')
          .select('id, firebase_uid, email, display_name, last_sign_in_at')
          .eq('firebase_uid', firebaseUid)
          .isFilter('deleted_at', null)
          .maybeSingle();

      if (response != null) {
        return SyncResult.success(
          operationType: SyncOperationType.dataSync,
          profileId: response['id'],
          firebaseUid: response['firebase_uid'],
          email: response['email'],
          displayName: response['display_name'],
          message: 'Profile exists',
          additionalData: {
            'profileExists': true,
            'lastSignInAt': response['last_sign_in_at'],
          },
        );
      } else {
        return SyncResult.success(
          operationType: SyncOperationType.dataSync,
          firebaseUid: firebaseUid,
          message: 'Profile does not exist',
          additionalData: {
            'profileExists': false,
          },
        );
      }
    } on PostgrestException catch (e) {
      return SyncResult.databaseFailure(
        operationType: SyncOperationType.dataSync,
        errorMessage: e.message,
        errorCode: e.code,
        firebaseUid: firebaseUid,
      );
    } catch (e) {
      return SyncResult.networkFailure(
        operationType: SyncOperationType.dataSync,
        errorMessage: e.toString(),
        firebaseUid: firebaseUid,
      );
    }
  }

  /// Synchronizes a Firebase Auth user to Supabase profiles table.
  ///
  /// This is the main entry point for sync operations. It handles both new
  /// profile creation and existing profile updates with comprehensive error
  /// handling and graceful degradation.
  ///
  /// The method implements a two-phase approach:
  /// 1. Attempt actual sync operation with timeout protection
  /// 2. Fall back to queue-based processing if sync fails
  ///
  /// Parameters:
  /// - [firebaseUser]: The authenticated Firebase user to sync
  ///
  /// Returns:
  /// A [SyncResult] indicating success/failure with detailed information:
  /// - Success: Profile created/updated successfully
  /// - Failure: Error details and fallback to queue processing
  ///
  /// Error Handling:
  /// - Network errors: Automatic retry with exponential backoff
  /// - Database errors: Specific error handling based on error type
  /// - Service degradation: Automatic fallback to offline queue
  /// - Validation errors: Immediate failure with detailed error message
  ///
  /// Example:
  /// ```dart
  /// final user = FirebaseAuth.instance.currentUser;
  /// if (user != null) {
  ///   final result = await syncService.syncUserToSupabase(user);
  ///   if (result.isSuccess) {
  ///     print('Profile synced: ${result.message}');
  ///   } else {
  ///     print('Sync failed: ${result.errorMessage}');
  ///   }
  /// }
  /// ```
  Future<SyncResult> syncUserToSupabase(firebase_auth.User firebaseUser) async {
    return await _executeWithGracefulDegradation<SyncResult>(
      () async => _performActualSync(firebaseUser),
      () => _handleSyncFallback(firebaseUser),
    );
  }

  /// Perform the actual sync operation
  Future<SyncResult> _performActualSync(firebase_auth.User firebaseUser) async {
    final startTime = DateTime.now();

    try {
      // First check if profile exists
      final existsResult = await checkProfileExists(firebaseUser.uid);

      if (!existsResult.isSuccess) {
        final duration = DateTime.now().difference(startTime);

        // Track sync failure
        await _analytics.trackProfileSyncFailed(
          syncResult: existsResult,
          syncDuration: duration,
        );

        return existsResult; // Return the error from checkProfileExists
      }

      final profileExists = existsResult.additionalData?['profileExists'] ?? false;
      SyncResult result;

      if (profileExists) {
        // Update existing profile
        result = await updateExistingProfile(firebaseUser);
      } else {
        // Create new profile
        result = await _createNewProfile(firebaseUser);
      }

      final duration = DateTime.now().difference(startTime);

      // Track analytics based on result
      if (result.isSuccess) {
        await _analytics.trackUserSyncedToSupabase(
          syncResult: result,
          syncDuration: duration,
        );

        if (!profileExists) {
          // Track profile creation success for new profiles
          await _analytics.trackProfileCreationSuccess(
            syncResult: result,
            syncDuration: duration,
            isNewUser: true,
          );
        }

        // Set user properties for analytics
        await _analytics.setUserProperties(
          firebaseUid: firebaseUser.uid,
          authProvider: result.authProvider,
          emailVerified: firebaseUser.emailVerified,
          hasDisplayName: firebaseUser.displayName != null,
          hasPhoneNumber: firebaseUser.phoneNumber != null,
        );
      } else {
        await _analytics.trackProfileSyncFailed(
          syncResult: result,
          syncDuration: duration,
        );
      }

      return result;
    } catch (e) {
      final duration = DateTime.now().difference(startTime);
      final errorResult = SyncResult.failure(
        operationType: SyncOperationType.dataSync,
        errorType: SyncErrorType.unknownError,
        errorMessage: e.toString(),
        message: 'Unexpected error during sync operation',
        firebaseUid: firebaseUser.uid,
      );

      // Track sync failure
      await _analytics.trackProfileSyncFailed(
        syncResult: errorResult,
        syncDuration: duration,
      );

      return errorResult;
    }
  }

  /// Handle sync fallback when service is unavailable
  SyncResult _handleSyncFallback(firebase_auth.User firebaseUser) {
    // Add to queue for later processing
    _addToQueueForLaterProcessing(firebaseUser);

    return SyncResult.success(
      operationType: SyncOperationType.dataSync,
      message: 'Sync queued for later processing (service unavailable)',
      firebaseUid: firebaseUser.uid,
      additionalData: {
        'queuedForLater': true,
        'reason': 'service_unavailable',
      },
    );
  }

  /// Add user to queue for later processing
  void _addToQueueForLaterProcessing(firebase_auth.User firebaseUser) {
    try {
      // Get the sync queue service and add the user
      final queueService = SyncQueueService();
      queueService.addToQueue(
        firebaseUid: firebaseUser.uid,
        email: firebaseUser.email ?? '',
        displayName: firebaseUser.displayName,
        phoneNumber: firebaseUser.phoneNumber,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        authProvider: _providerMapper.mapFirebaseProviderToAppProvider(firebaseUser),
        creationTime: firebaseUser.metadata.creationTime,
      );

      print('User ${firebaseUser.uid} added to sync queue for later processing');
    } catch (e) {
      print('Failed to add user to sync queue: $e');
    }
  }

  /// Update existing profile with latest Firebase data
  Future<SyncResult> updateExistingProfile(firebase_auth.User firebaseUser) async {
    try {
      final authProvider = _providerMapper.mapFirebaseProviderToAppProvider(firebaseUser);

      final updateData = {
        'email': firebaseUser.email,
        'display_name': firebaseUser.displayName,
        // CRITICAL FIX: Only update phone number if it's different and not corrupted
        // This prevents Firebase sync from overwriting correct phone numbers
        if (firebaseUser.phoneNumber != null &&
            firebaseUser.phoneNumber!.isNotEmpty &&
            !firebaseUser.phoneNumber!.contains('+91+91') && // Prevent double prefix
            !firebaseUser.phoneNumber!.contains('+9191')) // Prevent corruption
          'phone_number': firebaseUser.phoneNumber,
        'photo_url': firebaseUser.photoURL,
        'is_email_verified': firebaseUser.emailVerified,
        'auth_provider': authProvider,
        'last_sign_in_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Remove null values to avoid overwriting existing data with nulls
      updateData.removeWhere((key, value) => value == null);

      final response = await _supabase
          .from('profiles')
          .update(updateData)
          .eq('firebase_uid', firebaseUser.uid)
          .select('id, firebase_uid, email, display_name')
          .single();

      return SyncResult.success(
        operationType: SyncOperationType.profileUpdate,
        profileId: response['id'],
        firebaseUid: response['firebase_uid'],
        email: response['email'],
        displayName: response['display_name'],
        authProvider: authProvider,
        message: 'Profile updated successfully',
        additionalData: {
          'updatedFields': updateData.keys.toList(),
          'lastSignInAt': updateData['last_sign_in_at'],
        },
      );
    } on PostgrestException catch (e) {
      return SyncResult.databaseFailure(
        operationType: SyncOperationType.profileUpdate,
        errorMessage: e.message,
        errorCode: e.code,
        firebaseUid: firebaseUser.uid,
      );
    } catch (e) {
      return SyncResult.networkFailure(
        operationType: SyncOperationType.profileUpdate,
        errorMessage: e.toString(),
        firebaseUid: firebaseUser.uid,
      );
    }
  }

  /// Create new profile from Firebase user data
  Future<SyncResult> _createNewProfile(firebase_auth.User firebaseUser) async {
    try {
      final authProvider = _providerMapper.mapFirebaseProviderToAppProvider(firebaseUser);
      
      // Use the existing Supabase function for profile creation
      final userData = {
        'p_firebase_uid': firebaseUser.uid,
        'p_email': firebaseUser.email,
        'p_display_name': firebaseUser.displayName,
        'p_phone_number': firebaseUser.phoneNumber,
        'p_photo_url': firebaseUser.photoURL,
        'p_auth_provider': authProvider,
        'p_is_email_verified': firebaseUser.emailVerified,
      };

      final response = await _supabase.rpc('create_user_profile', params: userData);

      if (response != null) {
        return SyncResult.fromFirebaseUser(
          firebaseUser: firebaseUser,
          operationType: SyncOperationType.profileCreation,
          profileId: response.toString(),
          authProvider: authProvider,
          message: 'Profile created successfully',
        );
      } else {
        return SyncResult.databaseFailure(
          operationType: SyncOperationType.profileCreation,
          errorMessage: 'Failed to create profile - no response from database',
          firebaseUid: firebaseUser.uid,
        );
      }
    } on PostgrestException catch (e) {
      // Handle unique constraint violation (profile already exists)
      if (e.code == '23505' || e.message.contains('duplicate key')) {
        // Profile was created by another process, try to update instead
        return await updateExistingProfile(firebaseUser);
      }
      
      return SyncResult.databaseFailure(
        operationType: SyncOperationType.profileCreation,
        errorMessage: e.message,
        errorCode: e.code,
        firebaseUid: firebaseUser.uid,
      );
    } catch (e) {
      return SyncResult.networkFailure(
        operationType: SyncOperationType.profileCreation,
        errorMessage: e.toString(),
        firebaseUid: firebaseUser.uid,
      );
    }
  }

  /// Sync current Firebase user if authenticated
  Future<SyncResult?> syncCurrentUser() async {
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser == null) {
      return SyncResult.authFailure(
        operationType: SyncOperationType.dataSync,
        errorMessage: 'No authenticated Firebase user found',
      );
    }

    return await syncUserToSupabase(currentUser);
  }

  /// Batch sync multiple users (for admin operations)
  Future<List<SyncResult>> batchSyncUsers(List<firebase_auth.User> users) async {
    final results = <SyncResult>[];
    
    for (final user in users) {
      try {
        final result = await syncUserToSupabase(user);
        results.add(result);
        
        // Add small delay to avoid overwhelming the database
        await Future.delayed(const Duration(milliseconds: 100));
      } catch (e) {
        results.add(SyncResult.failure(
          operationType: SyncOperationType.dataSync,
          errorType: SyncErrorType.unknownError,
          errorMessage: e.toString(),
          message: 'Batch sync failed for user',
          firebaseUid: user.uid,
        ));
      }
    }
    
    return results;
  }

  /// Get sync statistics for monitoring
  Future<Map<String, dynamic>> getSyncStatistics() async {
    try {
      final response = await _supabase
          .from('profiles')
          .select('auth_provider, last_sign_in_at, created_at')
          .isFilter('deleted_at', null);

      final totalProfiles = response.length;
      final providerCounts = <String, int>{};
      final recentSyncs = response.where((profile) {
        final lastSignIn = profile['last_sign_in_at'];
        if (lastSignIn == null) return false;
        final signInDate = DateTime.parse(lastSignIn);
        return DateTime.now().difference(signInDate).inDays <= 7;
      }).length;

      for (final profile in response) {
        final provider = profile['auth_provider'] ?? 'unknown';
        providerCounts[provider] = (providerCounts[provider] ?? 0) + 1;
      }

      return {
        'totalProfiles': totalProfiles,
        'providerCounts': providerCounts,
        'recentSyncs': recentSyncs,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Retry sync for a user with exponential backoff
  Future<SyncResult> retrySyncWithBackoff(
    firebase_auth.User firebaseUser, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) async {
    int retryCount = 0;
    Duration currentDelay = initialDelay;

    while (retryCount < maxRetries) {
      try {
        // Attempt sync
        final result = await syncUserToSupabase(firebaseUser);

        if (result.isSuccess) {
          // Track successful retry - only track if this was actually a retry
          if (retryCount > 0) {
            final authProvider = _providerMapper.mapFirebaseProviderToAppProvider(firebaseUser);
            await _analytics.trackSyncRetryAttempted(
              firebaseUid: firebaseUser.uid,
              authProvider: authProvider,
              retryAttempt: retryCount + 1,
              lastErrorType: 'retry_success',
              lastErrorMessage: 'Sync succeeded on retry ${retryCount + 1}',
            );
          }

          return result;
        }

        // If this was the last retry, return the failure
        if (retryCount == maxRetries - 1) {
          final authProvider = _providerMapper.mapFirebaseProviderToAppProvider(firebaseUser);
          await _analytics.trackSyncRetryAttempted(
            firebaseUid: firebaseUser.uid,
            authProvider: authProvider,
            retryAttempt: retryCount + 1,
            lastErrorType: result.errorType.toString(),
            lastErrorMessage: result.errorMessage ?? 'Unknown error',
          );

          return result;
        }

        // Wait before next retry with exponential backoff
        await Future.delayed(currentDelay);
        retryCount++;
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * 2).clamp(
            initialDelay.inMilliseconds,
            30000, // Max 30 seconds
          ),
        );

      } catch (e) {
        // If this was the last retry, return a network failure
        if (retryCount == maxRetries - 1) {
          final authProvider = _providerMapper.mapFirebaseProviderToAppProvider(firebaseUser);
          await _analytics.trackSyncRetryAttempted(
            firebaseUid: firebaseUser.uid,
            authProvider: authProvider,
            retryAttempt: retryCount + 1,
            lastErrorType: 'network_error',
            lastErrorMessage: 'Sync failed after $maxRetries retries: ${e.toString()}',
          );

          return SyncResult.networkFailure(
            operationType: SyncOperationType.profileCreation,
            errorMessage: 'Sync failed after $maxRetries retries: ${e.toString()}',
            firebaseUid: firebaseUser.uid,
          );
        }

        // Wait before next retry
        await Future.delayed(currentDelay);
        retryCount++;
        currentDelay = Duration(
          milliseconds: (currentDelay.inMilliseconds * 2).clamp(
            initialDelay.inMilliseconds,
            30000, // Max 30 seconds
          ),
        );
      }
    }

    // This should never be reached, but just in case
    return SyncResult.networkFailure(
      operationType: SyncOperationType.profileCreation,
      errorMessage: 'Sync failed after maximum retries',
      firebaseUid: firebaseUser.uid,
    );
  }

  /// Get profiles that need retry based on sync status
  Future<List<Map<String, dynamic>>> getProfilesNeedingRetry() async {
    try {
      final response = await _supabase.rpc('get_profiles_needing_retry');
      return List<Map<String, dynamic>>.from(response ?? []);
    } catch (e) {
      print('Error getting profiles needing retry: $e');
      return [];
    }
  }

  /// Update sync status for a profile
  Future<bool> updateSyncStatus({
    required String firebaseUid,
    required String status,
    String? errorMessage,
    int? retryCount,
  }) async {
    try {
      await _supabase.rpc('update_sync_status', params: {
        'p_firebase_uid': firebaseUid,
        'p_status': status,
        'p_error_message': errorMessage,
        'p_retry_count': retryCount,
      });
      return true;
    } catch (e) {
      print('Error updating sync status: $e');
      return false;
    }
  }
}
