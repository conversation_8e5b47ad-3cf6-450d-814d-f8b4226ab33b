-- Fix for Profile Completion Preference Synchronization
-- This script updates the update_user_profile function to support the profile_completion_dont_ask_again parameter
-- Run this in the Supabase SQL Editor to fix the sync issue

-- Drop the existing function to recreate it with the new parameter
DROP FUNCTION IF EXISTS public.update_user_profile(
    TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, DECIMAL, INTEGER
);

-- Recreate the function with profile completion preference support
CREATE OR REPLACE FUNCTION public.update_user_profile(
    p_firebase_uid TEXT,
    p_display_name TEXT DEFAULT NULL,
    p_first_name TEXT DEFAULT NULL,
    p_last_name TEXT DEFAULT NULL,
    p_email TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_date_of_birth DATE DEFAULT NULL,
    p_gender TEXT DEFAULT NULL,
    p_native_language TEXT DEFAULT NULL,
    p_address_line_1 TEXT DEFAULT NULL,
    p_address_line_2 TEXT DEFAULT NULL,
    p_city TEXT DEFAULT NULL,
    p_state TEXT DEFAULT NULL,
    p_postal_code TEXT DEFAULT NULL,
    p_country TEXT DEFAULT NULL,
    p_occupation TEXT DEFAULT NULL,
    p_company_name TEXT DEFAULT NULL,
    p_annual_income DECIMAL(15,2) DEFAULT NULL,
    p_profile_completion_dont_ask_again BOOLEAN DEFAULT NULL,
    p_sync_version INTEGER DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    current_profile RECORD;
    cleaned_phone TEXT;
    update_count INTEGER;
    new_sync_version INTEGER;
    completion_percentage DECIMAL(5,2);
    result JSON;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Firebase UID is required',
            'message', 'Invalid user data provided',
            'error_code', 'INVALID_INPUT'
        );
    END IF;

    -- Get current profile with row-level locking
    SELECT * INTO current_profile
    FROM public.profiles
    WHERE firebase_uid = p_firebase_uid
    AND deleted_at IS NULL
    FOR UPDATE;

    -- Check if profile exists
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Profile not found',
            'message', 'No profile found for the specified user',
            'error_code', 'PROFILE_NOT_FOUND'
        );
    END IF;

    -- Validate sync version for optimistic locking (if provided)
    IF p_sync_version IS NOT NULL AND current_profile.sync_version != p_sync_version THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Sync conflict detected',
            'message', 'Profile has been modified by another process',
            'error_code', 'SYNC_CONFLICT',
            'current_version', current_profile.sync_version,
            'provided_version', p_sync_version
        );
    END IF;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Calculate new sync version
    new_sync_version := COALESCE(current_profile.sync_version, 0) + 1;

    -- Update profile with provided fields (only non-null values)
    UPDATE public.profiles SET
        display_name = COALESCE(p_display_name, display_name),
        first_name = COALESCE(p_first_name, first_name),
        last_name = COALESCE(p_last_name, last_name),
        email = COALESCE(p_email, email),
        phone_number = COALESCE(cleaned_phone, phone_number),
        date_of_birth = COALESCE(p_date_of_birth, date_of_birth),
        gender = COALESCE(p_gender, gender),
        native_language = COALESCE(p_native_language, native_language),
        address_line_1 = COALESCE(p_address_line_1, address_line_1),
        address_line_2 = COALESCE(p_address_line_2, address_line_2),
        city = COALESCE(p_city, city),
        state = COALESCE(p_state, state),
        postal_code = COALESCE(p_postal_code, postal_code),
        country = COALESCE(p_country, country),
        occupation = COALESCE(p_occupation, occupation),
        company_name = COALESCE(p_company_name, company_name),
        annual_income = COALESCE(p_annual_income, annual_income),
        profile_completion_dont_ask_again = COALESCE(p_profile_completion_dont_ask_again, profile_completion_dont_ask_again),
        sync_version = new_sync_version,
        updated_at = NOW()
    WHERE firebase_uid = p_firebase_uid
    AND deleted_at IS NULL;

    GET DIAGNOSTICS update_count = ROW_COUNT;

    -- Check if update was successful
    IF update_count = 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Update failed',
            'message', 'No rows were updated',
            'error_code', 'UPDATE_FAILED'
        );
    END IF;

    -- Calculate profile completion percentage (if function exists)
    BEGIN
        SELECT public.calculate_profile_completion(current_profile.id) INTO completion_percentage;
    EXCEPTION
        WHEN OTHERS THEN
            completion_percentage := 0.0;
    END;

    -- Get updated profile data for response
    SELECT * INTO current_profile
    FROM public.profiles
    WHERE firebase_uid = p_firebase_uid
    AND deleted_at IS NULL;

    -- Build success response
    result := json_build_object(
        'success', true,
        'message', 'Profile updated successfully',
        'sync_version', new_sync_version,
        'completion_percentage', completion_percentage,
        'profile', json_build_object(
            'id', current_profile.id,
            'firebase_uid', current_profile.firebase_uid,
            'email', current_profile.email,
            'display_name', current_profile.display_name,
            'first_name', current_profile.first_name,
            'last_name', current_profile.last_name,
            'phone_number', current_profile.phone_number,
            'date_of_birth', current_profile.date_of_birth,
            'gender', current_profile.gender,
            'native_language', current_profile.native_language,
            'address_line_1', current_profile.address_line_1,
            'address_line_2', current_profile.address_line_2,
            'city', current_profile.city,
            'state', current_profile.state,
            'postal_code', current_profile.postal_code,
            'country', current_profile.country,
            'occupation', current_profile.occupation,
            'company_name', current_profile.company_name,
            'annual_income', current_profile.annual_income,
            'profile_completion_dont_ask_again', current_profile.profile_completion_dont_ask_again,
            'photo_url', current_profile.photo_url,
            'company_logo_url', current_profile.company_logo_url,
            'auth_provider', current_profile.auth_provider,
            'is_email_verified', current_profile.is_email_verified,
            'sync_version', current_profile.sync_version,
            'created_at', current_profile.created_at,
            'updated_at', current_profile.updated_at,
            'last_sign_in_at', current_profile.last_sign_in_at,
            'last_active_at', current_profile.last_active_at
        )
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Log error and return failure response
        RETURN json_build_object(
            'success', false,
            'error', 'Database error occurred',
            'message', 'An unexpected error occurred while updating the profile',
            'error_code', 'DATABASE_ERROR',
            'details', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.update_user_profile(
    TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, DATE, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, DECIMAL, BOOLEAN, INTEGER
) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION public.update_user_profile IS 'Updates user profile with comprehensive field support including profile completion preferences. Supports optimistic locking via sync_version parameter.';

-- Verification query to check if the function was created successfully
SELECT 
    routine_name,
    routine_type,
    data_type,
    routine_definition IS NOT NULL as has_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'update_user_profile'
ORDER BY routine_name;
