import 'package:flutter/material.dart';

class OnboardingImage extends StatelessWidget {
  final String imagePath;
  final double? width;
  final double? height;

  const OnboardingImage({
    super.key,
    required this.imagePath,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    // Force light theme colors to prevent dark mode adaptation
    const lightBackgroundColor = Color(0xFFF5F5F5); // Light grey background
    const lightIconColor = Color(0xFF9E9E9E); // Medium grey for icon and text

    // Image placeholder covering full width and flexible height
    return Expanded(
      flex: 10, // Takes ~91% of available space
      child: Container(
        width: double.infinity,
        color: lightBackgroundColor, // Fixed light background color
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 80,
              color: lightIconColor.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 12),
            Text(
              'Image Placeholder',
              style: const TextStyle(
                color: lightIconColor,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }


}
