import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:aai/services/firebase_supabase_sync_service.dart';
import 'package:aai/models/sync_result.dart';

// Generate mocks
@GenerateMocks([
  firebase_auth.FirebaseAuth,
  firebase_auth.User,
  firebase_auth.UserInfo,
  firebase_auth.UserMetadata,
])
import 'firebase_supabase_sync_service_test.mocks.dart';

void main() {
  group('FirebaseSupabaseSyncService', () {
    late FirebaseSupabaseSyncService syncService;
    late MockUser mockUser;
    late MockUserInfo mockUserInfo;
    late MockUserMetadata mockUserMetadata;

    setUp(() {
      mockUser = MockUser();
      mockUserInfo = MockUserInfo();
      mockUserMetadata = MockUserMetadata();

      // Setup basic user data
      when(mockUser.uid).thenReturn('test-firebase-uid');
      when(mockUser.email).thenReturn('<EMAIL>');
      when(mockUser.displayName).thenReturn('Test User');
      when(mockUser.phoneNumber).thenReturn('+**********');
      when(mockUser.photoURL).thenReturn('https://example.com/photo.jpg');
      when(mockUser.emailVerified).thenReturn(true);
      when(mockUser.metadata).thenReturn(mockUserMetadata);
      when(mockUserMetadata.creationTime).thenReturn(DateTime.now().subtract(const Duration(minutes: 1)));

      // Reset singleton for testing
      syncService = FirebaseSupabaseSyncService.instance;
    });

    group('Singleton Pattern', () {
      test('should return same instance', () {
        final instance1 = FirebaseSupabaseSyncService.instance;
        final instance2 = FirebaseSupabaseSyncService.instance;
        expect(identical(instance1, instance2), true);
      });
    });

    group('Input Validation Tests', () {
      test('checkProfileExists should return validation failure for empty firebase UID', () async {
        // Act
        final result = await syncService.checkProfileExists('');

        // Assert
        expect(result.isSuccess, false);
        expect(result.errorType, SyncErrorType.validationError);
        expect(result.errorMessage, 'Firebase UID cannot be empty');
      });

      test('checkProfileExists should return validation failure for whitespace-only firebase UID', () async {
        // Act
        final result = await syncService.checkProfileExists('   ');

        // Assert
        expect(result.isSuccess, false);
        expect(result.errorType, SyncErrorType.validationError);
        expect(result.errorMessage, 'Firebase UID cannot be empty');
      });
    });

    group('Firebase User Data Processing', () {
      test('should handle Firebase user with all data fields', () {
        // Arrange
        when(mockUser.uid).thenReturn('firebase_123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.displayName).thenReturn('Test User');
        when(mockUser.phoneNumber).thenReturn('+**********');
        when(mockUser.photoURL).thenReturn('https://example.com/photo.jpg');
        when(mockUser.emailVerified).thenReturn(true);
        when(mockUserInfo.providerId).thenReturn('google.com');
        when(mockUser.providerData).thenReturn([mockUserInfo]);

        // Act & Assert - Just verify the service can handle the user data
        expect(mockUser.uid, 'firebase_123');
        expect(mockUser.email, '<EMAIL>');
        expect(mockUser.displayName, 'Test User');
        expect(mockUser.phoneNumber, '+**********');
        expect(mockUser.photoURL, 'https://example.com/photo.jpg');
        expect(mockUser.emailVerified, true);
      });

      test('should handle Firebase user with minimal data', () {
        // Arrange
        when(mockUser.uid).thenReturn('firebase_123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.displayName).thenReturn(null);
        when(mockUser.phoneNumber).thenReturn(null);
        when(mockUser.photoURL).thenReturn(null);
        when(mockUser.emailVerified).thenReturn(false);
        when(mockUser.providerData).thenReturn([]);

        // Act & Assert - Just verify the service can handle minimal user data
        expect(mockUser.uid, 'firebase_123');
        expect(mockUser.email, '<EMAIL>');
        expect(mockUser.displayName, null);
        expect(mockUser.phoneNumber, null);
        expect(mockUser.photoURL, null);
        expect(mockUser.emailVerified, false);
      });
    });

    group('Service Integration Tests', () {
      test('should be properly instantiated as singleton', () {
        // Act & Assert
        expect(syncService, isNotNull);
        expect(syncService, isA<FirebaseSupabaseSyncService>());
      });

      test('should handle null Firebase user gracefully', () async {
        // Act
        final result = await syncService.syncCurrentUser();

        // Assert - Should handle null user gracefully
        expect(result, isNotNull);
        expect(result!.isSuccess, false);
        expect(result.errorType, SyncErrorType.authenticationError);
        expect(result.errorMessage, 'No authenticated Firebase user found');
      });
    });

    group('Batch Operations', () {
      test('should handle empty user list in batch sync', () async {
        // Act
        final results = await syncService.batchSyncUsers([]);

        // Assert
        expect(results, isEmpty);
      });

      test('should handle single user in batch sync', () async {
        // Arrange
        when(mockUser.uid).thenReturn('firebase_123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.displayName).thenReturn('Test User');
        when(mockUser.emailVerified).thenReturn(true);
        when(mockUser.providerData).thenReturn([]);

        // Act
        final results = await syncService.batchSyncUsers([mockUser]);

        // Assert
        expect(results, hasLength(1));
        expect(results.first, isA<SyncResult>());
        // Note: The actual result will depend on database state,
        // but we can verify the structure is correct
      });
    });

    group('Statistics', () {
      test('should return statistics structure even on error', () async {
        // Act
        final stats = await syncService.getSyncStatistics();

        // Assert
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('lastUpdated'), true);
        // Either contains valid stats or error information
        expect(stats.containsKey('totalProfiles') || stats.containsKey('error'), true);
      });
    });

    group('Retry Mechanism', () {
      test('should handle retry with backoff', () async {
        // Act
        final result = await syncService.retrySyncWithBackoff(
          mockUser,
          maxRetries: 2,
          initialDelay: const Duration(milliseconds: 10),
        );

        // Assert
        expect(result, isA<SyncResult>());
        // The result will depend on the actual sync operation
      });

      test('should respect max retries parameter', () async {
        // Act
        final result = await syncService.retrySyncWithBackoff(
          mockUser,
          maxRetries: 1,
          initialDelay: const Duration(milliseconds: 10),
        );

        // Assert
        expect(result, isA<SyncResult>());
      });
    });

    group('Sync Status Management', () {
      test('should handle updateSyncStatus gracefully', () async {
        // Act
        final result = await syncService.updateSyncStatus(
          firebaseUid: 'test-uid',
          status: 'success',
        );

        // Assert
        expect(result, isA<bool>());
      });

      test('should handle getProfilesNeedingRetry gracefully', () async {
        // Act
        final profiles = await syncService.getProfilesNeedingRetry();

        // Assert
        expect(profiles, isA<List<Map<String, dynamic>>>());
      });
    });

    group('Error Handling', () {
      test('should handle null user gracefully', () async {
        // This test verifies that the service handles edge cases properly
        expect(() => syncService.checkProfileExists('valid-uid'), returnsNormally);
      });

      test('should validate Firebase user data', () async {
        // Setup user with minimal data
        when(mockUser.uid).thenReturn('test-uid');
        when(mockUser.email).thenReturn(null);
        when(mockUser.displayName).thenReturn(null);
        when(mockUser.phoneNumber).thenReturn(null);
        when(mockUser.photoURL).thenReturn(null);
        when(mockUser.emailVerified).thenReturn(false);

        // Act
        final result = await syncService.syncUserToSupabase(mockUser);

        // Assert
        expect(result, isA<SyncResult>());
        // Should handle null values gracefully
      });
    });

    group('Performance Tests', () {
      test('should handle multiple concurrent sync operations', () async {
        // Create multiple users
        final users = List.generate(5, (index) {
          final user = MockUser();
          when(user.uid).thenReturn('test-uid-$index');
          when(user.email).thenReturn('test$<EMAIL>');
          when(user.displayName).thenReturn('Test User $index');
          when(user.emailVerified).thenReturn(true);
          when(user.metadata).thenReturn(mockUserMetadata);
          return user;
        });

        // Act
        final futures = users.map((user) => syncService.syncUserToSupabase(user));
        final results = await Future.wait(futures);

        // Assert
        expect(results, hasLength(5));
        for (final result in results) {
          expect(result, isA<SyncResult>());
        }
      });

      test('should handle batch sync efficiently', () async {
        // Create multiple users
        final users = List.generate(3, (index) {
          final user = MockUser();
          when(user.uid).thenReturn('batch-uid-$index');
          when(user.email).thenReturn('batch$<EMAIL>');
          when(user.displayName).thenReturn('Batch User $index');
          when(user.emailVerified).thenReturn(true);
          when(user.metadata).thenReturn(mockUserMetadata);
          return user;
        });

        // Act
        final stopwatch = Stopwatch()..start();
        final results = await syncService.batchSyncUsers(users);
        stopwatch.stop();

        // Assert
        expect(results, hasLength(3));
        expect(stopwatch.elapsedMilliseconds, lessThan(10000)); // Should complete within 10 seconds
        for (final result in results) {
          expect(result, isA<SyncResult>());
        }
      });
    });
  });
}
