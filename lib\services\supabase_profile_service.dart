import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../models/user_role.dart';
import '../models/profile_creation_result.dart';
import '../utils/auth_provider_mapper.dart';

class SupabaseProfileService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final firebase_auth.FirebaseAuth _firebaseAuth = firebase_auth.FirebaseAuth.instance;

  /// Automatically create a Supabase profile when user signs up with Firebase Auth
  Future<ProfileCreationResult> createProfileOnSignup(firebase_auth.User firebaseUser) async {
    try {
      // Prepare user data from Firebase Auth
      final userData = {
        'p_firebase_uid': firebaseUser.uid,
        'p_email': firebaseUser.email,
        'p_display_name': firebaseUser.displayName,
        'p_phone_number': firebaseUser.phoneNumber,
        'p_photo_url': firebaseUser.photoURL,
        'p_auth_provider': _getAuthProvider(firebaseUser),
      };

      // Call Supabase function to handle signup
      final response = await _supabase.rpc('handle_user_signup', params: userData);

      if (response['success'] == true) {
        // Parse the successful response
        final profileData = response as Map<String, dynamic>;
        
        return ProfileCreationResult.success(
          profileId: profileData['profile_id'],
          firebaseUid: profileData['firebase_uid'],
          email: profileData['email'],
          displayName: profileData['display_name'],
          message: profileData['message'],
        );
      } else {
        // Handle creation failure
        return ProfileCreationResult.failure(
          errorMessage: response['error'] ?? 'Unknown error',
          message: response['message'] ?? 'Failed to create profile',
        );
      }
    } catch (e) {
      return ProfileCreationResult.failure(
        errorMessage: e.toString(),
        message: 'Exception during profile creation: ${e.toString()}',
      );
    }
  }

  /// Sync Firebase Auth user data to Supabase profile
  Future<bool> syncFirebaseUserData(firebase_auth.User firebaseUser) async {
    try {
      // Use AuthProviderMapper for accurate provider detection
      final authProvider = AuthProviderMapper.instance.mapFirebaseProviderToAppProvider(firebaseUser);

      final userData = {
        'p_firebase_uid': firebaseUser.uid,
        'p_email': firebaseUser.email,
        'p_display_name': firebaseUser.displayName,
        'p_phone_number': firebaseUser.phoneNumber,
        'p_photo_url': firebaseUser.photoURL,
        'p_is_email_verified': firebaseUser.emailVerified,
        'p_auth_provider': authProvider,
        'p_last_sign_in_at': DateTime.now().toIso8601String(),
      };

      final result = await _supabase.rpc('sync_firebase_user_data', params: userData);
      return result == true;
    } catch (e) {
      print('Error syncing Firebase user data: $e');
      return false;
    }
  }

  /// Get profile with role information
  Future<AppUser?> getProfileWithRole(String firebaseUid) async {
    try {
      final response = await _supabase
          .rpc('get_profile_with_role', params: {'uid': firebaseUid});

      if (response != null && response.isNotEmpty) {
        final profileData = response.first;

        // Extract role information if present
        UserRole? role;
        if (profileData['role_name'] != null) {
          role = UserRole(
            id: profileData['role_id'] as String? ?? '',
            roleName: profileData['role_name'] as String,
            roleCode: profileData['role_code'] as String? ?? '',
            description: profileData['role_description'] as String?,
            permissions: Map<String, dynamic>.from(profileData['role_permissions'] ?? {}),
            hierarchyLevel: profileData['role_hierarchy_level'] as int? ?? 1,
            isActive: profileData['role_is_active'] as bool? ?? true,
          );
        }

        return AppUser.fromSupabaseWithRole(profileData, role);
      }
      return null;
    } catch (e) {
      print('Error getting profile with role: $e');
      return null;
    }
  }

  /// Check if profile exists for Firebase UID
  Future<bool> profileExists(String firebaseUid) async {
    try {
      final response = await _supabase
          .from('profiles')
          .select('id')
          .eq('firebase_uid', firebaseUid)
          .isFilter('deleted_at', null)
          .maybeSingle();

      return response != null;
    } catch (e) {
      print('Error checking profile existence: $e');
      return false;
    }
  }

  /// Get available roles for role selection
  Future<List<UserRole>> getAvailableRoles() async {
    try {
      final response = await _supabase
          .from('roles')
          .select()
          .eq('is_active', true)
          .order('hierarchy_level');

      return response.map((role) => UserRole.fromSupabase(role)).toList();
    } catch (e) {
      print('Error getting available roles: $e');
      return [];
    }
  }

  /// Update user profile
  Future<bool> updateProfile(String firebaseUid, Map<String, dynamic> updates) async {
    try {
      await _supabase
          .from('profiles')
          .update(updates)
          .eq('firebase_uid', firebaseUid);
      return true;
    } catch (e) {
      print('Error updating profile: $e');
      return false;
    }
  }

  /// Check user permission
  Future<bool> hasPermission(String firebaseUid, String permission) async {
    try {
      final response = await _supabase.rpc('user_has_permission', params: {
        'uid': firebaseUid,
        'permission_key': permission,
      });
      return response == true;
    } catch (e) {
      print('Error checking permission: $e');
      return false;
    }
  }

  /// Sync Firebase user data on sign-in (enhanced version)
  Future<ProfileCreationResult> syncFirebaseUserOnSignIn(firebase_auth.User firebaseUser) async {
    try {
      // Use AuthProviderMapper for more accurate provider detection
      final authProvider = AuthProviderMapper.instance.mapFirebaseProviderToAppProvider(firebaseUser);

      final userData = {
        'p_firebase_uid': firebaseUser.uid,
        'p_email': firebaseUser.email,
        'p_display_name': firebaseUser.displayName,
        'p_phone_number': firebaseUser.phoneNumber,
        'p_photo_url': firebaseUser.photoURL,
        'p_is_email_verified': firebaseUser.emailVerified,
        'p_auth_provider': authProvider,
        'p_last_sign_in_at': DateTime.now().toIso8601String(),
      };

      final result = await _supabase.rpc('sync_firebase_user_data', params: userData);

      if (result == true) {
        return ProfileCreationResult.success(
          message: 'Profile synced successfully',
          firebaseUid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: firebaseUser.displayName,
          authProvider: authProvider,
        );
      } else {
        return ProfileCreationResult.failure(
          message: 'Failed to sync profile',
        );
      }
    } catch (e) {
      print('Error syncing Firebase user data on sign-in: $e');
      return ProfileCreationResult.failure(
        message: 'Failed to sync profile: ${e.toString()}',
      );
    }
  }

  /// Create profile from Firebase Auth (enhanced version with provider mapping)
  Future<ProfileCreationResult> createProfileFromFirebaseAuth(firebase_auth.User firebaseUser) async {
    try {
      // Use AuthProviderMapper for accurate provider detection
      final authProvider = AuthProviderMapper.instance.mapFirebaseProviderToAppProvider(firebaseUser);

      // Prepare user data from Firebase Auth with enhanced provider mapping
      final userData = {
        'p_firebase_uid': firebaseUser.uid,
        'p_email': firebaseUser.email,
        'p_display_name': firebaseUser.displayName,
        'p_phone_number': firebaseUser.phoneNumber,
        'p_photo_url': firebaseUser.photoURL,
        'p_auth_provider': authProvider,
        'p_is_email_verified': firebaseUser.emailVerified,
        'p_last_sign_in_at': DateTime.now().toIso8601String(),
      };

      // Call Supabase function to handle signup
      final response = await _supabase.rpc('handle_user_signup', params: userData);

      if (response['success'] == true) {
        // Parse the successful response
        final profileData = response as Map<String, dynamic>;

        return ProfileCreationResult.success(
          profileId: profileData['profile_id'],
          firebaseUid: profileData['firebase_uid'],
          email: profileData['email'],
          displayName: profileData['display_name'],
          message: profileData['message'],
        );
      } else {
        // Handle creation failure
        return ProfileCreationResult.failure(
          errorMessage: response['error'] ?? 'Unknown error',
          message: response['message'] ?? 'Failed to create profile',
        );
      }
    } catch (e) {
      return ProfileCreationResult.failure(
        errorMessage: e.toString(),
        message: 'Exception during profile creation: ${e.toString()}',
      );
    }
  }

  /// Update last sign-in timestamp for existing profile
  Future<bool> updateLastSignInTimestamp(String firebaseUid) async {
    try {
      await _supabase
          .from('profiles')
          .update({'last_sign_in_at': DateTime.now().toIso8601String()})
          .eq('firebase_uid', firebaseUid)
          .isFilter('deleted_at', null);
      return true;
    } catch (e) {
      print('Error updating last sign-in timestamp: $e');
      return false;
    }
  }

  /// Validate required fields before sync
  bool validateFirebaseUserForSync(firebase_auth.User firebaseUser) {
    // Firebase UID is always required
    if (firebaseUser.uid.isEmpty) {
      print('Validation failed: Firebase UID is empty');
      return false;
    }

    // At least email or phone number should be present
    if (firebaseUser.email == null && firebaseUser.phoneNumber == null) {
      print('Validation failed: Both email and phone number are null');
      return false;
    }

    return true;
  }

  /// Helper method to determine auth provider from Firebase User (enhanced)
  String _getAuthProvider(firebase_auth.User user) {
    // Use the AuthProviderMapper for consistent provider mapping
    return AuthProviderMapper.instance.mapFirebaseProviderToAppProvider(user);
  }
}


