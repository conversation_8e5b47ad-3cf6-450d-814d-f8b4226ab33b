-- CRITICAL FIX: Firebase Sync Service Phone Number Issue
-- This script addresses the phone number corruption caused by Firebase sync service

-- 1. First, run the diagnostic to see current state
SELECT 
    'Current Database State' as check_type,
    firebase_uid,
    phone_number,
    profile_completion_dont_ask_again,
    sync_version,
    updated_at
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

-- 2. Fix the phone number back to correct format (in case it got corrupted again)
UPDATE public.profiles 
SET 
    phone_number = '+************',
    updated_at = NOW()
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2'
AND phone_number != '+************';

-- 3. Check if the update was applied
SELECT 
    'After Phone Fix' as check_type,
    firebase_uid,
    phone_number,
    profile_completion_dont_ask_again,
    sync_version,
    updated_at
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

-- 4. Create a trigger to prevent phone number corruption from direct table updates
CREATE OR REPLACE FUNCTION public.prevent_phone_corruption()
RETURNS TRIGGER AS $$
BEGIN
    -- If phone_number is being updated, clean it properly
    IF NEW.phone_number IS NOT NULL AND NEW.phone_number != OLD.phone_number THEN
        NEW.phone_number := public.clean_phone_number(NEW.phone_number);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create trigger on profiles table to auto-clean phone numbers
DROP TRIGGER IF EXISTS trigger_clean_phone_on_update ON public.profiles;

CREATE TRIGGER trigger_clean_phone_on_update
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.prevent_phone_corruption();

-- 6. Test the trigger by simulating a corrupted phone update
UPDATE public.profiles 
SET phone_number = '+91************'  -- Simulate corruption
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

-- 7. Check if trigger fixed it automatically
SELECT 
    'After Trigger Test' as check_type,
    firebase_uid,
    phone_number,
    'Should be +************' as expected_result,
    CASE 
        WHEN phone_number = '+************' THEN 'TRIGGER WORKING ✅'
        ELSE 'TRIGGER FAILED ❌'
    END as trigger_status
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

-- 8. Test profile completion preference update (should not affect phone)
SELECT public.update_user_profile(
    p_firebase_uid := 'tunHYHs4WBY0hEzX9aGHW0LKBYw2',
    p_profile_completion_dont_ask_again := true
) as preference_update_result;

-- 9. Verify phone number remained unchanged after preference update
SELECT 
    'After Preference Update' as check_type,
    firebase_uid,
    phone_number,
    profile_completion_dont_ask_again,
    'Phone should still be +************' as expected_phone,
    'Preference should be true' as expected_preference,
    CASE 
        WHEN phone_number = '+************' AND profile_completion_dont_ask_again = true 
        THEN 'ALL GOOD ✅'
        ELSE 'ISSUE DETECTED ❌'
    END as final_status
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

-- 10. Show trigger information
SELECT 
    'Trigger Information' as info_type,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'trigger_clean_phone_on_update';

SELECT 'Fix completed! Phone number should now be protected from corruption.' as final_message;
