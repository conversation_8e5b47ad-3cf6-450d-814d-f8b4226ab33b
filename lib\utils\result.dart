/// A Result type for handling success and error states
/// This is a simple implementation of the Result pattern commonly used in Dart/Flutter
abstract class Result<T> {
  const Result();
  
  /// Create a success result
  factory Result.success(T data) = Success<T>;
  
  /// Create an error result
  factory Result.error(String message, [Exception? exception]) = Error<T>;

  /// Create a failure result (alias for error)
  factory Result.failure(String message, [String? errorCode]) {
    return Error<T>(message, errorCode != null ? Exception(errorCode) : null);
  }
  
  /// Check if this result is a success
  bool get isSuccess => this is Success<T>;
  
  /// Check if this result is an error
  bool get isError => this is Error<T>;
  
  /// Get the data if this is a success, null otherwise
  T? get data => isSuccess ? (this as Success<T>).data : null;
  
  /// Get the error message if this is an error, null otherwise
  String? get errorMessage => isError ? (this as Error<T>).message : null;
  
  /// Get the exception if this is an error, null otherwise
  Exception? get exception => isError ? (this as Error<T>).exception : null;
  
  /// Transform the data if this is a success
  Result<R> map<R>(R Function(T data) transform) {
    if (isSuccess) {
      try {
        return Result.success(transform((this as Success<T>).data));
      } catch (e) {
        return Result.error('Transform failed: $e', e is Exception ? e : Exception(e.toString()));
      }
    }
    return Result.error((this as Error<T>).message, (this as Error<T>).exception);
  }
  
  /// Transform the error if this is an error
  Result<T> mapError(String Function(String message) transform) {
    if (isError) {
      final error = this as Error<T>;
      return Result.error(transform(error.message), error.exception);
    }
    return this;
  }
  
  /// Execute a function if this is a success
  Result<T> onSuccess(void Function(T data) action) {
    if (isSuccess) {
      action((this as Success<T>).data);
    }
    return this;
  }
  
  /// Execute a function if this is an error
  Result<T> onError(void Function(String message, Exception? exception) action) {
    if (isError) {
      final error = this as Error<T>;
      action(error.message, error.exception);
    }
    return this;
  }
  
  /// Get the data or throw an exception if this is an error
  T getOrThrow() {
    if (isSuccess) {
      return (this as Success<T>).data;
    }
    final error = this as Error<T>;
    throw error.exception ?? Exception(error.message);
  }
  
  /// Get the data or return a default value if this is an error
  T getOrDefault(T defaultValue) {
    return isSuccess ? (this as Success<T>).data : defaultValue;
  }
  
  /// Get the data or compute a default value if this is an error
  T getOrElse(T Function() defaultValue) {
    return isSuccess ? (this as Success<T>).data : defaultValue();
  }

  /// Pattern matching method similar to freezed's when method
  R when<R>({
    required R Function(T data) success,
    required R Function(String message, String? errorCode) failure,
  }) {
    if (isSuccess) {
      return success((this as Success<T>).data);
    } else {
      final error = this as Error<T>;
      final errorCode = error.exception?.toString();
      return failure(error.message, errorCode);
    }
  }
}

/// Success result containing data
class Success<T> extends Result<T> {
  final T data;
  
  const Success(this.data);
  
  @override
  bool operator ==(Object other) {
    return other is Success<T> && other.data == data;
  }
  
  @override
  int get hashCode => data.hashCode;
  
  @override
  String toString() => 'Success($data)';
}

/// Error result containing error message and optional exception
class Error<T> extends Result<T> {
  final String message;
  final Exception? exception;
  
  const Error(this.message, [this.exception]);
  
  @override
  bool operator ==(Object other) {
    return other is Error<T> && 
           other.message == message && 
           other.exception == exception;
  }
  
  @override
  int get hashCode => Object.hash(message, exception);
  
  @override
  String toString() => 'Error($message${exception != null ? ', $exception' : ''})';
}

/// Extension methods for Future<Result<T>>
extension FutureResultExtension<T> on Future<Result<T>> {
  /// Transform the data if the future result is a success
  Future<Result<R>> mapAsync<R>(Future<R> Function(T data) transform) async {
    final result = await this;
    if (result.isSuccess) {
      try {
        final transformedData = await transform(result.data!);
        return Result.success(transformedData);
      } catch (e) {
        return Result.error('Async transform failed: $e', e is Exception ? e : Exception(e.toString()));
      }
    }
    return Result.error(result.errorMessage!, result.exception);
  }
  
  /// Execute an async function if the future result is a success
  Future<Result<T>> onSuccessAsync(Future<void> Function(T data) action) async {
    final result = await this;
    if (result.isSuccess) {
      await action(result.data!);
    }
    return result;
  }
  
  /// Execute an async function if the future result is an error
  Future<Result<T>> onErrorAsync(Future<void> Function(String message, Exception? exception) action) async {
    final result = await this;
    if (result.isError) {
      await action(result.errorMessage!, result.exception);
    }
    return result;
  }
}

/// Utility functions for working with Results
class ResultUtils {
  /// Combine multiple results into a single result containing a list
  static Result<List<T>> combine<T>(List<Result<T>> results) {
    final List<T> data = [];
    for (final result in results) {
      if (result.isError) {
        return Result.error(result.errorMessage!, result.exception);
      }
      data.add(result.data!);
    }
    return Result.success(data);
  }
  
  /// Execute a function that might throw and wrap the result
  static Result<T> tryExecute<T>(T Function() action) {
    try {
      return Result.success(action());
    } catch (e) {
      return Result.error(e.toString(), e is Exception ? e : Exception(e.toString()));
    }
  }
  
  /// Execute an async function that might throw and wrap the result
  static Future<Result<T>> tryExecuteAsync<T>(Future<T> Function() action) async {
    try {
      final data = await action();
      return Result.success(data);
    } catch (e) {
      return Result.error(e.toString(), e is Exception ? e : Exception(e.toString()));
    }
  }
}
