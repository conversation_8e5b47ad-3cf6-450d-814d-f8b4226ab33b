# Error Codes and Troubleshooting Guide

This document provides comprehensive information about error codes, their meanings, and troubleshooting steps for the Firebase Auth to Supabase sync system.

## Error Code Structure

Error codes follow the pattern: `SYNC_[CATEGORY]_[SPECIFIC_ERROR]`

### Categories
- **AUTH**: Authentication-related errors
- **DB**: Database operation errors
- **NET**: Network connectivity errors
- **VAL**: Data validation errors
- **SYS**: System/service errors
- **QUEUE**: Queue processing errors

## Authentication Errors (AUTH)

### SYNC_AUTH_001: Firebase User Not Found
**Description**: No authenticated Firebase user available for sync operation.

**Causes**:
- User not signed in to Firebase Auth
- Firebase Auth session expired
- User signed out during sync operation

**Resolution**:
1. Verify user is properly authenticated
2. Check Firebase Auth state
3. Re-authenticate user if necessary

**Code Example**:
```dart
if (FirebaseAuth.instance.currentUser == null) {
  // Handle unauthenticated state
  return SyncResult.authFailure(
    operationType: SyncOperationType.dataSync,
    errorMessage: 'No authenticated user found',
  );
}
```

### SYNC_AUTH_002: Invalid Firebase UID
**Description**: Firebase UID is empty, null, or invalid format.

**Causes**:
- Corrupted Firebase Auth state
- Invalid user object
- System error in Firebase Auth

**Resolution**:
1. Check Firebase Auth configuration
2. Verify user object integrity
3. Re-authenticate user
4. Contact Firebase support if persistent

### SYNC_AUTH_003: Provider Data Missing
**Description**: Required provider data not available from Firebase Auth.

**Causes**:
- Incomplete authentication flow
- Provider-specific configuration issues
- Firebase Auth service degradation

**Resolution**:
1. Check provider configuration (Google, Apple, etc.)
2. Verify authentication flow completion
3. Review Firebase console settings
4. Test with different authentication providers

## Database Errors (DB)

### SYNC_DB_001: Connection Failed
**Description**: Unable to establish connection to Supabase database.

**Causes**:
- Network connectivity issues
- Supabase service outage
- Invalid Supabase configuration
- Firewall/proxy blocking requests

**Resolution**:
1. Check network connectivity
2. Verify Supabase URL and API key
3. Check Supabase service status
4. Review firewall/proxy settings

**Monitoring**:
```dart
// Check Supabase connection
try {
  final response = await supabase.from('profiles').select('id').limit(1);
  // Connection successful
} catch (e) {
  // Handle connection error
  logError('SYNC_DB_001', 'Supabase connection failed: $e');
}
```

### SYNC_DB_002: RLS Policy Violation
**Description**: Row Level Security policy preventing database operation.

**Causes**:
- Incorrect RLS policy configuration
- JWT token issues
- User permissions not properly set
- Policy logic errors

**Resolution**:
1. Review RLS policies in Supabase dashboard
2. Verify JWT token contains correct user ID
3. Check policy conditions and logic
4. Test with different user accounts

**Example RLS Policy**:
```sql
-- Correct policy for user access
CREATE POLICY "Users can access own profile" ON profiles
  FOR ALL USING (firebase_uid = auth.jwt() ->> 'sub');
```

### SYNC_DB_003: Constraint Violation
**Description**: Database constraint violation during insert/update operation.

**Causes**:
- Duplicate firebase_uid (unique constraint)
- Invalid data format
- Missing required fields
- Data type mismatches

**Resolution**:
1. Check for existing profile with same firebase_uid
2. Validate data format and types
3. Ensure all required fields are provided
4. Review database schema constraints

### SYNC_DB_004: Transaction Timeout
**Description**: Database transaction exceeded timeout limit.

**Causes**:
- Large data operations
- Database performance issues
- Network latency
- Concurrent operations blocking

**Resolution**:
1. Optimize query performance
2. Reduce transaction size
3. Check database performance metrics
4. Implement retry logic with backoff

## Network Errors (NET)

### SYNC_NET_001: No Internet Connection
**Description**: Device has no internet connectivity.

**Causes**:
- WiFi/mobile data disabled
- Network outage
- Airplane mode enabled
- Router/ISP issues

**Resolution**:
1. Check device network settings
2. Verify internet connectivity
3. Try different network connection
4. Operations will be queued for retry when connection restored

**Auto-Recovery**:
```dart
// Connectivity monitoring
_connectivity.onConnectivityChanged.listen((result) {
  if (result != ConnectivityResult.none) {
    // Connection restored, process queue
    syncQueueService.processQueue();
  }
});
```

### SYNC_NET_002: Request Timeout
**Description**: Network request exceeded timeout limit.

**Causes**:
- Slow network connection
- Server overload
- Large payload size
- Network congestion

**Resolution**:
1. Check network speed and stability
2. Retry with exponential backoff
3. Reduce payload size if possible
4. Consider increasing timeout for slow networks

### SYNC_NET_003: DNS Resolution Failed
**Description**: Unable to resolve Supabase domain name.

**Causes**:
- DNS server issues
- Domain configuration problems
- Network filtering/blocking
- Incorrect Supabase URL

**Resolution**:
1. Verify Supabase URL configuration
2. Try different DNS servers
3. Check network filtering settings
4. Test with direct IP if available

## Validation Errors (VAL)

### SYNC_VAL_001: Invalid Email Format
**Description**: Email address format validation failed.

**Causes**:
- Malformed email address
- Special characters not supported
- Email too long
- Empty email when required

**Resolution**:
1. Validate email format before sync
2. Sanitize email input
3. Handle null/empty emails appropriately
4. Use proper email validation regex

**Validation Example**:
```dart
bool isValidEmail(String? email) {
  if (email == null || email.isEmpty) return true; // Allow null emails
  return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
}
```

### SYNC_VAL_002: Display Name Too Long
**Description**: Display name exceeds maximum length limit.

**Causes**:
- User entered very long name
- Data corruption
- Unicode character issues
- Database field length limit

**Resolution**:
1. Truncate display name to maximum length
2. Validate input length before submission
3. Handle Unicode characters properly
4. Consider increasing database field length

### SYNC_VAL_003: Invalid Phone Number Format
**Description**: Phone number format validation failed.

**Causes**:
- Invalid phone number format
- Missing country code
- Special characters in number
- Number too long/short

**Resolution**:
1. Use proper phone number validation
2. Normalize phone number format
3. Handle international formats
4. Validate with Firebase Auth requirements

## System Errors (SYS)

### SYNC_SYS_001: Service Unavailable
**Description**: Sync service is temporarily unavailable.

**Causes**:
- Service in degraded mode
- Too many consecutive failures
- Maintenance mode
- Resource exhaustion

**Resolution**:
1. Check service health status
2. Wait for automatic recovery
3. Use manual sync retry
4. Contact system administrator

**Service Health Check**:
```dart
final isAvailable = await syncService.isServiceAvailable();
if (!isAvailable) {
  // Service degraded, queue operation
  queueService.addToQueue(userData);
}
```

### SYNC_SYS_002: Rate Limit Exceeded
**Description**: Too many sync operations attempted in short time.

**Causes**:
- Rapid authentication attempts
- Automated testing
- System malfunction
- DDoS protection triggered

**Resolution**:
1. Implement proper rate limiting
2. Add delays between operations
3. Use exponential backoff
4. Review operation frequency

### SYNC_SYS_003: Memory Limit Exceeded
**Description**: System memory limit exceeded during operation.

**Causes**:
- Large queue size
- Memory leaks
- Insufficient device memory
- Too many concurrent operations

**Resolution**:
1. Clear old queue items
2. Restart application
3. Reduce concurrent operations
4. Optimize memory usage

## Queue Processing Errors (QUEUE)

### SYNC_QUEUE_001: Queue Full
**Description**: Sync queue has reached maximum capacity.

**Causes**:
- Too many failed sync operations
- Queue not being processed
- Service degradation
- High failure rate

**Resolution**:
1. Process queue manually
2. Clear old queue items
3. Investigate high failure rate
4. Increase queue capacity if needed

**Queue Management**:
```dart
// Check queue size before adding
final queueStatus = await queueService.getQueueStatus();
if (queueStatus['queueSize'] >= MAX_QUEUE_SIZE) {
  // Handle queue full condition
  await queueService.clearOldItems();
}
```

### SYNC_QUEUE_002: Processing Failed
**Description**: Queue processing operation failed.

**Causes**:
- Corrupted queue data
- Service unavailable
- Invalid queue items
- System errors

**Resolution**:
1. Retry queue processing
2. Clear corrupted items
3. Check service availability
4. Validate queue item format

### SYNC_QUEUE_003: Item Not Found
**Description**: Requested queue item not found.

**Causes**:
- Item already processed
- Item expired/removed
- Incorrect item ID
- Queue corruption

**Resolution**:
1. Verify item ID
2. Check if item was already processed
3. Refresh queue status
4. Use current queue items

## Error Recovery Strategies

### Automatic Recovery

1. **Exponential Backoff**: Automatic retry with increasing delays
2. **Queue Processing**: Failed operations queued for later retry
3. **Service Health Monitoring**: Automatic service recovery detection
4. **Connectivity Monitoring**: Automatic retry when connection restored

### Manual Recovery

1. **Manual Sync**: User-initiated sync retry
2. **Queue Processing**: Manual queue processing
3. **Error Log Review**: Detailed error analysis
4. **Service Reset**: Manual service state reset

### Monitoring and Alerting

1. **Health Dashboard**: Real-time error monitoring
2. **Error Statistics**: Historical error analysis
3. **Alert Conditions**: Automatic alert generation
4. **Log Export**: Detailed error log export

## Best Practices for Error Handling

### Development

1. **Comprehensive Testing**: Test all error scenarios
2. **Proper Logging**: Log errors with sufficient context
3. **User-Friendly Messages**: Provide clear error messages
4. **Graceful Degradation**: Handle errors without breaking app

### Production

1. **Monitoring**: Continuous error rate monitoring
2. **Alerting**: Set up alerts for critical errors
3. **Recovery**: Implement automatic recovery mechanisms
4. **Documentation**: Keep error documentation updated

### User Experience

1. **Non-Blocking**: Don't block user actions for sync errors
2. **Transparency**: Inform users of sync status when appropriate
3. **Recovery Options**: Provide manual recovery options
4. **Feedback**: Allow users to report persistent issues

## Error Code Reference

| Code | Category | Description | Severity | Auto-Recovery |
|------|----------|-------------|----------|---------------|
| SYNC_AUTH_001 | AUTH | Firebase User Not Found | High | No |
| SYNC_AUTH_002 | AUTH | Invalid Firebase UID | High | No |
| SYNC_AUTH_003 | AUTH | Provider Data Missing | Medium | Partial |
| SYNC_DB_001 | DB | Connection Failed | High | Yes |
| SYNC_DB_002 | DB | RLS Policy Violation | High | No |
| SYNC_DB_003 | DB | Constraint Violation | Medium | Partial |
| SYNC_DB_004 | DB | Transaction Timeout | Medium | Yes |
| SYNC_NET_001 | NET | No Internet Connection | Medium | Yes |
| SYNC_NET_002 | NET | Request Timeout | Medium | Yes |
| SYNC_NET_003 | NET | DNS Resolution Failed | High | Partial |
| SYNC_VAL_001 | VAL | Invalid Email Format | Low | Yes |
| SYNC_VAL_002 | VAL | Display Name Too Long | Low | Yes |
| SYNC_VAL_003 | VAL | Invalid Phone Number | Low | Yes |
| SYNC_SYS_001 | SYS | Service Unavailable | High | Yes |
| SYNC_SYS_002 | SYS | Rate Limit Exceeded | Medium | Yes |
| SYNC_SYS_003 | SYS | Memory Limit Exceeded | High | Partial |
| SYNC_QUEUE_001 | QUEUE | Queue Full | Medium | Yes |
| SYNC_QUEUE_002 | QUEUE | Processing Failed | Medium | Yes |
| SYNC_QUEUE_003 | QUEUE | Item Not Found | Low | No |

## Support and Escalation

### Level 1: User Self-Service
- Check network connectivity
- Retry sync operation
- Review error messages
- Use manual sync options

### Level 2: Application Support
- Review error logs
- Check service health dashboard
- Perform manual recovery operations
- Clear queue/cache if needed

### Level 3: System Administration
- Review system logs
- Check service configuration
- Investigate infrastructure issues
- Coordinate with external services

### Level 4: Development Team
- Code-level debugging
- System architecture review
- Performance optimization
- Feature enhancement
