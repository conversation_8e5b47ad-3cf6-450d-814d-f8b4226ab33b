-- Diagnostic Script: Check Database Function State
-- This script verifies if the fixes were applied correctly

-- 1. Check if update_user_profile function exists and its parameters
SELECT 
    'Function Parameters Check' as check_type,
    parameter_name,
    data_type,
    parameter_mode,
    ordinal_position
FROM information_schema.parameters 
WHERE routine_schema = 'public' 
AND routine_name = 'update_user_profile'
ORDER BY ordinal_position;

-- 2. Check if profile_completion_dont_ask_again parameter exists
SELECT 
    'Profile Completion Parameter Check' as check_type,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.parameters 
            WHERE routine_schema = 'public' 
            AND routine_name = 'update_user_profile'
            AND parameter_name = 'p_profile_completion_dont_ask_again'
        ) THEN 'PARAMETER EXISTS ✅'
        ELSE 'PARAMETER MISSING ❌'
    END as parameter_status;

-- 3. Check clean_phone_number function
SELECT 
    'Phone Cleaning Function Check' as check_type,
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'clean_phone_number';

-- 4. Check your current phone number in the database
SELECT 
    'Current Phone Number Check' as check_type,
    firebase_uid,
    phone_number,
    profile_completion_dont_ask_again,
    sync_version,
    updated_at
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

-- 5. Test the phone cleaning function with your number
SELECT 
    'Phone Cleaning Test' as check_type,
    'Input: +************' as input_number,
    public.clean_phone_number('+************') as cleaned_result,
    'Should remain: +************' as expected_result;

-- 6. Test the phone cleaning function with the corrupted number
SELECT 
    'Phone Cleaning Test (Corrupted)' as check_type,
    'Input: +91************' as input_number,
    public.clean_phone_number('+91************') as cleaned_result,
    'Should fix to: +************' as expected_result;

-- 7. Check if there are any other update functions that might be causing issues
SELECT 
    'Other Update Functions Check' as check_type,
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%update%'
AND routine_name LIKE '%profile%'
ORDER BY routine_name;

-- 8. Check recent profile updates (last 10)
SELECT 
    'Recent Profile Updates' as check_type,
    firebase_uid,
    phone_number,
    profile_completion_dont_ask_again,
    sync_version,
    updated_at
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2'
ORDER BY updated_at DESC 
LIMIT 1;

-- 9. Test update_user_profile function with preference only
SELECT 
    'Test Preference Update' as check_type,
    public.update_user_profile(
        p_firebase_uid := 'tunHYHs4WBY0hEzX9aGHW0LKBYw2',
        p_profile_completion_dont_ask_again := true
    ) as test_result;

-- 10. Check phone number after test update
SELECT 
    'Phone Number After Test' as check_type,
    firebase_uid,
    phone_number,
    profile_completion_dont_ask_again,
    sync_version,
    updated_at
FROM public.profiles 
WHERE firebase_uid = 'tunHYHs4WBY0hEzX9aGHW0LKBYw2';

SELECT 'Diagnostic completed. Check results above.' as final_status;
