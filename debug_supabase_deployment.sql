-- =============================================================================
-- SUPABASE DEPLOYMENT VERIFICATION SCRIPT
-- =============================================================================
-- Run this script in your Supabase SQL Editor to verify deployment
-- =============================================================================

-- 1. CHECK IF PROFILES TABLE EXISTS AND HAS CORRECT STRUCTURE
-- =============================================================================
SELECT 'CHECKING PROFILES TABLE STRUCTURE...' as status;

-- Check if profiles table exists
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public')
        THEN '✅ profiles table EXISTS'
        ELSE '❌ profiles table MISSING'
    END as table_status;

-- List all columns in profiles table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- =============================================================================
-- 2. CHECK IF REQUIRED FUNCTIONS EXIST
-- =============================================================================
SELECT 'CHECKING REQUIRED FUNCTIONS...' as status;

-- Check if update_user_profile function exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'update_user_profile' 
            AND routine_schema = 'public'
            AND routine_type = 'FUNCTION'
        )
        THEN '✅ update_user_profile function EXISTS'
        ELSE '❌ update_user_profile function MISSING'
    END as function_status;

-- Check if get_user_profile function exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'get_user_profile' 
            AND routine_schema = 'public'
            AND routine_type = 'FUNCTION'
        )
        THEN '✅ get_user_profile function EXISTS'
        ELSE '❌ get_user_profile function MISSING'
    END as function_status;

-- Check if create_user_profile function exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.routines 
            WHERE routine_name = 'create_user_profile' 
            AND routine_schema = 'public'
            AND routine_type = 'FUNCTION'
        )
        THEN '✅ create_user_profile function EXISTS'
        ELSE '❌ create_user_profile function MISSING'
    END as function_status;

-- List all profile-related functions
SELECT 
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%profile%'
ORDER BY routine_name;

-- =============================================================================
-- 3. CHECK RLS POLICIES
-- =============================================================================
SELECT 'CHECKING RLS POLICIES...' as status;

-- Check if RLS is enabled on profiles table
SELECT 
    schemaname,
    tablename,
    rowsecurity,
    CASE 
        WHEN rowsecurity THEN '✅ RLS ENABLED'
        ELSE '❌ RLS DISABLED'
    END as rls_status
FROM pg_tables 
WHERE tablename = 'profiles' 
AND schemaname = 'public';

-- List all policies on profiles table
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'profiles' 
AND schemaname = 'public';

-- =============================================================================
-- 4. TEST FUNCTION CALLS (SAFE TESTS)
-- =============================================================================
SELECT 'TESTING FUNCTION CALLS...' as status;

-- Test get_user_profile with non-existent user (should return error gracefully)
SELECT 'Testing get_user_profile with test UID...' as test_name;
SELECT get_user_profile('test-firebase-uid-12345');

-- =============================================================================
-- 5. CHECK CUSTOM TYPES
-- =============================================================================
SELECT 'CHECKING CUSTOM TYPES...' as status;

-- Check if sync_status enum exists
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_type 
            WHERE typname = 'sync_status' 
            AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
        )
        THEN '✅ sync_status enum EXISTS'
        ELSE '❌ sync_status enum MISSING'
    END as enum_status;

-- List enum values for sync_status
SELECT 
    enumlabel as sync_status_values
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type 
    WHERE typname = 'sync_status' 
    AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
)
ORDER BY enumsortorder;

-- =============================================================================
-- 6. SUMMARY REPORT
-- =============================================================================
SELECT 'DEPLOYMENT VERIFICATION SUMMARY' as summary;

SELECT 
    'profiles table' as component,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public')
        THEN '✅ OK'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'update_user_profile function' as component,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'update_user_profile' AND routine_schema = 'public')
        THEN '✅ OK'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'get_user_profile function' as component,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_user_profile' AND routine_schema = 'public')
        THEN '✅ OK'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'create_user_profile function' as component,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_user_profile' AND routine_schema = 'public')
        THEN '✅ OK'
        ELSE '❌ MISSING'
    END as status
UNION ALL
SELECT 
    'RLS on profiles' as component,
    CASE 
        WHEN EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'profiles' AND schemaname = 'public' AND rowsecurity = true)
        THEN '✅ OK'
        ELSE '❌ DISABLED'
    END as status;

-- =============================================================================
-- INSTRUCTIONS:
-- 1. Copy this entire script
-- 2. Go to Supabase Dashboard → SQL Editor → New Query
-- 3. Paste and run this script
-- 4. Review the results to see what's missing
-- 5. If anything shows ❌, you need to run complete_profiles_setup.sql first
-- =============================================================================
