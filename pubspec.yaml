name: aai
description: "All About Insurance - Your comprehensive insurance companion."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State management and storage
  shared_preferences: ^2.2.2

  # UI components for onboarding
  smooth_page_indicator: ^1.1.0

  # Animations
  lottie: ^3.1.0

  # Firebase & Authentication
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  firebase_analytics: ^11.3.3
  cloud_firestore: ^5.4.3
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.2
  connectivity_plus: ^5.0.2

  # URL handling for deep links
  url_launcher: ^6.2.5

  # State Management
  flutter_riverpod: ^2.6.1

  # Secure Storage
  flutter_secure_storage: ^9.2.2

  # Form Validation
  formz: ^0.7.0

  # Utilities
  equatable: ^2.0.5

  # Code generation
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

  # Image handling
  image_picker: ^1.0.7
  share_plus: ^11.0.0
  pdf: ^3.11.3
  path_provider: ^2.1.5
  supabase_flutter: ^2.9.1
  flutter_dotenv: ^5.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4
  mockito: ^5.4.6
  build_runner: ^2.5.4
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/animations/
    - assets/logo/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo/aai-logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/logo/aai-logo.png"
    background_color: "#e92933"
    theme_color: "#e92933"
  windows:
    generate: true
    image_path: "assets/logo/aai-logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/logo/aai-logo.png"
