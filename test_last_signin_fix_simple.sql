-- Simple test to verify last_sign_in_at fix is working
-- Run this after deploying the migration to confirm the issue is resolved

-- =============================================================================
-- TEST: Verify last_sign_in_at is properly set during profile operations
-- =============================================================================

-- Test 1: Create a new user profile and check last_sign_in_at
SELECT 'Testing profile creation with last_sign_in_at...' as test_step;

-- Create test profile using sync function (simulates user login)
SELECT public.sync_firebase_user_data(
    'test-user-' || extract(epoch from now())::text,
    '<EMAIL>',
    'Test User',
    '+1234567890',
    'https://example.com/photo.jpg',
    TRUE,
    'google',
    NOW()::TEXT
) as sync_result;

-- Check the most recent profile to see if last_sign_in_at is set
SELECT 
    'Profile Creation Test' as test_name,
    firebase_uid,
    email,
    display_name,
    role_id,
    subscription_plan,
    native_language,
    last_sign_in_at,
    CASE 
        WHEN last_sign_in_at IS NOT NULL THEN 'PASS ✅'
        ELSE 'FAIL ❌'
    END as test_result,
    created_at
FROM profiles 
WHERE firebase_uid LIKE 'test-user-%'
ORDER BY created_at DESC 
LIMIT 1;

-- Test 2: Update existing profile and verify last_sign_in_at changes
SELECT 'Testing profile update with last_sign_in_at...' as test_step;

-- Get the test user UID
WITH test_user AS (
    SELECT firebase_uid 
    FROM profiles 
    WHERE firebase_uid LIKE 'test-user-%' 
    ORDER BY created_at DESC 
    LIMIT 1
)
-- Update the profile (simulate another login)
SELECT public.sync_firebase_user_data(
    test_user.firebase_uid,
    '<EMAIL>',
    'Test User Updated',
    '+1234567890',
    'https://example.com/photo.jpg',
    TRUE,
    'google',
    (NOW() + INTERVAL '1 minute')::TEXT  -- Slightly later timestamp
) as sync_result
FROM test_user;

-- Check if last_sign_in_at was updated
SELECT 
    'Profile Update Test' as test_name,
    firebase_uid,
    email,
    display_name,
    last_sign_in_at,
    updated_at,
    CASE 
        WHEN last_sign_in_at IS NOT NULL 
        AND last_sign_in_at > created_at 
        THEN 'PASS ✅'
        ELSE 'FAIL ❌'
    END as test_result
FROM profiles 
WHERE firebase_uid LIKE 'test-user-%'
ORDER BY created_at DESC 
LIMIT 1;

-- Test 3: Check overall database state
SELECT 'Checking overall database state...' as test_step;

-- Count profiles with NULL last_sign_in_at
SELECT 
    'Database State Check' as test_name,
    COUNT(*) as total_profiles,
    COUNT(last_sign_in_at) as profiles_with_signin_time,
    COUNT(*) - COUNT(last_sign_in_at) as profiles_with_null_signin,
    CASE 
        WHEN COUNT(*) - COUNT(last_sign_in_at) = 0 THEN 'EXCELLENT ✅'
        WHEN COUNT(*) - COUNT(last_sign_in_at) < 5 THEN 'GOOD ⚠️'
        ELSE 'NEEDS ATTENTION ❌'
    END as overall_status
FROM profiles 
WHERE deleted_at IS NULL;

-- Test 4: Verify new schema changes are applied
SELECT 'Verifying schema changes...' as test_step;

SELECT 
    'Schema Verification' as test_name,
    column_name,
    COALESCE(column_default, 'NULL') as default_value,
    is_nullable,
    CASE 
        WHEN column_name = 'role_id' AND column_default IS NULL THEN 'PASS ✅'
        WHEN column_name = 'subscription_plan' AND column_default = '''Free''::text' THEN 'PASS ✅'
        WHEN column_name = 'native_language' AND column_default IS NULL THEN 'PASS ✅'
        ELSE 'CHECK ⚠️'
    END as status
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN ('role_id', 'subscription_plan', 'native_language')
ORDER BY column_name;

-- Clean up test data
DELETE FROM profiles WHERE firebase_uid LIKE 'test-user-%';

-- Final summary
SELECT 
    '=== TEST SUMMARY ===' as summary,
    'If all tests show PASS ✅, the last_sign_in_at fix is working correctly!' as result;
