import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:aai/models/sync_result.dart';

// Generate mocks
@GenerateMocks([firebase_auth.User, firebase_auth.UserMetadata])
import 'sync_result_test.mocks.dart';

void main() {
  group('SyncResult', () {
    group('Success Factory Constructors', () {
      test('should create successful sync result with all fields', () {
        final syncTimestamp = DateTime.now();
        final additionalData = {'test': 'data'};
        
        final result = SyncResult.success(
          operationType: SyncOperationType.profileCreation,
          profileId: 'profile_123',
          firebaseUid: 'firebase_123',
          email: '<EMAIL>',
          displayName: 'Test User',
          authProvider: 'google',
          syncTimestamp: syncTimestamp,
          message: 'Profile created successfully',
          additionalData: additionalData,
        );

        expect(result.isSuccess, true);
        expect(result.operationType, SyncOperationType.profileCreation);
        expect(result.profileId, 'profile_123');
        expect(result.firebaseUid, 'firebase_123');
        expect(result.email, '<EMAIL>');
        expect(result.displayName, 'Test User');
        expect(result.authProvider, 'google');
        expect(result.syncTimestamp, syncTimestamp);
        expect(result.message, 'Profile created successfully');
        expect(result.additionalData, additionalData);
        expect(result.errorType, null);
        expect(result.errorCode, null);
        expect(result.errorMessage, null);
      });

      test('should create successful sync result with minimal fields', () {
        final result = SyncResult.success(
          operationType: SyncOperationType.dataSync,
          message: 'Data synced successfully',
        );

        expect(result.isSuccess, true);
        expect(result.operationType, SyncOperationType.dataSync);
        expect(result.message, 'Data synced successfully');
        expect(result.syncTimestamp, isNotNull);
        expect(result.profileId, null);
        expect(result.firebaseUid, null);
      });
    });

    group('Failure Factory Constructors', () {
      test('should create failed sync result with all error details', () {
        final additionalData = {'errorDetails': 'connection timeout'};
        
        final result = SyncResult.failure(
          operationType: SyncOperationType.profileUpdate,
          errorType: SyncErrorType.networkError,
          errorCode: 'network_timeout',
          errorMessage: 'Connection timed out',
          message: 'Failed to update profile',
          firebaseUid: 'firebase_123',
          additionalData: additionalData,
        );

        expect(result.isSuccess, false);
        expect(result.operationType, SyncOperationType.profileUpdate);
        expect(result.errorType, SyncErrorType.networkError);
        expect(result.errorCode, 'network_timeout');
        expect(result.errorMessage, 'Connection timed out');
        expect(result.message, 'Failed to update profile');
        expect(result.firebaseUid, 'firebase_123');
        expect(result.additionalData, additionalData);
        expect(result.profileId, null);
        expect(result.syncTimestamp, null);
      });

      test('networkFailure should create network error result', () {
        final result = SyncResult.networkFailure(
          operationType: SyncOperationType.profileCreation,
          errorMessage: 'No internet connection',
          firebaseUid: 'firebase_123',
        );

        expect(result.isSuccess, false);
        expect(result.errorType, SyncErrorType.networkError);
        expect(result.errorCode, 'network_error');
        expect(result.errorMessage, 'No internet connection');
        expect(result.message, 'Network error during sync operation');
      });

      test('databaseFailure should create database error result', () {
        final result = SyncResult.databaseFailure(
          operationType: SyncOperationType.profileUpdate,
          errorMessage: 'Constraint violation',
          errorCode: 'unique_violation',
          firebaseUid: 'firebase_123',
        );

        expect(result.isSuccess, false);
        expect(result.errorType, SyncErrorType.databaseError);
        expect(result.errorCode, 'unique_violation');
        expect(result.errorMessage, 'Constraint violation');
        expect(result.message, 'Database error during sync operation');
      });

      test('validationFailure should create validation error result', () {
        final result = SyncResult.validationFailure(
          operationType: SyncOperationType.dataSync,
          errorMessage: 'Invalid email format',
          firebaseUid: 'firebase_123',
        );

        expect(result.isSuccess, false);
        expect(result.errorType, SyncErrorType.validationError);
        expect(result.errorCode, 'validation_error');
        expect(result.errorMessage, 'Invalid email format');
        expect(result.message, 'Validation error during sync operation');
      });

      test('authFailure should create authentication error result', () {
        final result = SyncResult.authFailure(
          operationType: SyncOperationType.profileCreation,
          errorMessage: 'Invalid token',
          firebaseUid: 'firebase_123',
        );

        expect(result.isSuccess, false);
        expect(result.errorType, SyncErrorType.authenticationError);
        expect(result.errorCode, 'auth_error');
        expect(result.errorMessage, 'Invalid token');
        expect(result.message, 'Authentication error during sync operation');
      });
    });

    group('fromFirebaseUser Factory', () {
      test('should create sync result from Firebase user', () {
        // Create mock Firebase user
        final mockUser = MockUser();
        final mockMetadata = MockUserMetadata();
        final creationTime = DateTime.now().subtract(const Duration(days: 30));
        final lastSignInTime = DateTime.now();

        when(mockUser.uid).thenReturn('firebase_123');
        when(mockUser.email).thenReturn('<EMAIL>');
        when(mockUser.displayName).thenReturn('Test User');
        when(mockUser.emailVerified).thenReturn(true);
        when(mockUser.phoneNumber).thenReturn('+**********');
        when(mockUser.photoURL).thenReturn('https://example.com/photo.jpg');
        when(mockUser.metadata).thenReturn(mockMetadata);
        when(mockMetadata.creationTime).thenReturn(creationTime);
        when(mockMetadata.lastSignInTime).thenReturn(lastSignInTime);

        final result = SyncResult.fromFirebaseUser(
          firebaseUser: mockUser,
          operationType: SyncOperationType.profileCreation,
          profileId: 'profile_123',
          authProvider: 'google',
          message: 'Profile created from Firebase user',
        );

        expect(result.isSuccess, true);
        expect(result.operationType, SyncOperationType.profileCreation);
        expect(result.profileId, 'profile_123');
        expect(result.firebaseUid, 'firebase_123');
        expect(result.email, '<EMAIL>');
        expect(result.displayName, 'Test User');
        expect(result.authProvider, 'google');
        expect(result.message, 'Profile created from Firebase user');
        
        // Check additional data
        expect(result.additionalData?['emailVerified'], true);
        expect(result.additionalData?['phoneNumber'], '+**********');
        expect(result.additionalData?['photoURL'], 'https://example.com/photo.jpg');
        expect(result.additionalData?['creationTime'], creationTime.toIso8601String());
        expect(result.additionalData?['lastSignInTime'], lastSignInTime.toIso8601String());
      });
    });

    group('JSON Serialization', () {
      test('should convert successful result to JSON', () {
        final syncTimestamp = DateTime.now();
        final result = SyncResult.success(
          operationType: SyncOperationType.profileCreation,
          profileId: 'profile_123',
          firebaseUid: 'firebase_123',
          email: '<EMAIL>',
          syncTimestamp: syncTimestamp,
          message: 'Success',
        );

        final json = result.toJson();

        expect(json['isSuccess'], true);
        expect(json['operationType'], 'profileCreation');
        expect(json['profileId'], 'profile_123');
        expect(json['firebaseUid'], 'firebase_123');
        expect(json['email'], '<EMAIL>');
        expect(json['syncTimestamp'], syncTimestamp.toIso8601String());
        expect(json['message'], 'Success');
        expect(json['errorType'], null);
      });

      test('should convert failed result to JSON', () {
        final result = SyncResult.failure(
          operationType: SyncOperationType.profileUpdate,
          errorType: SyncErrorType.networkError,
          errorCode: 'network_timeout',
          errorMessage: 'Connection failed',
          message: 'Failed to sync',
        );

        final json = result.toJson();

        expect(json['isSuccess'], false);
        expect(json['operationType'], 'profileUpdate');
        expect(json['errorType'], 'networkError');
        expect(json['errorCode'], 'network_timeout');
        expect(json['errorMessage'], 'Connection failed');
        expect(json['message'], 'Failed to sync');
      });

      test('should create result from JSON', () {
        final json = {
          'isSuccess': true,
          'operationType': 'dataSync',
          'profileId': 'profile_123',
          'firebaseUid': 'firebase_123',
          'email': '<EMAIL>',
          'message': 'Success',
          'syncTimestamp': '2023-01-01T12:00:00.000Z',
        };

        final result = SyncResult.fromJson(json);

        expect(result.isSuccess, true);
        expect(result.operationType, SyncOperationType.dataSync);
        expect(result.profileId, 'profile_123');
        expect(result.firebaseUid, 'firebase_123');
        expect(result.email, '<EMAIL>');
        expect(result.message, 'Success');
        expect(result.syncTimestamp, DateTime.parse('2023-01-01T12:00:00.000Z'));
      });
    });

    group('Helper Properties and Methods', () {
      test('should correctly identify operation types', () {
        final creationResult = SyncResult.success(
          operationType: SyncOperationType.profileCreation,
          message: 'Created',
        );
        final updateResult = SyncResult.success(
          operationType: SyncOperationType.profileUpdate,
          message: 'Updated',
        );
        final syncResult = SyncResult.success(
          operationType: SyncOperationType.dataSync,
          message: 'Synced',
        );

        expect(creationResult.isProfileCreation, true);
        expect(creationResult.isProfileUpdate, false);
        expect(creationResult.isDataSync, false);

        expect(updateResult.isProfileCreation, false);
        expect(updateResult.isProfileUpdate, true);
        expect(updateResult.isDataSync, false);

        expect(syncResult.isProfileCreation, false);
        expect(syncResult.isProfileUpdate, false);
        expect(syncResult.isDataSync, true);
      });

      test('should provide user-friendly error messages', () {
        final networkError = SyncResult.networkFailure(
          operationType: SyncOperationType.dataSync,
          errorMessage: 'Connection timeout',
        );
        final databaseError = SyncResult.databaseFailure(
          operationType: SyncOperationType.profileCreation,
          errorMessage: 'Constraint violation',
        );
        final validationError = SyncResult.validationFailure(
          operationType: SyncOperationType.profileUpdate,
          errorMessage: 'Invalid email',
        );
        final authError = SyncResult.authFailure(
          operationType: SyncOperationType.dataSync,
          errorMessage: 'Token expired',
        );

        expect(networkError.userFriendlyError, contains('Network connection issue'));
        expect(databaseError.userFriendlyError, contains('Database error occurred'));
        expect(validationError.userFriendlyError, contains('Invalid data provided'));
        expect(authError.userFriendlyError, contains('Authentication error'));
      });

      test('should return message for successful results', () {
        final successResult = SyncResult.success(
          operationType: SyncOperationType.profileCreation,
          message: 'Profile created successfully',
        );

        expect(successResult.userFriendlyError, 'Profile created successfully');
      });
    });

    group('toString', () {
      test('should provide meaningful string representation', () {
        final result = SyncResult.failure(
          operationType: SyncOperationType.profileCreation,
          errorType: SyncErrorType.networkError,
          errorMessage: 'Connection failed',
          message: 'Sync failed',
        );

        final stringRepresentation = result.toString();

        expect(stringRepresentation, contains('isSuccess: false'));
        expect(stringRepresentation, contains('operationType: SyncOperationType.profileCreation'));
        expect(stringRepresentation, contains('message: Sync failed'));
        expect(stringRepresentation, contains('errorType: SyncErrorType.networkError'));
        expect(stringRepresentation, contains('errorMessage: Connection failed'));
      });
    });
  });
}
