import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/user_profile.dart';
import '../../providers/profile_provider.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../utils/app_colors.dart';
import '../../utils/toast_utils.dart';
import 'profile_edit_screen.dart';

/// Demo screen to showcase profile update functionality
class ProfileDemoScreen extends ConsumerStatefulWidget {
  const ProfileDemoScreen({super.key});

  @override
  ConsumerState<ProfileDemoScreen> createState() => _ProfileDemoScreenState();
}

class _ProfileDemoScreenState extends ConsumerState<ProfileDemoScreen> {
  @override
  void initState() {
    super.initState();
    // Load profile when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(profileProvider.notifier).loadProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(profileProvider);
    
    // Listen to profile state changes for messages
    ref.listen<ProfileState>(profileProvider, (previous, next) {
      if (next.successMessage != null) {
        ToastUtils.showSuccess(context, next.successMessage!);
        ref.read(profileProvider.notifier).clearSuccessMessage();
      }
      
      if (next.error != null) {
        ToastUtils.showError(context, next.error!);
        ref.read(profileProvider.notifier).clearError();
      }
    });

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Profile Update Demo'),
        backgroundColor: AppColors.background,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.primary),
            onPressed: () => ref.read(profileProvider.notifier).refreshProfile(),
          ),
        ],
      ),
      body: LoadingOverlay(
        isLoading: profileState.isLoading,
        loadingText: 'Loading profile...',
        child: profileState.profile == null
            ? _buildEmptyState()
            : _buildProfileContent(profileState.profile!),
      ),
      floatingActionButton: profileState.profile != null
          ? FloatingActionButton(
              onPressed: () => _navigateToEditScreen(),
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.edit, color: Colors.white),
            )
          : null,
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_outline,
            size: 64,
            color: AppColors.grey400,
          ),
          SizedBox(height: 16),
          Text(
            'No profile data available',
            style: TextStyle(
              fontSize: 18,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Please sign in to view your profile',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent(UserProfile profile) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile Header
          _buildProfileHeader(profile),
          const SizedBox(height: 24),
          
          // Quick Actions
          _buildQuickActions(),
          const SizedBox(height: 24),
          
          // Profile Information Sections
          _buildBasicInfoSection(profile),
          const SizedBox(height: 16),
          
          _buildPersonalInfoSection(profile),
          const SizedBox(height: 16),
          
          _buildLocationSection(profile),
          const SizedBox(height: 16),
          
          _buildProfessionalSection(profile),
          const SizedBox(height: 16),
          
          _buildSyncInfoSection(profile),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(UserProfile profile) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Profile Picture
          CircleAvatar(
            radius: 32,
            backgroundColor: AppColors.primary,
            backgroundImage: profile.photoUrl != null 
                ? NetworkImage(profile.photoUrl!) 
                : null,
            child: profile.photoUrl == null
                ? Text(
                    profile.displayName?.isNotEmpty == true
                        ? profile.displayName![0].toUpperCase()
                        : 'U',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 16),
          
          // Profile Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  profile.displayName ?? 'No name',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  profile.email ?? 'No email',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                
                // Profile Completion
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: profile.profileCompletionPercentage / 100,
                        backgroundColor: AppColors.grey200,
                        valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${profile.profileCompletionPercentage}%',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Edit Profile',
                  onPressed: _navigateToEditScreen,
                  icon: const Icon(Icons.edit, size: 18, color: Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomButton(
                  text: 'Quick Update',
                  onPressed: _showQuickUpdateDialog,
                  isOutlined: true,
                  icon: const Icon(Icons.flash_on, size: 18, color: AppColors.primary),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(UserProfile profile) {
    return _buildInfoSection(
      'Basic Information',
      [
        _buildInfoRow('Display Name', profile.displayName ?? 'Not set'),
        _buildInfoRow('First Name', profile.firstName ?? 'Not set'),
        _buildInfoRow('Last Name', profile.lastName ?? 'Not set'),
        _buildInfoRow('Email', profile.email ?? 'Not set'),
        _buildInfoRow('Phone', profile.phoneNumber ?? 'Not set'),
      ],
    );
  }

  Widget _buildPersonalInfoSection(UserProfile profile) {
    return _buildInfoSection(
      'Personal Information',
      [
        _buildInfoRow('Date of Birth', profile.dateOfBirth?.toString().split(' ')[0] ?? 'Not set'),
        _buildInfoRow('Gender', profile.gender ?? 'Not set'),
        _buildInfoRow('Native Language', profile.nativeLanguage ?? 'Not set'),
      ],
    );
  }

  Widget _buildLocationSection(UserProfile profile) {
    return _buildInfoSection(
      'Location',
      [
        _buildInfoRow('City', profile.city ?? 'Not set'),
        _buildInfoRow('State', profile.state ?? 'Not set'),
        _buildInfoRow('Country', profile.country),
      ],
    );
  }

  Widget _buildProfessionalSection(UserProfile profile) {
    return _buildInfoSection(
      'Professional Information',
      [
        _buildInfoRow('Occupation', profile.occupation ?? 'Not set'),
        _buildInfoRow('Company', profile.companyName ?? 'Not set'),
        _buildInfoRow('Annual Income', profile.annualIncome?.toString() ?? 'Not set'),
      ],
    );
  }

  Widget _buildSyncInfoSection(UserProfile profile) {
    return _buildInfoSection(
      'Sync Information',
      [
        _buildInfoRow('Sync Version', profile.syncVersion.toString()),
        _buildInfoRow('Last Sign In', profile.lastSignInAt?.toString() ?? 'Never'),
        _buildInfoRow('Last Updated', profile.updatedAt?.toString() ?? 'Never'),
        _buildInfoRow('Subscription Plan', profile.subscriptionPlan),
      ],
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'Not set',
              style: TextStyle(
                fontSize: 14,
                color: value != null ? AppColors.textPrimary : AppColors.textSecondary,
                fontWeight: value != null ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToEditScreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProfileEditScreen(),
      ),
    );
  }

  void _showQuickUpdateDialog() {
    final profile = ref.read(profileProvider).profile;
    if (profile == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quick Update'),
        content: const Text('This would show a quick update form for common fields like display name, phone number, etc.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ToastUtils.showInfo(context, 'Quick update feature coming soon!');
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
}
