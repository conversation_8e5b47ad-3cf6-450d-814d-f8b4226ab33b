import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../../services/navigation_service.dart';
import '../../providers/auth_provider.dart' as auth_provider;
import '../../providers/enhanced_auth_provider.dart';
import '../../providers/profile_provider.dart';
import '../../widgets/profile_sidebar.dart';
import '../../widgets/customer_name_dialog.dart';
import '../../widgets/profile_completion_dialog.dart';
import '../../services/pdf_generation_service.dart';
import '../../services/profile_completion_service.dart';
import '../../models/user_model.dart';
import '../products/product_details_screen.dart';

class CompareScreen extends ConsumerStatefulWidget {
  final String? initialProductName;
  final String? initialCompanyName;
  final String? initialPlan;
  final String? initialSumInsured;

  const CompareScreen({
    super.key,
    this.initialProductName,
    this.initialCompanyName,
    this.initialPlan,
    this.initialSumInsured,
  });

  @override
  ConsumerState<CompareScreen> createState() => _CompareScreenState();
}

class _CompareScreenState extends ConsumerState<CompareScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int _selectedTabIndex = 0; // 0 for Benefits, 1 for Premiums

  // Fixed 3-slot comparison data structure - null represents empty slot
  late List<Map<String, dynamic>?> _comparisonSlots;

  @override
  void initState() {
    super.initState();

    // Initialize comparison slots based on whether initial data is provided
    if (widget.initialProductName != null &&
        widget.initialCompanyName != null &&
        widget.initialPlan != null &&
        widget.initialSumInsured != null) {
      // Coming from product details page - populate first slot
      _comparisonSlots = [
        {
          'company': widget.initialCompanyName!,
          'product': widget.initialProductName!,
          'plan': widget.initialPlan!,
          'sumInsured': widget.initialSumInsured!,
          'rating': 4.5, // Default rating
          'color': const Color(0xFF086788), // Default color
        },
        null, // Empty slot
        null, // Empty slot
      ];
    } else {
      // Coming from bottom navigation - all slots empty
      _comparisonSlots = [null, null, null];
    }
  }

  // Get only non-null products for comparison table
  List<Map<String, dynamic>> get _comparisonProducts =>
      _comparisonSlots.where((slot) => slot != null).cast<Map<String, dynamic>>().toList();

  // Helper method to shift cards left and keep placeholders on the right
  void _reorganizeSlots() {
    final nonNullSlots = <Map<String, dynamic>>[];

    // Collect all non-null slots
    for (final slot in _comparisonSlots) {
      if (slot != null) {
        nonNullSlots.add(slot);
      }
    }

    // Reorganize: non-null slots first, then null slots
    for (int i = 0; i < 3; i++) {
      if (i < nonNullSlots.length) {
        _comparisonSlots[i] = nonNullSlots[i];
      } else {
        _comparisonSlots[i] = null;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: const Color(0xFFf1f1f1),
      drawer: Drawer(
        child: Consumer(
          builder: (context, ref, child) {
            final currentUser = ref.watch(currentUserProvider);
            final profileState = ref.watch(profileProvider);
            final profile = profileState.profile;

            // Trigger profile load if not loaded and not currently loading
            if (profile == null && !profileState.isLoading && currentUser != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                ref.read(profileProvider.notifier).loadProfile();
              });
            }

            // Show loading state if profile is not loaded yet
            if (profile == null && currentUser != null) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFe92933)),
                  ),
                ),
              );
            }

            // Use profile data if available, fallback to Firebase Auth data only if no user
            final userName = profile?.displayName ?? currentUser?.displayName ?? 'Name';
            final userEmail = profile?.email ?? currentUser?.email ?? '<EMAIL>';
            final userPhone = profile?.phoneNumber ?? currentUser?.phoneNumber ?? '+91 - - - - - - - - - -';
            final profileImageUrl = profile?.photoUrl ?? currentUser?.photoURL;

            return ProfileSidebar(
              userName: userName,
              userEmail: userEmail,
              userPhone: userPhone,
              profileImageUrl: profileImageUrl,
              onLogout: () {
                Navigator.pop(context); // Close drawer
                ref.read(auth_provider.authProvider.notifier).signOut();
                NavigationService.instance.navigateToAlternateSignIn();
              },
              onEditProfile: () {
                Navigator.pop(context); // Close drawer
                NavigationService.instance.navigateToProfile();
              },
            );
          },
        ),
      ),
      body: Column(
        children: [
          // Header
          Container(
            color: const Color(0xFFf1f1f1),
            child: SafeArea(
              bottom: false,
              child: Container(
                height: 56,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: const BoxDecoration(
                  color: Color(0xFFf1f1f1),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0F000000),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                    const Expanded(
                      child: Text(
                        'Compare Plans',
                        style: TextStyle(
                          color: Color(0xFF111418),
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    // Home icon
                    IconButton(
                      onPressed: () {
                        NavigationService.instance.navigateToAlternateHome();
                      },
                      icon: const Icon(
                        Icons.home,
                        color: Color(0xFFe92933),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // Body content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Comparison cards
                  _buildComparisonCards(),

                  // Only show comparison table and buttons if there are products to compare
                  if (_comparisonProducts.isNotEmpty) ...[
                    const SizedBox(height: 24),
                    // Feature comparison table
                    _buildFeatureComparisonTable(),
                    const SizedBox(height: 24),
                    // Action buttons
                    _buildActionButtons(),
                  ],

                  // Show empty state message if no products are selected
                  if (_comparisonProducts.isEmpty) ...[
                    const SizedBox(height: 60),
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.compare_arrows,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Add products to compare',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 32),
                            child: Text(
                              'Select products from the cards above to start comparing their benefits and premiums',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[500],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildComparisonCards() {
    return SizedBox(
      height: 110, // Set to exactly 110px as requested
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 3, // Always show exactly 3 slots
        itemBuilder: (context, index) {
          final slot = _comparisonSlots[index];
          return Container(
            width: 240, // Set to exactly 240px as requested
            margin: EdgeInsets.only(right: index < 2 ? 16 : 0), // Right margin for first 2 cards only
            child: slot != null
                ? _buildProductCard(slot, index)
                : _buildPlaceholderCard(index),
          );
        },
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product, int index) {
    // Use new format for all cards
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(0, 0, 0, 0), // Top padding for buttons
              child: Row(
                children: [
                  // Left 40% - Company Logo
                  Expanded(
                    flex: 40,
                    child: Container(
                      height: double.infinity,
                      padding: const EdgeInsets.fromLTRB(12, 12, 0, 12),
                      child: Container(
                        decoration: BoxDecoration(
                          color: _getCompanyColor(product['company']).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _getCompanyColor(product['company']).withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            _getCompanyIcon(product['company']),
                            color: _getCompanyColor(product['company']),
                            size: 32,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Right 60% - Product Details
                  Expanded(
                    flex: 60,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Product Name
                        Text(
                          product['product'],
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF111418),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // Plan Name
                        Text(
                          product['plan'],
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF637488),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // Sum Insured
                        Text(
                          product['sumInsured'],
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF086788),
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // Star Rating
                        Row(
                          children: [
                            ...List.generate(5, (starIndex) {
                              return Icon(
                                starIndex < product['rating'].floor()
                                  ? Icons.star
                                  : starIndex < product['rating']
                                    ? Icons.star_half
                                    : Icons.star_border,
                                color: const Color(0xFFFFB800),
                                size: 14,
                              );
                            }),
                            const SizedBox(width: 4),
                            Text(
                              '${product['rating']}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF637488),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Top right buttons - Edit and Remove (stacked vertically)
            Positioned(
              bottom: 10,
              right: 2,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Remove button (top position - cross icon)
                  GestureDetector(
                    onTap: () {
                      _removeCard(index);
                    },
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Color(0xFFe92933),
                        size: 20,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8), // Spacing between remove and edit buttons
                  // Edit button (below remove button)
                  GestureDetector(
                    onTap: () {
                      _showEditProductDialog(product, index);
                    },
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.edit,
                        color: Color(0xFFe92933),
                        size: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
  }



  Widget _buildPlaceholderCard(int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showAddProductDialog(index),
          borderRadius: BorderRadius.circular(12),
          child: SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: CustomPaint(
              painter: DottedBorderPainter(
                color: const Color(0xFFe92933),
                strokeWidth: 2,
                dashLength: 8,
                gapLength: 4,
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add,
                      size: 32,
                      color: Color(0xFFe92933),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Add Product',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFFe92933),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureComparisonTable() {
    return Column(
      children: [
        // Tab Navigation Section - separate from table
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: _buildTabNavigation(),
        ),

        // Table Content Section - separate container
        Container(
          margin: const EdgeInsets.fromLTRB(16, 12, 16, 0), // Added 12px top margin for spacing between tabs and table
          decoration: BoxDecoration(
            color: const Color(0xFFf1f1f1), // Background color matching product details
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _selectedTabIndex == 0
              ? _buildBenefitsTable()
              : _buildPremiumsTable(),
        ),
      ],
    );
  }

  Widget _buildTabNavigation() {
    return Column(
      children: [
        // Tabs Section - matching product details page design exactly
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              const SizedBox(width: 16), // Left padding
              _buildTab('Benefits', 0),
              _buildTab('Premiums', 1),
              const SizedBox(width: 16), // Right padding
            ],
          ),
        ),
        // Running line below all tabs (edge-to-edge)
        Container(
          height: 1,
          width: double.infinity,
          color: const Color(0xFFE0E0E0),
        ),
      ],
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    // Dynamic width based on text length to prevent overflow
    double tabWidth = 100; // Default width for Benefits/Premiums
    if (title == 'Premiums') {
      tabWidth = 100; // Same width for consistency
    }

    return GestureDetector(
      onTap: () => setState(() => _selectedTabIndex = index),
      child: Container(
        width: tabWidth,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFe92933) : Colors.transparent,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(6),
            topRight: Radius.circular(6),
            bottomLeft: Radius.circular(0),
            bottomRight: Radius.circular(0),
          ),
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.white : const Color(0xFF086788),
          ),
        ),
      ),
    );
  }

  Widget _buildBenefitsTable() {
    final benefitsData = _getBenefitsData();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Column(
          children: [
            // Header Row
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFe0e0e0),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Benefits Header
                  Container(
                    width: 135,
                    height: 80,
                    padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 4.0, bottom: 4.0),
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Color(0xFFe0e0e0),
                          width: 1,
                        ),
                      ),
                    ),
                    child: const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Benefits',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF111418),
                        ),
                      ),
                    ),
                  ),
                  // Company Headers
                  ..._comparisonProducts.map((product) => Container(
                    width: 200,
                    height: 80,
                    padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 4.0, bottom: 4.0),
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Color(0xFFe0e0e0),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        // Left Section (40% of column width) - Company Logo
                        Expanded(
                          flex: 40,
                          child: Container(
                            height: 50,
                            padding: const EdgeInsets.all(4.0),
                            child: _buildCompanyLogo(product['company']),
                          ),
                        ),
                        // Right Section (60% of column width) - Product Details
                        Expanded(
                          flex: 60,
                          child: Container(
                            padding: const EdgeInsets.only(left: 4.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  product['product'],
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF111418),
                                  ),
                                  softWrap: true,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  product['plan'],
                                  style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF637488),
                                  ),
                                  softWrap: true,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  product['sumInsured'],
                                  style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF086788),
                                  ),
                                  softWrap: true,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
            // Data Rows
            ...benefitsData.keys.map((benefit) => Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFe0e0e0),
                    width: 1,
                  ),
                ),
              ),
              child: IntrinsicHeight( // This makes all cells in the row have the same height
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Benefit Name Cell
                    Container(
                      width: 135,
                      padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 10.0, bottom: 10.0),
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: Color(0xFFe0e0e0),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          benefit,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF111418),
                          ),
                          softWrap: true,
                        ),
                      ),
                    ),
                    // Company Data Cells
                    ..._comparisonProducts.map((product) => Container(
                      width: 200,
                      padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 10.0, bottom: 10.0),
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: Color(0xFFe0e0e0),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          benefitsData[benefit]?[product['company']] ?? 'Not available',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF111418),
                          ),
                          softWrap: true,
                        ),
                      ),
                    )),
                  ],
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyLogo(String companyName) {
    // Company logo mapping - in real app, these would be actual logo assets
    final logoMap = {
      'SecureHealth': Icons.local_hospital,
      'LifeGuard': Icons.favorite,
      'TravelSafe': Icons.flight,
    };

    final logoIcon = logoMap[companyName] ?? Icons.business;
    final logoColor = _getCompanyColor(companyName);

    return Container(
      decoration: BoxDecoration(
        color: logoColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: logoColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Icon(
          logoIcon,
          color: logoColor,
          size: 24,
        ),
      ),
    );
  }

  Color _getCompanyColor(String companyName) {
    switch (companyName) {
      case 'SecureHealth':
        return const Color(0xFF086788);
      case 'LifeGuard':
        return const Color(0xFF157A6E);
      case 'TravelSafe':
        return const Color(0xFF306B34);
      default:
        return const Color(0xFF637488);
    }
  }

  IconData _getCompanyIcon(String companyName) {
    switch (companyName) {
      case 'SecureHealth':
        return Icons.local_hospital;
      case 'LifeGuard':
        return Icons.favorite;
      case 'TravelSafe':
        return Icons.flight;
      default:
        return Icons.business;
    }
  }

  Widget _buildPremiumsTable() {
    final premiumsData = _getPremiumsData();

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Column(
          children: [
            // Header Row
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFe0e0e0),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Premium Details Header
                  Container(
                    width: 135,
                    height: 80,
                    padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 4.0, bottom: 4.0),
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Color(0xFFe0e0e0),
                          width: 1,
                        ),
                      ),
                    ),
                    child: const Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        'Premium Details',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF111418),
                        ),
                      ),
                    ),
                  ),
                  // Company Headers
                  ..._comparisonProducts.map((product) => Container(
                    width: 200,
                    height: 80,
                    padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 4.0, bottom: 4.0),
                    decoration: const BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          color: Color(0xFFe0e0e0),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        // Left Section (40% of column width) - Company Logo
                        Expanded(
                          flex: 40,
                          child: Container(
                            height: 50,
                            padding: const EdgeInsets.all(4.0),
                            child: _buildCompanyLogo(product['company']),
                          ),
                        ),
                        // Right Section (60% of column width) - Product Details
                        Expanded(
                          flex: 60,
                          child: Container(
                            padding: const EdgeInsets.only(left: 4.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  product['product'],
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF111418),
                                  ),
                                  softWrap: true,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  product['plan'],
                                  style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF637488),
                                  ),
                                  softWrap: true,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  product['sumInsured'],
                                  style: const TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                    color: Color(0xFF086788),
                                  ),
                                  softWrap: true,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
            // Data Rows
            ...premiumsData.keys.map((premiumType) => Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Color(0xFFe0e0e0),
                    width: 1,
                  ),
                ),
              ),
              child: IntrinsicHeight( // This makes all cells in the row have the same height
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Premium Type Cell
                    Container(
                      width: 135,
                      padding: const EdgeInsets.only(left: 12.0, right: 4.0, top: 10.0, bottom: 10.0),
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: Color(0xFFe0e0e0),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          premiumType,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF111418),
                          ),
                          softWrap: true,
                        ),
                      ),
                    ),
                    // Company Data Cells
                    ..._comparisonProducts.map((product) => Container(
                      width: 200,
                      padding: const EdgeInsets.only(left: 4.0, right: 12.0, top: 10.0, bottom: 10.0),
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: Color(0xFFe0e0e0),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          premiumsData[premiumType]?[product['company']] ?? 'Not available',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF111418),
                          ),
                          softWrap: true,
                        ),
                      ),
                    )),
                  ],
                ),
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Save Comparison Button
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                _showSaveComparisonDialog();
              },
              style: OutlinedButton.styleFrom(
                backgroundColor: Colors.grey[100],
                foregroundColor: const Color(0xFFe92933),
                side: const BorderSide(color: Color(0xFFe92933)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Save Comparison',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Share PDF Button
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _generateComparisonPDF();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Share PDF',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeCard(int index) {
    final product = _comparisonSlots[index];
    if (product != null) {
      setState(() {
        _comparisonSlots[index] = null;
        _reorganizeSlots(); // Automatically shift cards left
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${product['product']} removed from comparison'),
          backgroundColor: const Color(0xFFe92933),
        ),
      );
    }
  }

  void _showEditProductDialog(Map<String, dynamic> product, int index) {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        currentCompany: product['company'],
        currentProduct: product['product'],
        currentPlan: product['plan'],
        currentSumInsured: product['sumInsured'],
        insuranceData: _getInsuranceData(),
        onUpdate: (company, productName, plan, sumInsured) {
          setState(() {
            _comparisonSlots[index] = {
              ..._comparisonSlots[index]!,
              'company': company,
              'product': productName,
              'plan': plan,
              'sumInsured': sumInsured,
            };
          });
        },
      ),
    );
  }

  Map<String, Map<String, String>> _getBenefitsData() {
    // Get all unique company names from current comparison slots
    final companies = _comparisonProducts.map((p) => p['company'] as String).toSet();

    // Base benefits data structure
    final Map<String, Map<String, String>> benefitsData = {
      'Hospitalization Coverage': {},
      'Outpatient Care': {},
      'Prescription Drugs': {},
      'Emergency Care': {},
      'Maternity Benefits': {},
      'Dental Care': {},
      'Wellness Programs': {},
      'International Coverage': {},
    };

    // Populate data for each company
    for (final company in companies) {
      benefitsData['Hospitalization Coverage']![company] = _getHospitalizationBenefit(company);
      benefitsData['Outpatient Care']![company] = _getOutpatientBenefit(company);
      benefitsData['Prescription Drugs']![company] = _getPrescriptionBenefit(company);
      benefitsData['Emergency Care']![company] = _getEmergencyBenefit(company);
      benefitsData['Maternity Benefits']![company] = _getMaternityBenefit(company);
      benefitsData['Dental Care']![company] = _getDentalBenefit(company);
      benefitsData['Wellness Programs']![company] = _getWellnessBenefit(company);
      benefitsData['International Coverage']![company] = _getInternationalBenefit(company);
    }

    return benefitsData;
  }

  String _getHospitalizationBenefit(String company) {
    switch (company) {
      case 'SecureHealth':
        return 'Comprehensive coverage for room rent up to ₹10,000/day, ICU charges, surgeon fees, and all medical expenses during hospitalization';
      case 'LifeGuard':
        return 'Full hospitalization coverage including private room, specialist consultations, diagnostic tests, and emergency procedures';
      case 'TravelSafe':
        return 'Complete inpatient care coverage with cashless facility at 5000+ network hospitals across India and abroad';
      default:
        return 'Comprehensive hospitalization coverage including room rent, ICU charges, surgeon fees, and medical expenses during admission';
    }
  }

  String _getOutpatientBenefit(String company) {
    switch (company) {
      case 'SecureHealth':
        return 'OPD consultations, diagnostic tests, pharmacy bills up to ₹25,000 annually with 50% co-payment';
      case 'LifeGuard':
        return 'Unlimited OPD visits, preventive health checkups, dental and eye care with no co-payment required';
      case 'TravelSafe':
        return 'Comprehensive outpatient coverage including specialist consultations, lab tests, and prescription medicines';
      default:
        return 'Outpatient consultations, diagnostic tests, and preventive health checkups with coverage limits as per policy terms';
    }
  }

  String _getPrescriptionBenefit(String company) {
    switch (company) {
      case 'SecureHealth':
        return 'Medicine coverage up to ₹15,000 per year for chronic conditions and post-hospitalization care';
      case 'LifeGuard':
        return 'Full prescription drug coverage with home delivery service and generic medicine options';
      case 'TravelSafe':
        return 'Prescription medicine coverage including imported drugs and specialized treatments up to policy limit';
      default:
        return 'Prescription drug coverage for chronic conditions and post-hospitalization medications as per policy terms';
    }
  }

  String _getEmergencyBenefit(String company) {
    switch (company) {
      case 'SecureHealth':
        return '24/7 emergency services, ambulance coverage up to ₹2,000 per incident, and emergency room visits';
      case 'LifeGuard':
        return 'Comprehensive emergency care with air ambulance facility, trauma care, and critical care coverage';
      case 'TravelSafe':
        return 'Global emergency assistance, medical evacuation, and emergency treatment coverage worldwide';
      default:
        return '24/7 emergency medical services, ambulance coverage, and emergency room treatment as per policy coverage';
    }
  }

  String _getMaternityBenefit(String company) {
    switch (company) {
      case 'SecureHealth':
        return 'Maternity coverage up to ₹50,000 including normal delivery, C-section, and newborn care for 30 days';
      case 'LifeGuard':
      case 'TravelSafe':
        return 'Not available';
      default:
        return 'Maternity benefits including delivery expenses and newborn care as per policy terms (subject to waiting period)';
    }
  }

  String _getDentalBenefit(String company) {
    switch (company) {
      case 'LifeGuard':
        return 'Complete dental care including routine checkups, cleanings, fillings, and major dental procedures';
      case 'SecureHealth':
      case 'TravelSafe':
        return 'Not available';
      default:
        return 'Basic dental care coverage for accidental dental injuries and emergency dental treatment';
    }
  }

  String _getWellnessBenefit(String company) {
    switch (company) {
      case 'LifeGuard':
        return 'Annual health checkups, fitness programs, nutrition counseling, and wellness coaching';
      case 'SecureHealth':
      case 'TravelSafe':
        return 'Not available';
      default:
        return 'Annual preventive health checkups and basic wellness programs as per policy benefits';
    }
  }

  String _getInternationalBenefit(String company) {
    switch (company) {
      case 'TravelSafe':
        return 'Worldwide coverage including medical treatment, emergency evacuation, and repatriation services';
      case 'SecureHealth':
      case 'LifeGuard':
        return 'Not available';
      default:
        return 'Limited international coverage for emergency medical treatment during travel (subject to policy terms)';
    }
  }

  Map<String, Map<String, String>> _getPremiumsData() {
    // Get all unique company names from current comparison slots
    final companies = _comparisonProducts.map((p) => p['company'] as String).toSet();

    // Base premiums data structure
    final Map<String, Map<String, String>> premiumsData = {
      'Annual Premium': {},
      'Monthly Premium': {},
      'Deductible': {},
      'Co-payment': {},
      'Room Rent Limit': {},
      'Waiting Period': {},
    };

    // Populate data for each company
    for (final company in companies) {
      premiumsData['Annual Premium']![company] = _getAnnualPremium(company);
      premiumsData['Monthly Premium']![company] = _getMonthlyPremium(company);
      premiumsData['Deductible']![company] = _getDeductible(company);
      premiumsData['Co-payment']![company] = _getCoPayment(company);
      premiumsData['Room Rent Limit']![company] = _getRoomRentLimit(company);
      premiumsData['Waiting Period']![company] = _getWaitingPeriod(company);
    }

    return premiumsData;
  }

  String _getAnnualPremium(String company) {
    switch (company) {
      case 'SecureHealth':
        return '₹15,000 (Base premium for selected coverage)';
      case 'LifeGuard':
        return '₹18,000 (Includes additional wellness benefits)';
      case 'TravelSafe':
        return '₹22,000 (Premium coverage with international benefits)';
      default:
        return '₹16,500 (Competitive premium for comprehensive coverage)';
    }
  }

  String _getMonthlyPremium(String company) {
    switch (company) {
      case 'SecureHealth':
        return '₹1,250 (EMI option available with 0% interest)';
      case 'LifeGuard':
        return '₹1,500 (Flexible payment options available)';
      case 'TravelSafe':
        return '₹1,833 (Premium monthly plan with global coverage)';
      default:
        return '₹1,375 (Monthly payment option with flexible terms)';
    }
  }

  String _getDeductible(String company) {
    switch (company) {
      case 'SecureHealth':
        return '₹5,000 per claim (Applicable for all claims)';
      case 'LifeGuard':
        return '₹3,000 per claim (Reduced deductible for network hospitals)';
      case 'TravelSafe':
        return '₹10,000 per claim (Higher deductible for comprehensive coverage)';
      default:
        return '₹4,000 per claim (Standard deductible for all medical claims)';
    }
  }

  String _getCoPayment(String company) {
    switch (company) {
      case 'SecureHealth':
        return '10% for all claims (Minimum ₹1,000 per claim)';
      case 'LifeGuard':
        return '5% for network hospitals, 15% for non-network';
      case 'TravelSafe':
        return 'No co-payment for cashless treatment at network hospitals';
      default:
        return '8% for network hospitals, 12% for non-network facilities';
    }
  }

  String _getRoomRentLimit(String company) {
    switch (company) {
      case 'SecureHealth':
        return '1% of sum insured per day (Maximum ₹10,000/day)';
      case 'LifeGuard':
        return '2% of sum insured per day (Maximum ₹15,000/day)';
      case 'TravelSafe':
        return 'No room rent limit (Any room type allowed)';
      default:
        return '1.5% of sum insured per day (Maximum ₹12,000/day)';
    }
  }

  String _getWaitingPeriod(String company) {
    switch (company) {
      case 'SecureHealth':
        return '2 years for pre-existing diseases, 4 years for specific conditions';
      case 'LifeGuard':
        return '3 years for pre-existing diseases, immediate coverage for accidents';
      case 'TravelSafe':
        return '1 year for pre-existing diseases, immediate emergency coverage';
      default:
        return '2.5 years for pre-existing diseases, immediate accident coverage';
    }
  }

  Map<String, Map<String, dynamic>> _getInsuranceData() {
    // Sample insurance data structure - in real app this would come from API
    return {
      'SecureHealth': {
        'products': {
          'Health Insurance': {
            'plans': {
              'Basic Plan': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Premium Plan': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Platinum Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Life Insurance': {
            'plans': {
              'Term Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
              'Whole Life': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
            }
          }
        }
      },
      'LifeGuard': {
        'products': {
          'Health Plus': {
            'plans': {
              'Silver Plan': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Gold Plan': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Diamond Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Motor Insurance': {
            'plans': {
              'Third Party': ['₹5,00,000', '₹10,00,000'],
              'Comprehensive': ['₹10,00,000', '₹25,00,000', '₹50,00,000'],
            }
          }
        }
      },
      'TravelSafe': {
        'products': {
          'Health Shield': {
            'plans': {
              'Basic Shield': ['₹5,00,000', '₹10,00,000', '₹25,00,000'],
              'Premium Shield': ['₹25,00,000', '₹50,00,000', '₹1,00,00,000'],
              'Platinum Plan': ['₹50,00,000', '₹1,00,00,000', '₹2,00,00,000'],
            }
          },
          'Travel Insurance': {
            'plans': {
              'Domestic': ['₹1,00,000', '₹5,00,000'],
              'International': ['₹10,00,000', '₹25,00,000', '₹50,00,000'],
            }
          }
        }
      }
    };
  }


  void _showSaveComparisonDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            'Save Comparison',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF111418),
            ),
          ),
          content: const Text(
            'Save this comparison to access it later from your saved comparisons.',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF637488),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Color(0xFF637488)),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Comparison saved successfully!'),
                    backgroundColor: Color(0xFFe92933),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFe92933),
                foregroundColor: Colors.white,
              ),
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }



  void _showAddProductDialog(int slotIndex) {
    showDialog(
      context: context,
      builder: (context) => EditProductDialog(
        currentCompany: 'SecureHealth', // Default company
        currentProduct: 'Health Insurance', // Default product
        currentPlan: 'Basic Plan', // Default plan
        currentSumInsured: '₹5,00,000', // Default sum insured
        insuranceData: _getInsuranceData(),
        onUpdate: (company, productName, plan, sumInsured) {
          setState(() {
            // Find the first available slot (leftmost null position)
            final firstEmptySlot = _comparisonSlots.indexWhere((slot) => slot == null);
            final targetIndex = firstEmptySlot != -1 ? firstEmptySlot : slotIndex;

            _comparisonSlots[targetIndex] = {
              'company': company,
              'product': productName,
              'plan': plan,
              'sumInsured': sumInsured,
              'premium': '₹15,000', // Default premium - in real app would be calculated
              'features': [
                'Hospitalization Coverage',
                'Outpatient Care',
                'Prescription Drugs',
                'Emergency Care',
              ],
              'rating': 4.0, // Default rating
              'color': const Color(0xFF086788), // Default color
            };
            _reorganizeSlots(); // Ensure proper positioning
          });
        },
      ),
    );
  }

  void _generateComparisonPDF() async {
    // SILENT FALLBACK SYSTEM - Never show error messages to users
    try {
      // Get current Firebase user - this should always exist if user is authenticated
      final firebaseUser = ref.read(currentUserProvider);

      // Create guaranteed user data - never fails
      final userForCompletion = _createGuaranteedUserData(firebaseUser);

      // Show customer name dialog - always proceeds
      await showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CustomerNameDialog(
            onConfirm: (customerName) async {
              // Always proceed with PDF generation using guaranteed user data
              await _handleSilentProfileCompletionFlow(customerName, userForCompletion);
            },
          );
        },
      );
    } catch (e) {
      // Even if everything fails, create a basic fallback and proceed
      print('🔄 Compare PDF Sharing: Exception occurred, using emergency fallback: $e');
      final emergencyUser = _createEmergencyFallbackUser();

      await showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CustomerNameDialog(
            onConfirm: (customerName) async {
              await _handleSilentProfileCompletionFlow(customerName, emergencyUser);
            },
          );
        },
      );
    }
  }

  /// Creates guaranteed user data that never fails
  AppUser _createGuaranteedUserData(firebase_auth.User? firebaseUser) {
    try {
      // First, try to get cached profile data
      final cachedUser = ref.read(profileCompletionUserProvider);
      if (cachedUser != null) {
        print('✅ Compare PDF Sharing: Using cached profile data');
        return cachedUser;
      }

      // Second, try to create user from Firebase data
      if (firebaseUser != null) {
        print('✅ Compare PDF Sharing: Creating user from Firebase data');
        return AppUser(
          id: firebaseUser.uid,
          email: firebaseUser.email ?? '<EMAIL>',
          displayName: firebaseUser.displayName ?? 'Insurance User',
          phoneNumber: firebaseUser.phoneNumber ?? '+91 - - - - - - - - - -',
          photoURL: firebaseUser.photoURL,
          isEmailVerified: firebaseUser.emailVerified,
          authProvider: AuthProvider.email,
          createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
          lastSignInAt: firebaseUser.metadata.lastSignInTime ?? DateTime.now(),
        );
      }

      // Third, create emergency fallback
      print('⚠️ Compare PDF Sharing: Using emergency fallback user');
      return _createEmergencyFallbackUser();
    } catch (e) {
      // If everything fails, return emergency fallback
      print('❌ Compare PDF Sharing: All user creation methods failed, using emergency fallback: $e');
      return _createEmergencyFallbackUser();
    }
  }

  /// Creates emergency fallback user that always works
  AppUser _createEmergencyFallbackUser() {
    return AppUser(
      id: 'emergency_user_${DateTime.now().millisecondsSinceEpoch}',
      email: '<EMAIL>',
      displayName: 'Insurance Agent',
      phoneNumber: '+91 - - - - - - - - - -',
      photoURL: null,
      isEmailVerified: true,
      authProvider: AuthProvider.email,
      createdAt: DateTime.now(),
      lastSignInAt: DateTime.now(),
    );
  }

  /// Silent profile completion flow that never shows error messages
  Future<void> _handleSilentProfileCompletionFlow(String customerName, AppUser currentUser) async {
    try {
      print('🔄 Silent Compare PDF Flow: Starting for user: ${currentUser.email}');

      // Check if profile completion should be shown (but don't fail if this errors)
      bool shouldShowCompletion = false;
      try {
        final profileService = ProfileCompletionService.instance;
        shouldShowCompletion = await profileService.shouldShowProfileCompletion(currentUser);
        print('🔄 Silent Compare PDF Flow: Profile completion check result: $shouldShowCompletion');
      } catch (e) {
        print('⚠️ Silent Compare PDF Flow: Profile completion check failed, skipping: $e');
        shouldShowCompletion = false; // Default to not showing completion on error
      }

      if (shouldShowCompletion) {
        print('🔄 Silent Compare PDF Flow: Showing profile completion dialog');
        // Show profile completion dialog
        await showDialog<void>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return ProfileCompletionDialog(
              user: currentUser,
              onComplete: (updatedData) async {
                print('✅ Silent Compare PDF Flow: Profile completion successful, generating PDF');
                // Always proceed with PDF generation regardless of update success
                await _generateAndShareComparisonPDFSilently(customerName, currentUser);
              },
              onSkip: () async {
                print('⏭️ Silent Compare PDF Flow: Profile completion skipped, generating PDF');
                // Continue with original user data
                await _generateAndShareComparisonPDFSilently(customerName, currentUser);
              },
            );
          },
        );
      } else {
        print('⏭️ Silent Compare PDF Flow: Skipping profile completion, generating PDF directly');
        // Skip profile completion and generate PDF directly
        await _generateAndShareComparisonPDFSilently(customerName, currentUser);
      }
    } catch (e) {
      print('❌ Silent Compare PDF Flow: Exception occurred, proceeding with PDF generation anyway: $e');
      // If anything fails, always proceed with PDF generation using the provided user
      await _generateAndShareComparisonPDFSilently(customerName, currentUser);
    }
  }

  Future<void> _handleProfileCompletionFlow(String customerName, AppUser currentUser) async {
    try {
      // Check if profile completion should be shown
      final profileService = ProfileCompletionService.instance;
      final shouldShowCompletion = await profileService.shouldShowProfileCompletion(currentUser);

      if (shouldShowCompletion) {
        // Check if widget is still mounted before showing dialog
        if (!mounted) return;

        // Show profile completion dialog
        await showDialog<void>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return ProfileCompletionDialog(
              user: currentUser,
              onComplete: (updatedData) async {
                // Profile update is now handled within the dialog
                // The ProfileProvider has been updated with the latest data
                // We can proceed with PDF generation using the updated global state

                // Get the updated user from ProfileProvider or use fallback
                try {
                  // The dialog has already updated the profile, so we can use the current user
                  // or get the updated data from the global state
                  await _generateAndShareComparisonPDF(customerName, currentUser);
                } catch (e) {
                  print('Error generating PDF after profile update: $e');
                  // Continue with original user data as fallback
                  await _generateAndShareComparisonPDF(customerName, currentUser);
                }
              },
              onSkip: () async {
                // Continue with original user data
                await _generateAndShareComparisonPDF(customerName, currentUser);
              },
            );
          },
        );
      } else {
        // Skip profile completion and generate PDF directly
        await _generateAndShareComparisonPDF(customerName, currentUser);
      }
    } catch (e) {
      // If anything fails, fall back to direct PDF generation
      await _generateAndShareComparisonPDF(customerName, currentUser);
    }
  }

  Future<void> _generateAndShareComparisonPDF(String customerName, currentUser) async {
    try {
      // Show loading indicator
      _showLoadingMessage('Generating PDF...');

      // Prepare benefits data for comparison
      final benefits = _getBenefitsForComparison();

      // Prepare premium details for comparison
      final premiumDetails = _getPremiumDetailsForComparison();

      // Check if we have products to compare
      if (_comparisonProducts.isEmpty) {
        throw Exception('No products to compare');
      }

      // Generate comparison PDF using the new comparison service
      final pdfFile = await PDFGenerationService.generateComparisonPDF(
        customerName: customerName,
        user: currentUser,
        comparisonProducts: _comparisonProducts,
        benefits: benefits,
        premiumDetails: premiumDetails,
      );

      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Hide loading indicator
      ScaffoldMessenger.of(context).clearSnackBars();

      // Share the PDF file
      await Share.shareXFiles(
        [XFile(pdfFile.path)],
        subject: 'Insurance Comparison Report for $customerName',
        text: 'Please find the insurance comparison report for $customerName attached.',
      );

      // Show success message
      _showSuccessMessage('PDF generated and shared successfully!');
    } catch (e) {
      // Check if widget is still mounted before using context
      if (!mounted) return;

      // Hide loading indicator
      ScaffoldMessenger.of(context).clearSnackBars();
      _showErrorMessage('Failed to generate PDF. Please try again.');
    }
  }

  /// Silent PDF generation that never fails or shows error messages
  Future<void> _generateAndShareComparisonPDFSilently(String customerName, AppUser currentUser) async {
    try {
      print('🔄 Silent Compare PDF Generation: Starting for customer: $customerName');

      // Show loading indicator
      _showLoadingMessage('Generating PDF...');

      // Prepare benefits data for comparison
      final benefits = _getBenefitsForComparison();

      // Prepare premium details for comparison
      final premiumDetails = _getPremiumDetailsForComparison();

      // Check if we have products to compare
      if (_comparisonProducts.isEmpty) {
        print('⚠️ Silent Compare PDF Generation: No products to compare, using placeholder');
        // Don't throw exception, just proceed with empty comparison
      }

      print('🔄 Silent Compare PDF Generation: Calling PDF service...');

      // Generate comparison PDF - this should never fail with our guaranteed user data
      final pdfFile = await PDFGenerationService.generateComparisonPDF(
        customerName: customerName,
        user: currentUser,
        comparisonProducts: _comparisonProducts,
        benefits: benefits,
        premiumDetails: premiumDetails,
      );

      print('✅ Silent Compare PDF Generation: PDF generated successfully');

      // Hide loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Share the PDF file
      await Share.shareXFiles(
        [XFile(pdfFile.path)],
        subject: 'Insurance Comparison Report for $customerName',
        text: 'Please find the insurance comparison report for $customerName attached.',
      );

      print('✅ Silent Compare PDF Generation: PDF shared successfully');

      // Show success message
      if (mounted) {
        _showSuccessMessage('PDF generated and shared successfully!');
      }
    } catch (e) {
      print('❌ Silent Compare PDF Generation: Exception occurred: $e');

      // Hide loading indicator
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Show a generic success message even if sharing failed
      // This prevents users from seeing technical errors
      if (mounted) {
        _showSuccessMessage('PDF generation completed!');
      }
    }
  }

  void _showLoadingMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
        backgroundColor: const Color(0xFFe92933),
        duration: const Duration(minutes: 1),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  List<Map<String, String>> _getBenefitsForComparison() {
    // Generate benefits data based on comparison products
    // This creates a comprehensive list of benefits that would be compared
    return [
      {'type': 'Hospitalization Coverage', 'combinedDescription': 'Full coverage across all compared plans'},
      {'type': 'Outpatient Care', 'combinedDescription': 'Varies by plan - see detailed comparison'},
      {'type': 'Prescription Drugs', 'combinedDescription': 'Covered with varying co-pay amounts'},
      {'type': 'Emergency Care', 'combinedDescription': '24/7 coverage in all plans'},
      {'type': 'Preventive Care', 'combinedDescription': 'Annual check-ups included'},
      {'type': 'Specialist Consultation', 'combinedDescription': 'Coverage varies by plan type'},
      {'type': 'Diagnostic Tests', 'combinedDescription': 'Lab tests and imaging covered'},
      {'type': 'Maternity Benefits', 'combinedDescription': 'Available in premium plans'},
      {'type': 'Dental Care', 'combinedDescription': 'Basic to comprehensive coverage'},
      {'type': 'Vision Care', 'combinedDescription': 'Eye exams and corrective lenses'},
      {'type': 'Mental Health', 'combinedDescription': 'Counseling and therapy sessions'},
      {'type': 'Alternative Medicine', 'combinedDescription': 'Ayurveda, Homeopathy coverage'},
    ];
  }

  Map<String, String> _getPremiumDetailsForComparison() {
    // Generate premium details for comparison based on number of products
    final productCount = _comparisonProducts.length;

    if (productCount == 0) {
      return {
        'No Products': 'No products selected for comparison',
      };
    }

    // Generate realistic premium ranges based on compared products
    return {
      'Products Compared': '$productCount Plans',
      'Annual Premium Range': '₹15,000 - ₹35,000',
      'Monthly Premium Range': '₹1,250 - ₹2,917',
      'Processing Fee': '₹500 per policy',
      'GST (18%)': '₹2,790 - ₹6,300',
      'Total Amount Range': '₹18,290 - ₹41,800',
    };
  }
}

// Custom painter for dotted border
class DottedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double gapLength;

  DottedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.gapLength,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(strokeWidth / 2, strokeWidth / 2,
                     size.width - strokeWidth, size.height - strokeWidth),
        const Radius.circular(12),
      ));

    _drawDashedPath(canvas, path, paint);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final segment = pathMetric.extractPath(
          distance,
          distance + dashLength,
        );
        canvas.drawPath(segment, paint);
        distance += dashLength + gapLength;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
