import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/subscription/subscription_status_card.dart';
import '../../widgets/subscription/plan_card.dart';

class SubscriptionScreen extends ConsumerStatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  ConsumerState<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends ConsumerState<SubscriptionScreen> {
  bool isAnnualBilling = false; // Track billing period

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F1F1), // Main body background
      body: Column(
        children: [
          // Sticky Header
          _buildStickyHeader(),

          // Scrollable Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),

                  // Current subscription status
                  const SubscriptionStatusCard(),

                  const SizedBox(height: 24),

                  // Available plans section
                  const Center(
                    child: Text(
                      'Choose Your Plan',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Billing period toggle
                  _buildBillingToggle(),

                  const SizedBox(height: 16),

                  // Plan cards
                  PlanCard(
                    planType: PlanType.free,
                    title: 'Free',
                    price: '₹0',
                    period: '/month',
                    originalPrice: null,
                    discount: null,
                    features: const [
                      '3 Projects',
                      'Community Support',
                      '500MB Storage',
                      'Basic Features',
                    ],
                    isCurrentPlan: false,
                    isPopular: false,
                  ),

                  const SizedBox(height: 16),

                  PlanCard(
                    planType: PlanType.pro,
                    title: 'Pro',
                    price: isAnnualBilling ? '₹5,999' : '₹599',
                    period: isAnnualBilling ? '/year' : '/month',
                    originalPrice: isAnnualBilling ? '₹7,188' : null,
                    discount: isAnnualBilling ? '₹1,189 (17% off)' : null,
                    features: const [
                      '25 Projects',
                      'Priority Support',
                      '10GB Storage',
                      'Advanced Analytics',
                      'API Access',
                      'Custom Integrations',
                    ],
                    isCurrentPlan: true,
                    isPopular: true,
                  ),

                  const SizedBox(height: 16),

                  PlanCard(
                    planType: PlanType.premium,
                    title: 'Enterprise',
                    price: isAnnualBilling ? '₹9,999' : '₹999',
                    period: isAnnualBilling ? '/year' : '/month',
                    originalPrice: isAnnualBilling ? '₹11,988' : null,
                    discount: isAnnualBilling ? '₹1,989 (17% off)' : null,
                    features: const [
                      'Unlimited Projects',
                      '24/7 Support',
                      '100GB Storage',
                      'Custom Branding',
                      'Advanced Security',
                      'Dedicated Manager',
                    ],
                    isCurrentPlan: false,
                    isPopular: false,
                  ),

                  const SizedBox(height: 32),

                  // Need Help section
                  _buildNeedHelpSection(),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStickyHeader() {
    return Container(
      color: const Color(0xFFf1f1f1),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: const BoxDecoration(
            color: Color(0xFFf1f1f1),
            boxShadow: [
              BoxShadow(
                color: Color(0x0F000000),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Back Button
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Color(0xFFe92933)),
                onPressed: () => Navigator.pop(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),

              // Title
              const Expanded(
                child: Text(
                  'Subscription',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0xFF111418),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              // Spacer to balance the layout
              const SizedBox(width: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBillingToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFe0e0e0)),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isAnnualBilling = false;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: !isAnnualBilling ? const Color(0xFFe92933) : Colors.transparent,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
                child: Text(
                  'Monthly',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: !isAnnualBilling ? Colors.white : const Color(0xFF637488),
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isAnnualBilling = true;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isAnnualBilling ? const Color(0xFFe92933) : Colors.transparent,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Annual',
                      style: TextStyle(
                        color: isAnnualBilling ? Colors.white : const Color(0xFF637488),
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFF22c55e),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'Save 17%',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNeedHelpSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFe0e0e0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Text(
              'Need Help?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),

            const SizedBox(height: 16),

            // FAQ Item 1
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Can I change my plan anytime?',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF637488),
                    height: 1.4,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // FAQ Item 2
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'What payment methods do you accept?',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'We accept all major credit cards, UPI, and net banking for Indian customers.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF637488),
                    height: 1.4,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // View all FAQs link
            GestureDetector(
              onTap: () {
                Navigator.pushNamed(context, '/faq');
              },
              child: const Text(
                'View all FAQs →',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFFe92933),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
