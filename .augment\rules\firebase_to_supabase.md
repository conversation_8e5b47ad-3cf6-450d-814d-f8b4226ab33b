

## **Product Requirements Document (PRD)**

**Feature**: Automatic Sync of Firebase Auth Users to Supabase Profiles
**App**: All About Insurance

---

### **Objective**

When a new user signs up through Firebase Auth, basic profile information (e.g., UID, email, display name, photo URL, etc.) should be automatically inserted into the `profiles` table in Supabase. Fields that are not available via Firebase (e.g., DOB) must remain empty for the user to complete later.

---

### **Key Requirements**

#### 1. **Trigger Point**
> Store all synced user data in the profiles table in Supabase.
* Any successful sign-in through:
    * Google
    * Email/password
    * Apple
    * Mobile number

This includes both new sign-ups and first-time sign-ins that result in user creation.


#### 2. **Sync Conditions**
* After sign-in:
    * Check if user already exists in profiles table (firebase_uid match).
    * If user does not exist, create a new row in Supabase profiles.
    * If user exists, no action is taken (optional: update last_sign_in_at timestamp).

#### 3. **Data to Sync**
> The data will be inserted into the 'profiles' table, with one row per user.

| Firebase Field   | Supabase Column     | Notes                              |
| ---------------- | ------------------- | ---------------------------------- |
| `uid`            | `firebase_uid`      | Unique identifier (Primary lookup) |
| `email`          | `email`             | May be null (for phone login)      |
| `emailVerified`  | `is_email_verified` | Boolean                            |
| `displayName`    | `display_name`      | May be null                        |
| `phoneNumber`    | `phone_number`      | May be null                        |
| `photoURL`       | `photo_url`         | profile image                      |
| `providerId`     | `auth_provider`     | mapped as `email`, `google`, `phone`, `apple` |
| `lastSignInTime` | `last_sign_in_at`   | date/time                           |

> Fields like `date_of_birth`, `company_name`, etc., are **not** part of Firebase and should remain `NULL`.

#### 4. **Sync Location**

* This logic should reside on your **backend** (e.g., Firebase Cloud Function, Cloud Run) or on **client-side app** after login.
* Must only insert if the user does not already exist in Supabase.

---

### **User Flow**

1. **User signs up** via Firebase Auth.
2. **App checks** if a corresponding `firebase_uid` exists in Supabase.
3. If not found:

   * Call Supabase `insert` API or query.
   * Insert only available fields into the `profiles` table.
4. **If found**: Do nothing (skip insert).

---

### **Client-Side Code Responsibility (Flutter)**

* After Firebase sign-in success:

  * Collect available data using `FirebaseAuth.instance.currentUser`.
  * Use Supabase client to call `.insert()` on `profiles` table.

```dart
final user = FirebaseAuth.instance.currentUser;

if (user != null) {
  final existingProfile = await supabase
    .from('profiles')
    .select()
    .eq('firebase_uid', user.uid)
    .maybeSingle();

  if (existingProfile == null) {
    await supabase.from('profiles').insert({
      'firebase_uid': user.uid,
      'email': user.email,
      'is_email_verified': user.emailVerified,
      'display_name': user.displayName,
      'phone_number': user.phoneNumber,
      'photo_url': user.photoURL,
      'auth_provider': user.providerData.first.providerId,
      'last_sign_in_at': DateTime.now().toIso8601String(),
    });
  }
}
```

---

### **Security Considerations**

* Ensure `insert` permissions are allowed for authenticated users in Supabase RLS.
* Use `auth.uid()` in Supabase policies to limit row access.
* Apply RLS policies on the profiles table to ensure secure access.

---

### **Testing Scenarios**

1. User signs up with email – entry created in Supabase with correct data.
2. User signs up with Google – entry includes provider, name, photo URL.
3. User signs up and logs in again – no duplicate row is created.
4. Fields not available in Firebase (DOB, company) remain `NULL`.

---








---

### 🛠️ Implementation Tasks

#### 1. **Firebase Sign-In Hook**

* Implement a post-sign-in handler in your Flutter app (e.g., inside `onAuthStateChanged` or `signInWithCredential` success).
* Call a backend endpoint or Supabase RPC to upsert profile.

#### 2. **Supabase Profile Upsert Logic**

* Build an `upsert_profile` API call (Supabase `INSERT ... ON CONFLICT DO NOTHING` or `DO UPDATE`).
* Backend logic should:

  * Use `firebase_uid` to check if profile exists.
  * If not, insert new row with above fields.
  * If yes, skip or update `last_sign_in_at`.

#### 3. **Secure Access**

* Ensure JWT from Firebase is sent as Bearer token or used to sign Supabase requests.

---

### 🔐 RLS (Row-Level Security) in Supabase

Ensure RLS policies on `profiles` table allow:

* Users to `SELECT`, `INSERT`, and `UPDATE` only their own data using:

  ```sql
  firebase_uid = auth.jwt() ->> 'sub'
  ```

---

### 📈 Tracking

Track these analytics events:

* `user_synced_to_supabase`
* `profile_completion_prompt_shown`
* `profile_fields_updated_manually`

---

### 🙏 Next Steps

1. **Implement Firebase Hook**: Add post-sign-in logic to Flutter app.
2. **Build Supabase API**: Create `upsert_profile` endpoint or RPC.
3. **Test Rigorously**: Verify all sign-in methods and edge cases.
4. **Monitor Analytics**: Ensure tracking is implemented correctly.
5. **Secure Access**: Apply RLS policies to `profiles` table.