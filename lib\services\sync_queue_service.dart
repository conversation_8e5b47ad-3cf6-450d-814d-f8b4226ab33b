import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:connectivity_plus/connectivity_plus.dart';

import '../models/sync_result.dart';
import 'firebase_supabase_sync_service.dart';
import 'analytics_service.dart';

/// Service for managing offline sync queue and failed sync operations
class SyncQueueService {
  static final SyncQueueService _instance = SyncQueueService._internal();
  factory SyncQueueService() => _instance;
  SyncQueueService._internal();

  static const String _queueKey = 'sync_queue';
  static const String _failedSyncsKey = 'failed_syncs';
  static const int _maxQueueSize = 100;
  static const int _maxRetryAttempts = 5;

  final FirebaseSupabaseSyncService _syncService = FirebaseSupabaseSyncService.instance;
  final AnalyticsService _analytics = AnalyticsService.instance;
  final Connectivity _connectivity = Connectivity();

  Timer? _processingTimer;
  bool _isProcessing = false;

  /// Initialize the sync queue service
  Future<void> initialize() async {
    // Start periodic processing of the queue
    _startPeriodicProcessing();
    
    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);
  }

  /// Add a sync operation to the queue
  Future<void> addToQueue({
    required String firebaseUid,
    required String email,
    String? displayName,
    String? phoneNumber,
    String? photoURL,
    required bool emailVerified,
    required String authProvider,
    DateTime? creationTime,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(_queueKey) ?? '[]';
      final queue = List<Map<String, dynamic>>.from(json.decode(queueJson));

      // Check if user is already in queue
      final existingIndex = queue.indexWhere((item) => item['firebaseUid'] == firebaseUid);
      
      final queueItem = {
        'firebaseUid': firebaseUid,
        'email': email,
        'displayName': displayName,
        'phoneNumber': phoneNumber,
        'photoURL': photoURL,
        'emailVerified': emailVerified,
        'authProvider': authProvider,
        'creationTime': creationTime?.toIso8601String(),
        'addedAt': DateTime.now().toIso8601String(),
        'retryCount': 0,
      };

      if (existingIndex >= 0) {
        // Update existing item
        queue[existingIndex] = queueItem;
      } else {
        // Add new item
        queue.add(queueItem);
      }

      // Limit queue size
      if (queue.length > _maxQueueSize) {
        queue.removeRange(0, queue.length - _maxQueueSize);
      }

      await prefs.setString(_queueKey, json.encode(queue));
      
      // Track queue addition
      await _analytics.trackSyncQueuedForLater(
        firebaseUid: firebaseUid,
        authProvider: authProvider,
        queueSize: queue.length,
      );

    } catch (e) {
      print('Error adding to sync queue: $e');
    }
  }

  /// Process the sync queue
  Future<void> processQueue() async {
    if (_isProcessing) return;
    
    _isProcessing = true;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(_queueKey) ?? '[]';
      final queue = List<Map<String, dynamic>>.from(json.decode(queueJson));

      if (queue.isEmpty) {
        _isProcessing = false;
        return;
      }

      // Check connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        _isProcessing = false;
        return;
      }

      final processedItems = <int>[];
      final failedItems = <Map<String, dynamic>>[];

      for (int i = 0; i < queue.length; i++) {
        final item = queue[i];
        
        try {
          // Create a mock Firebase user for sync
          final mockUser = _createMockFirebaseUser(item);
          
          // Attempt sync
          final result = await _syncService.syncUserToSupabase(mockUser);
          
          if (result.isSuccess) {
            processedItems.add(i);
            
            // Track successful queue processing
            await _analytics.trackSyncQueueProcessed(
              firebaseUid: item['firebaseUid'],
              authProvider: item['authProvider'],
              retryCount: item['retryCount'] ?? 0,
              isSuccess: true,
            );
          } else {
            // Increment retry count
            item['retryCount'] = (item['retryCount'] ?? 0) + 1;
            item['lastError'] = result.errorMessage;
            item['lastAttempt'] = DateTime.now().toIso8601String();
            
            if (item['retryCount'] >= _maxRetryAttempts) {
              // Move to failed syncs
              failedItems.add(item);
              processedItems.add(i);
              
              await _analytics.trackSyncQueueProcessed(
                firebaseUid: item['firebaseUid'],
                authProvider: item['authProvider'],
                retryCount: item['retryCount'],
                isSuccess: false,
                errorType: result.errorType.toString(),
              );
            }
          }
        } catch (e) {
          // Handle processing error
          item['retryCount'] = (item['retryCount'] ?? 0) + 1;
          item['lastError'] = e.toString();
          item['lastAttempt'] = DateTime.now().toIso8601String();
          
          if (item['retryCount'] >= _maxRetryAttempts) {
            failedItems.add(item);
            processedItems.add(i);
          }
        }
      }

      // Remove processed items from queue (in reverse order to maintain indices)
      for (int i = processedItems.length - 1; i >= 0; i--) {
        queue.removeAt(processedItems[i]);
      }

      // Save updated queue
      await prefs.setString(_queueKey, json.encode(queue));

      // Save failed syncs
      if (failedItems.isNotEmpty) {
        await _addToFailedSyncs(failedItems);
      }

    } catch (e) {
      print('Error processing sync queue: $e');
    } finally {
      _isProcessing = false;
    }
  }

  /// Get current queue status
  Future<Map<String, dynamic>> getQueueStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueJson = prefs.getString(_queueKey) ?? '[]';
      final failedJson = prefs.getString(_failedSyncsKey) ?? '[]';
      
      final queue = List<Map<String, dynamic>>.from(json.decode(queueJson));
      final failed = List<Map<String, dynamic>>.from(json.decode(failedJson));

      return {
        'queueSize': queue.length,
        'failedSyncs': failed.length,
        'isProcessing': _isProcessing,
        'lastProcessed': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'queueSize': 0,
        'failedSyncs': 0,
        'isProcessing': false,
      };
    }
  }

  /// Clear the sync queue
  Future<void> clearQueue() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_queueKey);
    } catch (e) {
      print('Error clearing sync queue: $e');
    }
  }

  /// Get failed syncs
  Future<List<Map<String, dynamic>>> getFailedSyncs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedJson = prefs.getString(_failedSyncsKey) ?? '[]';
      return List<Map<String, dynamic>>.from(json.decode(failedJson));
    } catch (e) {
      print('Error getting failed syncs: $e');
      return [];
    }
  }

  /// Retry a failed sync
  Future<SyncResult> retryFailedSync(String firebaseUid) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedJson = prefs.getString(_failedSyncsKey) ?? '[]';
      final failed = List<Map<String, dynamic>>.from(json.decode(failedJson));

      final itemIndex = failed.indexWhere((item) => item['firebaseUid'] == firebaseUid);
      if (itemIndex < 0) {
        return SyncResult.validationFailure(
          operationType: SyncOperationType.dataSync,
          errorMessage: 'Failed sync not found for Firebase UID: $firebaseUid',
          firebaseUid: firebaseUid,
        );
      }

      final item = failed[itemIndex];
      final mockUser = _createMockFirebaseUser(item);
      
      // Attempt sync
      final result = await _syncService.retrySyncWithBackoff(mockUser);
      
      if (result.isSuccess) {
        // Remove from failed syncs
        failed.removeAt(itemIndex);
        await prefs.setString(_failedSyncsKey, json.encode(failed));
        
        await _analytics.trackSyncManualRetry(
          firebaseUid: firebaseUid,
          authProvider: item['authProvider'],
          isSuccess: true,
        );
      } else {
        await _analytics.trackSyncManualRetry(
          firebaseUid: firebaseUid,
          authProvider: item['authProvider'],
          isSuccess: false,
          errorType: result.errorType.toString(),
        );
      }

      return result;
    } catch (e) {
      return SyncResult.failure(
        operationType: SyncOperationType.dataSync,
        errorType: SyncErrorType.unknownError,
        errorMessage: 'Error retrying failed sync: $e',
        message: 'Manual retry failed',
        firebaseUid: firebaseUid,
      );
    }
  }

  /// Start periodic processing of the queue
  void _startPeriodicProcessing() {
    _processingTimer?.cancel();
    _processingTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      processQueue();
    });
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    if (result != ConnectivityResult.none) {
      // Connection restored, process queue
      processQueue();
    }
  }

  /// Add items to failed syncs storage
  Future<void> _addToFailedSyncs(List<Map<String, dynamic>> items) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final failedJson = prefs.getString(_failedSyncsKey) ?? '[]';
      final failed = List<Map<String, dynamic>>.from(json.decode(failedJson));

      failed.addAll(items);
      await prefs.setString(_failedSyncsKey, json.encode(failed));
    } catch (e) {
      print('Error adding to failed syncs: $e');
    }
  }

  /// Create a mock Firebase user from queue item data
  firebase_auth.User _createMockFirebaseUser(Map<String, dynamic> item) {
    // Note: This is a simplified approach. In a real implementation,
    // you might need a more sophisticated way to recreate Firebase user objects
    // or store the necessary data differently.
    throw UnimplementedError('Mock Firebase user creation needs implementation');
  }

  /// Dispose resources
  void dispose() {
    _processingTimer?.cancel();
  }
}
