import 'sync_result.dart';

/// Legacy result class for profile creation operations
/// This class is maintained for backward compatibility with existing tests
/// New code should use SyncResult instead
@Deprecated('Use SyncResult instead')
class ProfileCreationResult {
  final bool isSuccess;
  final String message;
  final String? errorMessage;
  final String? profileId;
  final String? firebaseUid;
  final String? email;
  final String? displayName;
  final String? authProvider;
  final DateTime? timestamp;
  final Map<String, dynamic>? additionalData;

  ProfileCreationResult._({
    required this.isSuccess,
    required this.message,
    this.errorMessage,
    this.profileId,
    this.firebaseUid,
    this.email,
    this.displayName,
    this.authProvider,
    this.timestamp,
    this.additionalData,
  });

  /// Create a successful profile creation result
  factory ProfileCreationResult.success({
    required String message,
    String? profileId,
    String? firebaseUid,
    String? email,
    String? displayName,
    String? authProvider,
    Map<String, dynamic>? additionalData,
  }) {
    return ProfileCreationResult._(
      isSuccess: true,
      message: message,
      profileId: profileId,
      firebaseUid: firebaseUid,
      email: email,
      displayName: displayName,
      authProvider: authProvider,
      timestamp: DateTime.now(),
      additionalData: additionalData,
    );
  }

  /// Create a failed profile creation result
  factory ProfileCreationResult.failure({
    required String message,
    String? errorMessage,
    String? firebaseUid,
    String? email,
    Map<String, dynamic>? additionalData,
  }) {
    return ProfileCreationResult._(
      isSuccess: false,
      message: message,
      errorMessage: errorMessage,
      firebaseUid: firebaseUid,
      email: email,
      timestamp: DateTime.now(),
      additionalData: additionalData,
    );
  }

  /// Convert from SyncResult to ProfileCreationResult for backward compatibility
  factory ProfileCreationResult.fromSyncResult(SyncResult syncResult) {
    return ProfileCreationResult._(
      isSuccess: syncResult.isSuccess,
      message: syncResult.message,
      errorMessage: syncResult.errorMessage,
      profileId: syncResult.profileId,
      firebaseUid: syncResult.firebaseUid,
      email: syncResult.email,
      displayName: syncResult.displayName,
      authProvider: syncResult.authProvider,
      timestamp: syncResult.syncTimestamp,
      additionalData: syncResult.additionalData,
    );
  }

  /// Convert to SyncResult for forward compatibility
  SyncResult toSyncResult() {
    if (isSuccess) {
      return SyncResult.success(
        operationType: SyncOperationType.profileCreation,
        message: message,
        profileId: profileId,
        firebaseUid: firebaseUid,
        email: email,
        displayName: displayName,
        authProvider: authProvider,
        additionalData: additionalData,
      );
    } else {
      return SyncResult.failure(
        operationType: SyncOperationType.profileCreation,
        errorType: SyncErrorType.unknownError,
        message: message,
        errorMessage: errorMessage ?? 'Unknown error',
        firebaseUid: firebaseUid,
        additionalData: additionalData,
      );
    }
  }

  @override
  String toString() {
    return 'ProfileCreationResult(isSuccess: $isSuccess, message: $message, profileId: $profileId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProfileCreationResult &&
        other.isSuccess == isSuccess &&
        other.message == message &&
        other.errorMessage == errorMessage &&
        other.profileId == profileId &&
        other.firebaseUid == firebaseUid;
  }

  @override
  int get hashCode {
    return Object.hash(
      isSuccess,
      message,
      errorMessage,
      profileId,
      firebaseUid,
    );
  }
}
