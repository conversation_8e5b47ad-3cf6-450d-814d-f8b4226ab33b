-- Migration: Comprehensive Profile Updates
-- Created: 2024-08-03
-- Description: Apply all requested profile creation changes to live database

-- =============================================================================
-- PART 1: UPDATE NATIVE_LANGUAGE FIELD
-- =============================================================================

-- Remove default value from native_language column
ALTER TABLE profiles 
ALTER COLUMN native_language DROP DEFAULT;

-- Update existing users with 'Hindi' to NULL (optional - uncomment if desired)
-- UPDATE profiles 
-- SET native_language = NULL 
-- WHERE native_language = 'Hindi';

-- Add comment for documentation
COMMENT ON COLUMN profiles.native_language IS 'User preferred language - NULL by default, set during onboarding';

-- =============================================================================
-- PART 2: UPDATE SUBSCRIPTION PLANS (basic → Free)
-- =============================================================================

-- Step 1: Add "Free" to CHECK constraint (keeping "basic" temporarily)
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS profiles_subscription_plan_check;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_subscription_plan_check 
CHECK (subscription_plan IN ('Free', 'basic', 'pro', 'enterprise'));

-- Step 2: Update existing users with "basic" plan to "Free" plan
UPDATE profiles 
SET 
    subscription_plan = 'Free',
    updated_at = NOW()
WHERE subscription_plan = 'basic';

-- Step 3: Update the default value for new users
ALTER TABLE profiles 
ALTER COLUMN subscription_plan SET DEFAULT 'Free';

-- Step 4: Remove "basic" from CHECK constraint (final cleanup)
ALTER TABLE profiles 
DROP CONSTRAINT profiles_subscription_plan_check;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_subscription_plan_check 
CHECK (subscription_plan IN ('Free', 'pro', 'enterprise'));

-- Update table comment
COMMENT ON COLUMN profiles.subscription_plan IS 'Current subscription tier: Free (default), pro, or enterprise';

-- =============================================================================
-- PART 3: UPDATE PROFILE CREATION FUNCTIONS
-- =============================================================================

-- Update create_user_profile function to remove role assignment
CREATE OR REPLACE FUNCTION public.create_user_profile(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_auth_provider TEXT DEFAULT 'email',
    p_is_email_verified BOOLEAN DEFAULT FALSE
)
RETURNS UUID AS $$
DECLARE
    new_profile_id UUID;
    cleaned_phone TEXT;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RAISE EXCEPTION 'Firebase UID is required and cannot be empty';
    END IF;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    -- Insert new profile with updated defaults (no role assignment)
    INSERT INTO public.profiles (
        firebase_uid,
        email,
        display_name,
        phone_number,
        photo_url,
        auth_provider,
        is_email_verified,
        -- role_id intentionally omitted - will remain NULL for manual assignment
        -- native_language intentionally omitted - will remain NULL
        -- App preferences with sensible defaults
        push_notifications_enabled,
        email_notifications_enabled,
        location_services_enabled,
        analytics_enabled,
        dark_mode_enabled,
        biometric_enabled,
        auto_download_enabled,
        profile_completion_dont_ask_again,
        -- Subscription defaults
        subscription_plan,
        subscription_status,
        subscription_start_date
    ) VALUES (
        p_firebase_uid,
        p_email,
        p_display_name,
        cleaned_phone,
        p_photo_url,
        p_auth_provider,
        p_is_email_verified,
        -- role_id omitted - remains NULL
        -- native_language omitted - remains NULL
        -- Default app preferences
        TRUE,  -- push_notifications_enabled
        FALSE, -- email_notifications_enabled
        TRUE,  -- location_services_enabled
        FALSE, -- analytics_enabled
        FALSE, -- dark_mode_enabled
        TRUE,  -- biometric_enabled
        FALSE, -- auto_download_enabled
        FALSE, -- profile_completion_dont_ask_again
        -- Default subscription (now 'Free')
        'Free',
        'active',
        NOW()
    ) RETURNING id INTO new_profile_id;

    RETURN new_profile_id;

EXCEPTION
    WHEN unique_violation THEN
        -- Profile already exists, return existing profile ID
        SELECT id INTO new_profile_id
        FROM public.profiles
        WHERE firebase_uid = p_firebase_uid;
        RETURN new_profile_id;
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE EXCEPTION 'Failed to create profile for user %: %', p_firebase_uid, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- PART 4: UPDATE SYNC FUNCTION
-- =============================================================================

-- Update sync_firebase_user_data function to use new defaults
CREATE OR REPLACE FUNCTION public.sync_firebase_user_data(
    p_firebase_uid TEXT,
    p_email TEXT DEFAULT NULL,
    p_display_name TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_photo_url TEXT DEFAULT NULL,
    p_is_email_verified BOOLEAN DEFAULT NULL,
    p_auth_provider TEXT DEFAULT NULL,
    p_last_sign_in_at TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    profile_exists BOOLEAN;
    cleaned_phone TEXT;
BEGIN
    -- Validate required parameters
    IF p_firebase_uid IS NULL OR p_firebase_uid = '' THEN
        RAISE EXCEPTION 'Firebase UID is required and cannot be empty';
    END IF;

    -- Check if profile exists
    SELECT EXISTS(
        SELECT 1 FROM public.profiles 
        WHERE firebase_uid = p_firebase_uid 
        AND deleted_at IS NULL
    ) INTO profile_exists;

    -- Clean phone number if provided
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        cleaned_phone := public.clean_phone_number(p_phone_number);
    END IF;

    IF profile_exists THEN
        -- Update existing profile with Firebase data
        UPDATE public.profiles SET
            email = COALESCE(p_email, email),
            display_name = COALESCE(p_display_name, display_name),
            phone_number = COALESCE(cleaned_phone, phone_number),
            photo_url = COALESCE(p_photo_url, photo_url),
            is_email_verified = COALESCE(p_is_email_verified, is_email_verified),
            auth_provider = COALESCE(p_auth_provider, auth_provider),
            last_sign_in_at = COALESCE(p_last_sign_in_at::TIMESTAMPTZ, NOW()),
            updated_at = NOW()
        WHERE firebase_uid = p_firebase_uid;
    ELSE
        -- Create new profile using updated function (no role assignment)
        PERFORM public.create_user_profile(
            p_firebase_uid,
            p_email,
            p_display_name,
            cleaned_phone,
            p_photo_url,
            COALESCE(p_auth_provider, 'email'),
            COALESCE(p_is_email_verified, FALSE)
        );
        
        -- Update last_sign_in_at if provided for new profile
        IF p_last_sign_in_at IS NOT NULL THEN
            UPDATE public.profiles SET
                last_sign_in_at = p_last_sign_in_at::TIMESTAMPTZ
            WHERE firebase_uid = p_firebase_uid;
        END IF;
    END IF;

    RETURN TRUE;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to sync user data for %: %', p_firebase_uid, SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- PART 5: VERIFICATION AND LOGGING
-- =============================================================================

-- Log the migration results
DO $$
DECLARE
    free_plan_count INTEGER;
    null_role_count INTEGER;
    null_language_count INTEGER;
BEGIN
    -- Count users with Free plan
    SELECT COUNT(*) INTO free_plan_count 
    FROM profiles 
    WHERE subscription_plan = 'Free';
    
    -- Count users with NULL role_id
    SELECT COUNT(*) INTO null_role_count 
    FROM profiles 
    WHERE role_id IS NULL;
    
    -- Count users with NULL native_language
    SELECT COUNT(*) INTO null_language_count 
    FROM profiles 
    WHERE native_language IS NULL;
    
    RAISE NOTICE 'Migration completed successfully:';
    RAISE NOTICE '- % users now have "Free" subscription plan', free_plan_count;
    RAISE NOTICE '- % users have NULL role_id', null_role_count;
    RAISE NOTICE '- % users have NULL native_language', null_language_count;
END $$;
