# Onboarding Image Specifications

## Overview

This document defines the technical specifications for onboarding screen image placeholders and actual images in the All About Insurance (AAI) Flutter app. These specifications ensure consistent visual presentation across different device types and screen densities while maintaining optimal performance.

## Design Requirements

### Layout Constraints
- **Height**: 80% of available screen height (excluding header, text, and navigation areas)
- **Width**: Full screen width with responsive horizontal padding
- **Aspect Ratio**: Flexible, adapts to content while maintaining proportions
- **Background**: Light theme colors only (no dark mode adaptation)

### Screen Size Calculations

#### Common Mobile Device Resolutions
| Device Type | Resolution | Available Height* | Image Height (80%) | Recommended Dimensions |
|-------------|------------|-------------------|-------------------|----------------------|
| Phone (Small) | 360×640 | ~480px | ~384px | 360×384px |
| Phone (Medium) | 414×896 | ~680px | ~544px | 414×544px |
| Phone (Large) | 428×926 | ~700px | ~560px | 428×560px |
| Tablet (Small) | 768×1024 | ~780px | ~624px | 768×624px |
| Tablet (Large) | 1024×1366 | ~1040px | ~832px | 1024×832px |

*Available height = Total height - (Status bar + Header + Text area + Navigation + Padding)

## Technical Specifications

### Image Format Requirements

#### Primary Format: PNG
- **Current Usage**: `assets/logo/aai-logo.png` (31KB)
- **Advantages**: 
  - Excellent quality with transparency support
  - Universal Flutter support
  - No additional dependencies
- **Recommended Settings**:
  - Color depth: 24-bit (RGB) or 32-bit (RGBA with transparency)
  - Compression: PNG-8 for simple graphics, PNG-24 for complex images
  - Target file size: 50-200KB per image

#### Alternative Format: SVG
- **Current Availability**: `assets/logo/app-logo.svg`
- **Advantages**:
  - Infinite scalability without quality loss
  - Smaller file sizes for simple graphics
  - Vector-based precision
- **Requirements**:
  - flutter_svg package dependency
  - Additional bundle size consideration
  - Potential rendering performance impact on older devices

#### Optimization Recommendations
- **WebP Format**: Consider for future optimization (smaller file sizes)
- **Multiple Densities**: Provide @2x, @3x variants for PNG if needed
- **Compression**: Use tools like TinyPNG or ImageOptim for size reduction

### Responsive Design Specifications

#### Horizontal Padding
```dart
// From ResponsiveHelper.getHorizontalPadding()
- Mobile: 16px each side
- Tablet: 24px each side
- Desktop: 32px each side
```

#### Container Specifications
```dart
// OnboardingImage widget implementation
Expanded(
  flex: 10, // Takes ~91% of available vertical space
  child: Container(
    width: double.infinity, // Full width minus padding
    color: Color(0xFFF5F5F5), // Fixed light background
    // Image content here
  ),
)
```

## Content Guidelines

### Image Placeholder Styling
- **Background Color**: `#F5F5F5` (light grey, fixed)
- **Icon Color**: `#9E9E9E` with 60% opacity
- **Icon Size**: 80px
- **Text Color**: `#9E9E9E`
- **Text Style**: 14px, normal weight
- **Text Content**: "Image Placeholder"

### Actual Image Requirements
- **Content Type**: Insurance-related illustrations or photos
- **Style**: Professional, trustworthy, modern
- **Color Scheme**: Compatible with AAI brand colors
  - Primary: `#e92933` (AAI red)
  - Background: `#f1f1f1` (light grey)
  - Text: `#1a1a1a` (dark grey/black)
- **Transparency**: Support for transparent backgrounds if needed

## Implementation Examples

### Optimal Dimensions by Use Case

#### Welcome/Introduction Screen
- **Recommended**: 414×500px (standard phone)
- **Content**: Company logo, welcome illustration
- **Aspect Ratio**: ~4:5 (portrait orientation)

#### Feature Explanation Screens
- **Recommended**: 414×450px
- **Content**: Feature demonstrations, UI mockups
- **Aspect Ratio**: ~11:12 (slightly portrait)

#### Benefits/Value Proposition
- **Recommended**: 414×400px
- **Content**: Charts, graphs, benefit illustrations
- **Aspect Ratio**: ~1:1 (square-ish)

#### Call-to-Action Screen
- **Recommended**: 414×350px
- **Content**: Action-oriented graphics, buttons preview
- **Aspect Ratio**: ~6:5 (slightly landscape)

## Performance Considerations

### File Size Targets
- **Individual Image**: 50-200KB
- **Total Onboarding Assets**: <1MB
- **Loading Time**: <500ms on 3G connection

### Memory Usage
- **Decoded Size**: Width × Height × 4 bytes (RGBA)
- **Example**: 414×500px = ~800KB in memory
- **Recommendation**: Monitor memory usage on low-end devices

### Caching Strategy
- **Asset Bundle**: Images included in app bundle (immediate availability)
- **Preloading**: Consider preloading next screen images
- **Memory Management**: Dispose unused images promptly

## Quality Assurance

### Testing Requirements
- **Device Testing**: Test on minimum 3 different screen sizes
- **Orientation**: Verify portrait mode display
- **Performance**: Monitor loading times and memory usage
- **Visual**: Ensure consistent appearance across devices

### Accessibility
- **Alt Text**: Provide meaningful descriptions for screen readers
- **Contrast**: Ensure sufficient contrast for visual elements
- **Scaling**: Support system font scaling settings

## Migration Path

### Current State
- Using placeholder containers with icons
- Fixed light theme colors implemented
- Responsive layout structure in place

### Implementation Steps
1. **Design Phase**: Create actual images following specifications
2. **Asset Preparation**: Optimize images for web and mobile
3. **Integration**: Replace placeholder with actual Image.asset() widgets
4. **Testing**: Verify performance and visual quality
5. **Optimization**: Fine-tune based on real-world usage

### Fallback Strategy
- Maintain current placeholder implementation as fallback
- Implement error handling for failed image loads
- Provide offline-capable experience

## Maintenance

### Regular Reviews
- **Quarterly**: Review file sizes and performance metrics
- **Annually**: Update images for brand consistency
- **As Needed**: Optimize for new device form factors

### Version Control
- Store source files (PSD, AI, Sketch) separately
- Maintain changelog for image updates
- Tag releases with asset version numbers

This specification ensures consistent, performant, and visually appealing onboarding experiences across all supported devices while maintaining the AAI brand identity and technical requirements.
