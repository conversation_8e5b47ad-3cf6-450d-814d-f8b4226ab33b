import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';
part 'user_profile.g.dart';

/// Comprehensive user profile model that maps to Supabase profiles table
@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String id,
    required String firebaseUid,
    String? email,
    String? displayName,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? photoUrl,
    @Default(false) bool isEmailVerified,
    @Default('email') String authProvider,
    String? role,
    
    // Personal information
    DateTime? dateOfBirth,
    String? gender,
    String? nativeLanguage,
    
    // Address information
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    @Default('India') String country,
    
    // Professional information
    String? occupation,
    String? companyName,
    double? annualIncome,
    
    // App preferences
    @Default(true) bool pushNotificationsEnabled,
    @Default(false) bool emailNotificationsEnabled,
    @Default(true) bool smsNotificationsEnabled,
    @Default(true) bool locationServicesEnabled,
    @Default(false) bool analyticsEnabled,
    @Default(false) bool darkModeEnabled,
    @Default(true) bool biometricEnabled,
    @Default(false) bool autoDownloadEnabled,
    @Default(false) bool profileCompletionDontAskAgain,
    
    // Subscription information
    @Default('Free') String subscriptionPlan,
    @Default('active') String subscriptionStatus,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
    
    // Profile completion and onboarding
    @Default(0) int profileCompletionPercentage,
    @Default(false) bool onboardingCompleted,
    @Default(false) bool termsAccepted,
    @Default(false) bool privacyPolicyAccepted,
    
    // Sync tracking
    @Default(1) int syncVersion,
    DateTime? lastSignInAt,
    DateTime? lastActiveAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) => _$UserProfileFromJson(json);

  /// Create UserProfile from Supabase database response
  factory UserProfile.fromSupabase(Map<String, dynamic> data) {
    return UserProfile(
      id: data['id'] as String,
      firebaseUid: data['firebase_uid'] as String,
      email: data['email'] as String?,
      displayName: data['display_name'] as String?,
      firstName: data['first_name'] as String?,
      lastName: data['last_name'] as String?,
      phoneNumber: data['phone_number'] as String?,
      photoUrl: data['photo_url'] as String?,
      isEmailVerified: data['is_email_verified'] as bool? ?? false,
      authProvider: data['auth_provider'] as String? ?? 'email',
      role: data['role'] as String?,
      
      // Personal information
      dateOfBirth: data['date_of_birth'] != null 
          ? DateTime.parse(data['date_of_birth'] as String) 
          : null,
      gender: data['gender'] as String?,
      nativeLanguage: data['native_language'] as String?,
      
      // Address information
      addressLine1: data['address_line_1'] as String?,
      addressLine2: data['address_line_2'] as String?,
      city: data['city'] as String?,
      state: data['state'] as String?,
      postalCode: data['postal_code'] as String?,
      country: data['country'] as String? ?? 'India',
      
      // Professional information
      occupation: data['occupation'] as String?,
      companyName: data['company_name'] as String?,
      annualIncome: data['annual_income'] != null 
          ? double.parse(data['annual_income'].toString()) 
          : null,
      
      // App preferences
      pushNotificationsEnabled: data['push_notifications_enabled'] as bool? ?? true,
      emailNotificationsEnabled: data['email_notifications_enabled'] as bool? ?? false,
      smsNotificationsEnabled: data['sms_notifications_enabled'] as bool? ?? true,
      locationServicesEnabled: data['location_services_enabled'] as bool? ?? true,
      analyticsEnabled: data['analytics_enabled'] as bool? ?? false,
      darkModeEnabled: data['dark_mode_enabled'] as bool? ?? false,
      biometricEnabled: data['biometric_enabled'] as bool? ?? true,
      autoDownloadEnabled: data['auto_download_enabled'] as bool? ?? false,
      profileCompletionDontAskAgain: data['profile_completion_dont_ask_again'] as bool? ?? false,
      
      // Subscription information
      subscriptionPlan: data['subscription_plan'] as String? ?? 'Free',
      subscriptionStatus: data['subscription_status'] as String? ?? 'active',
      subscriptionStartDate: data['subscription_start_date'] != null 
          ? DateTime.parse(data['subscription_start_date'] as String) 
          : null,
      subscriptionEndDate: data['subscription_end_date'] != null 
          ? DateTime.parse(data['subscription_end_date'] as String) 
          : null,
      
      // Profile completion and onboarding
      profileCompletionPercentage: data['profile_completion_percentage'] as int? ?? 0,
      onboardingCompleted: data['onboarding_completed'] as bool? ?? false,
      termsAccepted: data['terms_accepted'] as bool? ?? false,
      privacyPolicyAccepted: data['privacy_policy_accepted'] as bool? ?? false,
      
      // Sync tracking
      syncVersion: data['sync_version'] as int? ?? 1,
      lastSignInAt: data['last_sign_in_at'] != null 
          ? DateTime.parse(data['last_sign_in_at'] as String) 
          : null,
      lastActiveAt: data['last_active_at'] != null 
          ? DateTime.parse(data['last_active_at'] as String) 
          : null,
      createdAt: data['created_at'] != null 
          ? DateTime.parse(data['created_at'] as String) 
          : null,
      updatedAt: data['updated_at'] != null 
          ? DateTime.parse(data['updated_at'] as String) 
          : null,
    );
  }
}

/// Extension methods for UserProfile
extension UserProfileExtensions on UserProfile {
  /// Convert to map for Supabase updates (only include non-null values)
  Map<String, dynamic> toSupabaseUpdate() {
    final Map<String, dynamic> data = {};

    if (displayName != null) data['p_display_name'] = displayName;
    if (firstName != null) data['p_first_name'] = firstName;
    if (lastName != null) data['p_last_name'] = lastName;
    if (email != null) data['p_email'] = email;
    if (phoneNumber != null) data['p_phone_number'] = phoneNumber;
    if (dateOfBirth != null) data['p_date_of_birth'] = dateOfBirth!.toIso8601String().split('T')[0];
    if (gender != null) data['p_gender'] = gender;
    if (nativeLanguage != null) data['p_native_language'] = nativeLanguage;
    if (addressLine1 != null) data['p_address_line_1'] = addressLine1;
    if (addressLine2 != null) data['p_address_line_2'] = addressLine2;
    if (city != null) data['p_city'] = city;
    if (state != null) data['p_state'] = state;
    if (postalCode != null) data['p_postal_code'] = postalCode;
    if (country != 'India') data['p_country'] = country; // Only include if different from default
    if (occupation != null) data['p_occupation'] = occupation;
    if (companyName != null) data['p_company_name'] = companyName;
    if (annualIncome != null) data['p_annual_income'] = annualIncome;

    // Always include sync version for optimistic locking
    data['p_sync_version'] = syncVersion;

    return data;
  }
}

/// Profile update request model for partial updates
@freezed
class ProfileUpdateRequest with _$ProfileUpdateRequest {
  const factory ProfileUpdateRequest({
    String? displayName,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    DateTime? dateOfBirth,
    String? gender,
    String? nativeLanguage,
    String? addressLine1,
    String? addressLine2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    String? occupation,
    String? companyName,
    double? annualIncome,
    bool? profileCompletionDontAskAgain,
    int? syncVersion,
  }) = _ProfileUpdateRequest;

  factory ProfileUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$ProfileUpdateRequestFromJson(json);
}

/// Extension methods for ProfileUpdateRequest
extension ProfileUpdateRequestExtensions on ProfileUpdateRequest {
  /// Convert to Supabase RPC parameters
  Map<String, dynamic> toSupabaseParams(String firebaseUid) {
    final Map<String, dynamic> params = {'p_firebase_uid': firebaseUid};

    if (displayName != null) params['p_display_name'] = displayName;
    if (firstName != null) params['p_first_name'] = firstName;
    if (lastName != null) params['p_last_name'] = lastName;
    if (email != null) params['p_email'] = email;
    if (phoneNumber != null) params['p_phone_number'] = phoneNumber;
    if (dateOfBirth != null) params['p_date_of_birth'] = dateOfBirth!.toIso8601String().split('T')[0];
    if (gender != null) params['p_gender'] = gender;
    if (nativeLanguage != null) params['p_native_language'] = nativeLanguage;
    if (addressLine1 != null) params['p_address_line_1'] = addressLine1;
    if (addressLine2 != null) params['p_address_line_2'] = addressLine2;
    if (city != null) params['p_city'] = city;
    if (state != null) params['p_state'] = state;
    if (postalCode != null) params['p_postal_code'] = postalCode;
    if (country != null) params['p_country'] = country;
    if (occupation != null) params['p_occupation'] = occupation;
    if (companyName != null) params['p_company_name'] = companyName;
    if (annualIncome != null) params['p_annual_income'] = annualIncome;
    if (profileCompletionDontAskAgain != null) params['p_profile_completion_dont_ask_again'] = profileCompletionDontAskAgain;
    if (syncVersion != null) params['p_sync_version'] = syncVersion;

    return params;
  }
}

/// Profile update result
@freezed
class ProfileUpdateResult with _$ProfileUpdateResult {
  const factory ProfileUpdateResult.success({
    required UserProfile profile,
    required String message,
    required int newSyncVersion,
    required int completionPercentage,
  }) = ProfileUpdateSuccess;

  const factory ProfileUpdateResult.error({
    required String error,
    required String message,
    String? errorCode,
    int? currentVersion,
    int? providedVersion,
  }) = ProfileUpdateError;

  factory ProfileUpdateResult.fromJson(Map<String, dynamic> json) => 
      _$ProfileUpdateResultFromJson(json);
}
