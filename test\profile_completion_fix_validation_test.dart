import 'package:flutter_test/flutter_test.dart';
import '../lib/models/user_model.dart';
import '../lib/services/profile_completion_service.dart';

void main() {
  group('Profile Completion Fix Validation', () {
    late ProfileCompletionService profileService;

    setUp(() {
      profileService = ProfileCompletionService.instance;
    });

    group('Google Auth Users', () {
      test('complete Supabase profile should not show completion dialog', () {
        final googleUserComplete = AppUser(
          id: 'google-user-complete',
          email: '<EMAIL>',
          displayName: 'Google User',
          phoneNumber: '+919876543210',
          photoURL: 'https://example.com/custom-photo.jpg', // Custom uploaded photo
          isEmailVerified: true,
          authProvider: AuthProvider.google,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(googleUserComplete);
        
        // Should only have company logo missing (not implemented yet)
        expect(missingFields.length, equals(1));
        expect(missingFields, contains('companyLogo'));
        expect(missingFields, isNot(contains('email')));
        expect(missingFields, isNot(contains('displayName')));
        expect(missingFields, isNot(contains('phoneNumber')));
      });

      test('incomplete Supabase profile should show completion dialog for missing fields only', () {
        final googleUserIncomplete = AppUser(
          id: 'google-user-incomplete',
          email: '<EMAIL>', // Has email from Google
          displayName: 'Google User', // Has name from Google
          phoneNumber: null, // Missing phone in Supabase
          photoURL: 'https://lh3.googleusercontent.com/default', // Default Google photo
          isEmailVerified: true,
          authProvider: AuthProvider.google,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(googleUserIncomplete);
        
        // Should detect missing phone and profile image (default Google image)
        expect(missingFields, contains('profileImage')); // Default Google image
        expect(missingFields, contains('phoneNumber')); // Missing in Supabase
        expect(missingFields, contains('companyLogo')); // Always missing
        expect(missingFields, isNot(contains('email'))); // Has email
        expect(missingFields, isNot(contains('displayName'))); // Has name
      });
    });

    group('Mobile Auth Users', () {
      test('complete Supabase profile should not show completion dialog', () {
        final mobileUserComplete = AppUser(
          id: 'mobile-user-complete',
          email: '<EMAIL>', // Added in Supabase
          displayName: 'Mobile User', // Added in Supabase
          phoneNumber: '+919876543210', // From mobile auth
          photoURL: 'https://example.com/custom-photo.jpg', // Custom uploaded photo
          isEmailVerified: false,
          authProvider: AuthProvider.phone,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(mobileUserComplete);
        
        // Should only have company logo missing
        expect(missingFields.length, equals(1));
        expect(missingFields, contains('companyLogo'));
        expect(missingFields, isNot(contains('email')));
        expect(missingFields, isNot(contains('displayName')));
        expect(missingFields, isNot(contains('phoneNumber')));
      });

      test('incomplete Supabase profile should show completion dialog for missing fields only', () {
        final mobileUserIncomplete = AppUser(
          id: 'mobile-user-incomplete',
          email: null, // Missing email in Supabase
          displayName: null, // Missing name in Supabase
          phoneNumber: '+919876543210', // Has phone from mobile auth
          photoURL: null, // No profile image
          isEmailVerified: false,
          authProvider: AuthProvider.phone,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(mobileUserIncomplete);
        
        // Should detect missing email, name, and profile image
        expect(missingFields, contains('profileImage')); // No image
        expect(missingFields, contains('email')); // Missing in Supabase
        expect(missingFields, contains('displayName')); // Missing in Supabase
        expect(missingFields, contains('companyLogo')); // Always missing
        expect(missingFields, isNot(contains('phoneNumber'))); // Has phone
      });
    });

    group('Email Auth Users', () {
      test('complete Supabase profile should not show completion dialog', () {
        final emailUserComplete = AppUser(
          id: 'email-user-complete',
          email: '<EMAIL>',
          displayName: 'Email User',
          phoneNumber: '+919876543210',
          photoURL: 'https://example.com/custom-photo.jpg',
          isEmailVerified: true,
          authProvider: AuthProvider.email,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(emailUserComplete);
        
        // Should only have company logo missing
        expect(missingFields.length, equals(1));
        expect(missingFields, contains('companyLogo'));
      });
    });

    group('Profile Image Detection', () {
      test('should detect Google default images', () {
        final userWithGoogleDefault = AppUser(
          id: 'test-user',
          email: '<EMAIL>',
          displayName: 'Test User',
          phoneNumber: '+919876543210',
          photoURL: 'https://lh3.googleusercontent.com/a/default-user',
          isEmailVerified: true,
          authProvider: AuthProvider.google,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(userWithGoogleDefault);
        expect(missingFields, contains('profileImage'));
      });

      test('should detect Facebook default images', () {
        final userWithFacebookDefault = AppUser(
          id: 'test-user',
          email: '<EMAIL>',
          displayName: 'Test User',
          phoneNumber: '+919876543210',
          photoURL: 'https://graph.facebook.com/default',
          isEmailVerified: true,
          authProvider: AuthProvider.email,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(userWithFacebookDefault);
        expect(missingFields, contains('profileImage'));
      });

      test('should accept custom uploaded images', () {
        final userWithCustomImage = AppUser(
          id: 'test-user',
          email: '<EMAIL>',
          displayName: 'Test User',
          phoneNumber: '+919876543210',
          photoURL: 'https://example.com/user-uploads/custom-photo.jpg',
          isEmailVerified: true,
          authProvider: AuthProvider.email,
          createdAt: DateTime.now(),
        );

        final missingFields = profileService.getMissingProfileFields(userWithCustomImage);
        expect(missingFields, isNot(contains('profileImage')));
      });
    });

    group('Profile Completion Logic', () {
      test('should show completion for users with missing critical fields', () async {
        final userWithMissingFields = AppUser(
          id: 'test-user',
          email: null,
          displayName: null,
          phoneNumber: null,
          photoURL: null,
          isEmailVerified: true,
          authProvider: AuthProvider.email,
          createdAt: DateTime.now(),
        );

        final shouldShow = await profileService.shouldShowProfileCompletion(userWithMissingFields);
        expect(shouldShow, isTrue);
      });

      test('should not show completion for users with complete profiles', () async {
        final userWithCompleteProfile = AppUser(
          id: 'test-user',
          email: '<EMAIL>',
          displayName: 'Test User',
          phoneNumber: '+919876543210',
          photoURL: 'https://example.com/custom-photo.jpg',
          isEmailVerified: true,
          authProvider: AuthProvider.email,
          createdAt: DateTime.now(),
        );

        final shouldShow = await profileService.shouldShowProfileCompletion(userWithCompleteProfile);
        expect(shouldShow, isFalse);
      });
    });
  });
}
