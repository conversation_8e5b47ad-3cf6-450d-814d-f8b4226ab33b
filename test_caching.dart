import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'lib/models/user_profile.dart';

void main() async {
  print('🧪 Testing Profile Caching Implementation...');
  
  // Initialize SharedPreferences
  SharedPreferences.setMockInitialValues({});
  final prefs = await SharedPreferences.getInstance();
  
  // Create test profile
  final testProfile = UserProfile(
    id: 'test-id',
    firebaseUid: 'test-firebase-uid',
    email: '<EMAIL>',
    displayName: 'Test User',
    syncVersion: 5,
  );
  
  print('✅ Created test profile: ${testProfile.displayName}');
  
  // Test caching
  try {
    final profileJson = jsonEncode(testProfile.toJson());
    await prefs.setString('cached_user_profile', profileJson);
    await prefs.setInt('cached_user_profile_timestamp', DateTime.now().millisecondsSinceEpoch);
    await prefs.setString('cached_user_profile_user_id', testProfile.firebaseUid);
    
    print('✅ Profile cached successfully');
    
    // Test retrieval
    final cachedJson = prefs.getString('cached_user_profile');
    if (cachedJson != null) {
      final cachedData = jsonDecode(cachedJson) as Map<String, dynamic>;
      final cachedProfile = UserProfile.fromJson(cachedData);
      
      print('✅ Profile retrieved from cache: ${cachedProfile.displayName}');
      print('✅ Sync version: ${cachedProfile.syncVersion}');
      
      // Test cache timestamp
      final timestamp = prefs.getInt('cached_user_profile_timestamp');
      if (timestamp != null) {
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        final age = DateTime.now().difference(cacheTime);
        print('✅ Cache age: ${age.inSeconds} seconds');
      }
      
      print('🎉 Caching implementation works correctly!');
    } else {
      print('❌ Failed to retrieve cached profile');
    }
  } catch (e) {
    print('❌ Error testing cache: $e');
  }
}
