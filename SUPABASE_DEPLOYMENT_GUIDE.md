# Supabase Database Deployment Guide

## 🎯 **OBJECTIVE**

Deploy the following changes to your live Supabase database:
1. Remove default role assignment (role_id = NULL for new users)
2. Change subscription plan from "basic" to "Free"
3. Make native_language field nullable with no default value
4. **FIX: last_sign_in_at tracking issue** (NEW - addresses your reported issue)

---

## 🚀 **DEPLOYMENT METHODS**

### **METHOD 1: Supabase Dashboard (Recommended)**

#### **Step 1: Access SQL Editor**
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Click **"New query"**

#### **Step 2: Run the Migration Scripts (IN ORDER)**

**First, run the comprehensive updates:**
1. Copy the entire content from `supabase/migrations/20240803000003_comprehensive_profile_updates.sql`
2. Paste it into the SQL Editor
3. Click **"Run"** button
4. Wait for execution to complete
5. Check for any error messages in the output

**Then, run the last_sign_in_at fix:**
1. Copy the entire content from `supabase/migrations/20240803000004_fix_last_signin_tracking.sql`
2. Paste it into a new SQL Editor tab
3. Click **"Run"** button
4. Wait for execution to complete
5. Check for success messages in the output

#### **Step 3: Verify Changes**
Run this verification query:
```sql
-- Verify the changes
SELECT 
    'Schema Check' as check_type,
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN ('role_id', 'subscription_plan', 'native_language');

-- Check existing data
SELECT 
    'Data Check' as check_type,
    COUNT(*) as total_users,
    COUNT(role_id) as users_with_roles,
    COUNT(*) - COUNT(role_id) as users_without_roles
FROM profiles;

-- Check subscription plans
SELECT 
    subscription_plan,
    COUNT(*) as user_count
FROM profiles 
GROUP BY subscription_plan;
```

---

### **METHOD 2: Supabase CLI (Alternative)**

#### **Prerequisites:**
```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref YOUR_PROJECT_REF
```

#### **Deployment Steps:**
```bash
# Navigate to your project directory
cd /path/to/your/project

# Run the migration
supabase db push

# Or apply specific migration file
supabase migration up
```

---

## 🔍 **VERIFICATION CHECKLIST**

### **After Deployment, Verify:**

#### **1. Schema Changes Applied:**
```sql
-- Check column defaults
SELECT 
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN ('role_id', 'subscription_plan', 'native_language');

-- Expected Results:
-- role_id: default=NULL, nullable=YES
-- subscription_plan: default='Free', nullable=YES  
-- native_language: default=NULL, nullable=YES
```

#### **2. Data Migration Successful:**
```sql
-- Check subscription plan migration
SELECT subscription_plan, COUNT(*) 
FROM profiles 
GROUP BY subscription_plan;

-- Should show 'Free' instead of 'basic'
```

#### **3. Functions Updated:**
```sql
-- Test profile creation function
SELECT public.create_user_profile(
    'test-uid-verification',
    '<EMAIL>',
    'Test User',
    NULL,
    NULL,
    'email',
    TRUE
);

-- Check the created profile
SELECT role_id, subscription_plan, native_language
FROM profiles 
WHERE firebase_uid = 'test-uid-verification';

-- Expected: role_id=NULL, subscription_plan='Free', native_language=NULL

-- Clean up test data
DELETE FROM profiles WHERE firebase_uid = 'test-uid-verification';
```

---

## 🧪 **TEST NEW USER CREATION**

### **Test Script:**
```sql
-- Create a test user to verify new behavior
SELECT public.create_user_profile(
    'test-new-user-' || extract(epoch from now())::text,
    '<EMAIL>',
    'New Test User',
    '+1234567890',
    'https://example.com/photo.jpg',
    'google',
    TRUE
);

-- Check the latest created profile
SELECT 
    firebase_uid,
    role_id,
    subscription_plan,
    native_language,
    created_at
FROM profiles 
ORDER BY created_at DESC 
LIMIT 1;

-- Expected Results:
-- role_id: NULL
-- subscription_plan: 'Free'
-- native_language: NULL
-- last_sign_in_at: NOT NULL (should have a timestamp)
```

---

## ⚠️ **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Permission Errors:**
```sql
-- If you get permission errors, ensure you're running as service_role
-- In Supabase dashboard, make sure you're using the service_role key
```

#### **2. Constraint Violations:**
```sql
-- If CHECK constraint fails, first check existing data
SELECT DISTINCT subscription_plan FROM profiles;

-- Then update any invalid values before applying constraints
```

#### **3. Function Not Found Errors:**
```sql
-- If functions don't exist, create them first
-- Copy the function definitions from the migration script
```

### **Rollback Plan (If Needed):**
```sql
-- Rollback subscription plan changes
UPDATE profiles SET subscription_plan = 'basic' WHERE subscription_plan = 'Free';
ALTER TABLE profiles ALTER COLUMN subscription_plan SET DEFAULT 'basic';

-- Rollback native_language changes  
ALTER TABLE profiles ALTER COLUMN native_language SET DEFAULT 'Hindi';

-- Note: Role assignments cannot be easily rolled back
-- You would need to manually assign roles to users
```

---

## 📋 **POST-DEPLOYMENT TASKS**

### **1. Update Flutter App:**
- Deploy the updated Flutter app with new enum values
- Test user registration flow
- Verify subscription screen shows "Free" plan

### **2. Monitor New Signups:**
```sql
-- Monitor new user creation for the next few days
SELECT 
    firebase_uid,
    role_id,
    subscription_plan,
    native_language,
    created_at
FROM profiles 
WHERE created_at > NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;
```

### **3. Admin Interface Updates:**
- Update admin interface to handle NULL role assignments
- Create role assignment workflow for new users
- Update user management screens

---

## ✅ **SUCCESS CRITERIA**

**Deployment is successful when:**
- ✅ New users have `role_id = NULL`
- ✅ New users have `subscription_plan = 'Free'`
- ✅ New users have `native_language = NULL`
- ✅ **New users have `last_sign_in_at` with proper timestamp (NOT NULL)**
- ✅ **Existing users get updated `last_sign_in_at` on login**
- ✅ Existing "basic" users are migrated to "Free"
- ✅ All database functions work correctly
- ✅ No constraint violations or errors
- ✅ Flutter app works with new schema

**Ready to deploy? Follow Method 1 (Supabase Dashboard) for the safest approach!** 🚀
